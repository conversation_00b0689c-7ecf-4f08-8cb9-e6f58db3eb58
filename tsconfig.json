{
    "compilerOptions": {
        "removeComments": true,
        "target": "ES2020",
        "useDefineForClassFields": true,
        "module": "ESNext",
        "lib": [
            "ES2020",
            "DOM",
            "DOM.Iterable"
        ],
        "skipLibCheck": true,
        /* Bundler mode */
        "moduleResolution": "bundler",
        "allowImportingTsExtensions": true,
        "resolveJsonModule": true,
        "isolatedModules": true,
        "noEmit": true,
        "allowSyntheticDefaultImports": true,
        "esModuleInterop": true,
        "jsx": "react-jsx",
        /* Linting */
        "strict": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noFallthroughCasesInSwitch": true,
        "paths": {
            "three/examples/jsm/*": [
                "./node_modules/three/examples/jsm/*"
            ]
        },
        "types": [
            "@tauri-apps/api"
        ]
    },
    "include": [
        "src",
        "src/script/apply/*.d.ts"
    ],
    "references": [
        {
            "path": "./tsconfig.node.json"
        }
    ]
}