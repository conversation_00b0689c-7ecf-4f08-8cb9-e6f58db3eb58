import { defineConfig } from "vite";
import { resolve } from 'path';
import fs from 'fs';
import path from 'path';

// @ts-ignore process is a nodejs global
const host = process.env.TAURI_DEV_HOST;

// 获取根目录中的所有HTML文件
const rootHtmlFiles = fs.readdirSync(__dirname)
    .filter(file => file.endsWith('.html'))
    .reduce((acc, file) => {
        // 用文件名去除.html作为key
        const key = path.basename(file, '.html');
        acc[ key ] = resolve(__dirname, file);
        return acc;
    }, {});

// 创建入口点配置
const createEntrypoints = () => {
    const entries = { ...rootHtmlFiles };

    // 为每个HTML文件添加对应的JS入口点

    if (entries[ 'search_color' ]) {
        entries[ 'search_color_js' ] = resolve(__dirname, 'src/script/random_color_theme/search_color_window.ts');
    }

    return entries;
};

// https://vitejs.dev/config/
export default defineConfig(async () => ({
    // Vite选项针对Tauri开发定制，仅应用于`tauri dev`或`tauri build`
    //
    // 1. 防止 Vite 遮蔽 Rust 错误
    clearScreen: false,
    // 2. tauri 预期一个固定端口，如果该端口不可用则失败
    server: {
        port: 3000,
        strictPort: true,
        host: host || false,
        headers: {
            'Content-Type': 'text/html; charset=utf-8'
        },
        hmr: host
            ? {
                protocol: "ws",
                host,
                port: 3000,
            }
            : undefined,
        watch: {
            // 3. 告诉 Vite 忽略监视 `src-tauri`
            ignored: [ "**/src-tauri/**" ],
        },
    },

    // 添加资源处理配置
    resolve: {
        alias: {
            // 确保能正确解析资源路径
            '/src/assets/': resolve(__dirname, 'src/assets/')
        }
    },

    // 静态资源处理配置
    build: {
        // 确保资源输出到正确位置
        assetsDir: 'assets',
        // 禁用资源内联 - 确保所有资源都是独立文件
        assetsInlineLimit: 0,
        // 设置为false可以更清晰地查看处理过程
        minify: false,
        // 确保sourcemap可用于调试
        sourcemap: true,
        rollupOptions: {
            input: createEntrypoints(),
            output: {
                // 确保资源名称保持不变
                assetFileNames: (assetInfo) => {
                    // 如果是SVG文件，保留原始文件名
                    if (assetInfo.name.endsWith('.svg')) {
                        return 'assets/[name][extname]';
                    }
                    // 其他资源使用hash
                    return 'assets/[name]-[hash][extname]';
                }
            }
        }
    }
}));