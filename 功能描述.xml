<root>
    <!-- 线性进度 -->
    <Group>
        <!-- 创建一个名为linear_progress_bar的函数，实现将rootElement 中的 LinearProgressBar 标签根据对应参数修改为进度条标签，rootElement 是 XML 文档中的根元素节点，类型为 Element。 具体转换规则为 将 -->
        <LinearProgressBar x="0" y="0" w="1080" h="100" progress="#ani" fill="#ff0000,#00ff00" stroke="#ff0000ff,#ff00ff00" weight="0,4" alpha="255*0.5,255" strokeAlign="inner" padding="0" cornerRadius="10,10" direction="toRight">
            <!-- FillShaders 填充着色；StrokeShaders 描边着色 -->
            <FillShaders tag="0">
                <!-- 线性渐变 -->
                <LinearGradient x="#screen_width" y="0" x1="0" y1="#screen_height" tile="clamp">
                    <GradientStop color="#050B2A" position="0" />
                    <GradientStop color="#81425D" position="0.5" />
                    <GradientStop color="#C87960" position="1" />
                </LinearGradient>
                <!-- 放射渐变 -->
                <RadialGradient x="" y="" rX="" rY="" tile="">
                    <GradientStop color="" position="" />
                    <GradientStop color="" position="" />
                </RadialGradient>
                <!-- 扫描渐变 -->
                <SweepGradient x="" y="" rotation="" tile="">
                    <GradientStop color="" position="" />
                    <GradientStop color="" position="" />
                </SweepGradient>
            </FillShaders>
            <StrokeShaders tag="0">
    </StrokeShaders>
            <!-- 其他标签... -->
        </LinearProgressBar>
        <!-- 修改后的Rectangle标签继承LinearProgressBar中除progress、fill、stroke、weight、alpha、padding、direction、fillColor、strokeColor、cornerRadius、cap、startAngle、sweep、close、dash、strokeAlign之外的属性 -->
        <!-- 如果LinearProgressBar标签内有FillShaders和StrokeShaders标签，根据FillShaders和StrokeShaders中的tag属性，来决定将FillShaders和StrokeShaders添加到哪个Rectangle标签中，tag=0时，添加到进度条背景中，tag=1时，添加到进度条中 -->

        <!-- LinearProgressBar.direction = toRight 时，修改为从左向右进度条 -->
        <FolmeState name="progress_随机不重复字符串" w="max((#LinearProgressBar.w - #LinearProgressBar.padding * 2) * #LinearProgressBar.progress,0.001)" />
        <FolmeConfig name="config_随机不重复字符串" ease="-2,1,0.5" onBegin="'fra120_随机不重复字符串'" onUpdate="'fra120_随机不重复字符串'" onEnd="'fra120_随机不重复字符串'" />
        <Function name="fra120_随机不重复字符串">
            <AnimationCommand target="fra" command="play" />
        </Function>
        <!-- 如果已经有name=fra的FramerateController，则不添加FramerateController -->
        <FramerateController name="fra" initPause="true" loop="false">
            <ControlPoint frameRate="120" time="0" />
            <ControlPoint frameRate="120" time="1000" />
            <ControlPoint frameRate="0" time="1500" />
        </FramerateController>
        <!-- 更新进度 -->
        <Var name="随机不重复name" expression="#LinearProgressBar.progress" type="string">
            <Trigger>
                <FolmeCommand target="progressGroup_随机不重复字符串" states="'progress_随机不重复字符串'" command="to" config="'config_随机不重复字符串'" />
            </Trigger>
        </Var>
        <Rectangle x="#LinearProgressBar.x" y="#LinearProgressBar.y" w="#LinearProgressBar.w" h="#LinearProgressBar.h" fillColor="@LinearProgressBar.fill[0]" strokeColor="@LinearProgressBar.stroke[0]" alpha="255" cornerRadiusExp="#LinearProgressBar.cornerRadius[0]" weight="#LinearProgressBar.weight[0]" strokeAlign="inner" />
        <Group name="progressGroup_随机不重复字符串" x="#LinearProgressBar.x+#LinearProgressBar.padding" y="#LinearProgressBar.y+#LinearProgressBar.padding" w="0.001" h="#LinearProgressBar.h-#LinearProgressBar.padding*2" clip="true">
            <!-- cornerRadius中有两个参数时 -->
            <Rectangle w="#LinearProgressBar.w-#LinearProgressBar.padding*2" h="#LinearProgressBar.h-#LinearProgressBar.padding*2" fillColor="@LinearProgressBar.fill[1]" strokeColor="@LinearProgressBar.stroke[1]" alpha="255" cornerRadiusExp="#LinearProgressBar.cornerRadius[1]" weight="#LinearProgressBar.weight[1]" strokeAlign="inner" />
            <!-- cornerRadius中只有一个参数时 -->
            <Rectangle w="#LinearProgressBar.w-#LinearProgressBar.padding*2" h="#LinearProgressBar.h-#LinearProgressBar.padding*2" fillColor="@LinearProgressBar.fill[1]" strokeColor="@LinearProgressBar.stroke[1]" alpha="255" cornerRadiusExp="max(#LinearProgressBar.cornerRadius[0] - #LinearProgressBar.padding,(#LinearProgressBar.h-#LinearProgressBar.padding*2)/8)" weight="#LinearProgressBar.weight[1]" strokeAlign="inner" />
        </Group>

        <!-- LinearProgressBar.direction = toLeft 时，修改为从右向左进度条 -->
        <FolmeState name="progress_随机不重复字符串" w="max((#LinearProgressBar.w - #LinearProgressBar.padding * 2) * #LinearProgressBar.progress,0.001)" />
        <FolmeConfig name="config_随机不重复字符串" ease="-2,1,0.5" onBegin="'fra120_随机不重复字符串'" onUpdate="'fra120_随机不重复字符串'" onEnd="'fra120_随机不重复字符串'" />
        <Function name="fra120_随机不重复字符串">
            <AnimationCommand target="fra" command="play" />
        </Function>
        <!-- 如果已经有name=fra的FramerateController，则不添加FramerateController -->
        <FramerateController name="fra" initPause="true" loop="false">
            <ControlPoint frameRate="120" time="0" />
            <ControlPoint frameRate="120" time="1000" />
            <ControlPoint frameRate="0" time="1500" />
        </FramerateController>
        <!-- 更新进度 -->
        <Var name="随机不重复name" expression="#LinearProgressBar.progress" type="string">
            <Trigger>
                <FolmeCommand target="progressGroup_随机不重复字符串" states="'progress_随机不重复字符串'" command="to" config="'config_随机不重复字符串'" />
            </Trigger>
        </Var>
        <Rectangle x="#LinearProgressBar.x" y="#LinearProgressBar.y" w="#LinearProgressBar.w" h="#LinearProgressBar.h" fillColor="@LinearProgressBar.fill[0]" strokeColor="@LinearProgressBar.stroke[0]" alpha="255" cornerRadiusExp="#LinearProgressBar.cornerRadius[0]" weight="#LinearProgressBar.weight[0]" strokeAlign="inner" />
        <Group name="progressGroup_随机不重复字符串" x="#LinearProgressBar.x + #LinearProgressBar.w - #LinearProgressBar.padding" y="#LinearProgressBar.y+#LinearProgressBar.padding" w="0.001" h="#LinearProgressBar.h-#LinearProgressBar.padding*2" clip="true" scaleX="-1">
            <!-- cornerRadius中有两个参数时 -->
            <Rectangle w="#LinearProgressBar.w-#LinearProgressBar.padding*2" h="#LinearProgressBar.h-#LinearProgressBar.padding*2" fillColor="@LinearProgressBar.fill[1]" strokeColor="@LinearProgressBar.stroke[1]" alpha="255" cornerRadiusExp="#LinearProgressBar.cornerRadius[1]" weight="#LinearProgressBar.weight[1]" strokeAlign="inner" />
            <!-- cornerRadius中只有一个参数时 -->
            <Rectangle x="0" w="#LinearProgressBar.w-#LinearProgressBar.padding*2" h="#LinearProgressBar.h-#LinearProgressBar.padding*2" fillColor="@LinearProgressBar.fill[1]" strokeColor="@LinearProgressBar.stroke[1]" alpha="255" cornerRadiusExp="max(#LinearProgressBar.cornerRadius[0] - #LinearProgressBar.padding,(#LinearProgressBar.h-#LinearProgressBar.padding*2)/8)" weight="#LinearProgressBar.weight[1]" strokeAlign="inner" />
        </Group>

        <!-- LinearProgressBar.direction = toDown 时，修改为从上向下进度条 -->
        <FolmeState name="progress_随机不重复字符串" h="max((#LinearProgressBar.h - #LinearProgressBar.padding * 2) * #LinearProgressBar.progress,0.001)" />
        <FolmeConfig name="config_随机不重复字符串" ease="-2,1,0.5" onBegin="'fra120_随机不重复字符串'" onUpdate="'fra120_随机不重复字符串'" onEnd="'fra120_随机不重复字符串'" />
        <Function name="fra120_随机不重复字符串">
            <AnimationCommand target="fra" command="play" />
        </Function>
        <!-- 如果已经有name=fra的FramerateController，则不添加FramerateController -->
        <FramerateController name="fra" initPause="true" loop="false">
            <ControlPoint frameRate="120" time="0" />
            <ControlPoint frameRate="120" time="1000" />
            <ControlPoint frameRate="0" time="1500" />
        </FramerateController>
        <!-- 更新进度 -->
        <Var name="随机不重复name" expression="#LinearProgressBar.progress" type="string">
            <Trigger>
                <FolmeCommand target="progressGroup_随机不重复字符串" states="'progress_随机不重复字符串'" command="to" config="'config_随机不重复字符串'" />
            </Trigger>
        </Var>
        <Rectangle x="#LinearProgressBar.x" y="#LinearProgressBar.y" w="#LinearProgressBar.w" h="#LinearProgressBar.h" fillColor="@LinearProgressBar.fill[0]" strokeColor="@LinearProgressBar.stroke[0]" alpha="255" cornerRadiusExp="#LinearProgressBar.cornerRadius[0]" weight="#LinearProgressBar.weight[0]" strokeAlign="inner" />
        <Group name="progressGroup_随机不重复字符串" x="#LinearProgressBar.x + #LinearProgressBar.padding" y="#LinearProgressBar.y+#LinearProgressBar.padding" w="#LinearProgressBar.w-#LinearProgressBar.padding*2" h="0.001" clip="true">
            <!-- cornerRadius中有两个参数时 -->
            <Rectangle w="#LinearProgressBar.w-#LinearProgressBar.padding*2" h="#LinearProgressBar.h-#LinearProgressBar.padding*2" fillColor="@LinearProgressBar.fill[1]" strokeColor="@LinearProgressBar.stroke[1]" alpha="255" cornerRadiusExp="#LinearProgressBar.cornerRadius[1]" weight="#LinearProgressBar.weight[1]" strokeAlign="inner" />
            <!-- cornerRadius中只有一个参数时 -->
            <Rectangle x="0" w="#LinearProgressBar.w-#LinearProgressBar.padding*2" h="#LinearProgressBar.h-#LinearProgressBar.padding*2" fillColor="@LinearProgressBar.fill[1]" strokeColor="@LinearProgressBar.stroke[1]" alpha="255" cornerRadiusExp="max(#LinearProgressBar.cornerRadius[0] - #LinearProgressBar.padding,(#LinearProgressBar.w-#LinearProgressBar.padding*2)/8)" weight="#LinearProgressBar.weight[1]" strokeAlign="inner" />
        </Group>

        <!-- LinearProgressBar.direction = toUp 时，修改为从下向上进度条 -->
        <FolmeState name="progress_随机不重复字符串" h="max((#LinearProgressBar.h - #LinearProgressBar.padding * 2) * #LinearProgressBar.progress,0.001)" />
        <FolmeConfig name="config_随机不重复字符串" ease="-2,1,0.5" onBegin="'fra120_随机不重复字符串'" onUpdate="'fra120_随机不重复字符串'" onEnd="'fra120_随机不重复字符串'" />
        <Function name="fra120_随机不重复字符串">
            <AnimationCommand target="fra" command="play" />
        </Function>
        <!-- 如果已经有name=fra的FramerateController，则不添加FramerateController -->
        <FramerateController name="fra" initPause="true" loop="false">
            <ControlPoint frameRate="120" time="0" />
            <ControlPoint frameRate="120" time="1000" />
            <ControlPoint frameRate="0" time="1500" />
        </FramerateController>
        <!-- 更新进度 -->
        <Var name="随机不重复name" expression="#LinearProgressBar.progress" type="string">
            <Trigger>
                <FolmeCommand target="progressGroup_随机不重复字符串" states="'progress_随机不重复字符串'" command="to" config="'config_随机不重复字符串'" />
            </Trigger>
        </Var>
        <Rectangle x="#LinearProgressBar.x" y="#LinearProgressBar.y" w="#LinearProgressBar.w" h="#LinearProgressBar.h" fillColor="@LinearProgressBar.fill[0]" strokeColor="@LinearProgressBar.stroke[0]" alpha="255" cornerRadiusExp="#LinearProgressBar.cornerRadius[0]" weight="#LinearProgressBar.weight[0]" strokeAlign="inner" />
        <Group name="progressGroup_随机不重复字符串" x="#LinearProgressBar.x + #LinearProgressBar.padding" y="#LinearProgressBar.y + #LinearProgressBar.h - #LinearProgressBar.padding" w="#LinearProgressBar.w-#LinearProgressBar.padding*2" h="0.001" clip="true" scaleY="-1">
            <!-- cornerRadius中有两个参数时 -->
            <Rectangle w="#LinearProgressBar.w-#LinearProgressBar.padding*2" h="#LinearProgressBar.h-#LinearProgressBar.padding*2" fillColor="@LinearProgressBar.fill[1]" strokeColor="@LinearProgressBar.stroke[1]" alpha="255" cornerRadiusExp="#LinearProgressBar.cornerRadius[1]" weight="#LinearProgressBar.weight[1]" strokeAlign="inner" />
            <!-- cornerRadius中只有一个参数时 -->
            <Rectangle x="0" w="#LinearProgressBar.w-#LinearProgressBar.padding*2" h="#LinearProgressBar.h-#LinearProgressBar.padding*2" fillColor="@LinearProgressBar.fill[1]" strokeColor="@LinearProgressBar.stroke[1]" alpha="255" cornerRadiusExp="max(#LinearProgressBar.cornerRadius[0] - #LinearProgressBar.padding,(#LinearProgressBar.w-#LinearProgressBar.padding*2)/8)" weight="#LinearProgressBar.weight[1]" strokeAlign="inner" />
        </Group>

    </Group>

    <!-- 删除未使用变量 -->
    <Group>
        <!-- 删除所有未使用的变量标签'var', 'variablecommand', 'permanence', 'permanencecommand', 'Variable' -->
        <Var name="x1" expression="" type="number" const="false" />
        <Var name="x2" expression="" type="xxx[]" const="false" />
        <Variable name="x3" expression="" type="number" const="false" />
        <Permanence name="x4" expression="" type="number" const="false" />
        <PermanenceCommand name="x5" expression="" type="number" const="false" />
        <VariableCommand name="x6" expression="" type="number" const="false" />
        <VariableCommand name="x7" expression="" type="number[]" const="false" />
        <!-- 以上标签的name属性值为变量名，没有被#或@或$引用的变量名，则可以删除该标签。如果变量名被其他标签引用，则不能删除。 注意可能或存在多个变量名相同的情况，例如： -->
        <VariableCommand name="x1" expression="1" type="number" const="false" />
        <VariableCommand name="x1" expression="2" type="number" const="false" />
        <VariableCommand name="x1" expression="3" type="number" const="false" />
        <!-- 以上情况，如果x1没有被其他标签引用，则可以删除所有name为x1的标签。 变量名可能存在数组索引，例如： -->
        <Var name="x1" type="number[]" const="true" expression="" values="1,2,3" />
        <Var name="x1" type="string[]" const="true" expression="" values="1,2,3" />
        <VariableCommand name="x1" expression="2" type="number[]" index="#i" const="false" />
        <VariableCommand name="x1" expression="'3'" type="string[]" index="#i" const="false" />
        <!-- 以上数组变量没有被#x1[索引]或@x1[索引]或$x1[索引]引用，则可以删除所有name为x1的标签。 [索引]为数组索引，可能为数字，也可能是变量或表达式。 变量存在循环依赖问题，例如： -->
        <Var name="x1" type="number" expression="1" />
        <Var name="x2" type="number" expression="#x1" />
        <Var name="x3" type="number" expression="#x2" />
        <!-- 以上情况，如果x3没有被使用，则需要删除name为x3，x2,x1的标签。 还有以下几种情况，在检测是否被引用时应当考虑 变量可能会被混合引用例如 -->
        <Var name="combined" expression="#var1 + @var2 + $var3" type="string" />
        <!-- 函数参数中的变量引用 -->
        <Var name="result" expression="max(#value1, #value2)" type="number" />
        <!-- 字符串模板中嵌入变量 -->
        <Var name="message" expression="'用户'+@username+'的余额为'+#balance" type="string" />
        <!-- 表达式中可能包含多个变量引用 -->
        <Var name="total" expression="#price * #quantity + #tax" type="number" />
        <!-- 表达式中可能包含条件判断 -->
        <Var name="discount" expression="#total }= 1000 #total * 0.9 : #total" type="number" />
        <!-- 以上各种情况中的变量引用要能被正确识别，然后进行删除没有被引用的变量标签。 -->

    </Group>


    <!-- 圆形进度 -->
    <Group>
        <!-- 创建一个名为arc_progress_bar的函数，实现将rootElement 中的 ArcProgressBar 标签根据对应参数修改为进度条标签，rootElement 是 XML 文档中的根元素节点，类型为 Element。 具体转换规则为 将 -->
        <ArcProgressBar x="#view_width/2" y="#view_height/2" progress="#ani" radius="200" startAngle="-90" sweep="360" close="false" strokeColor="#ffffff,#ff0000" padding="5" weight="20,20" cap="round" alpha="255,255">
            <!-- FillShaders 填充着色；StrokeShaders 描边着色 -->
            <FillShaders tag="0">
                <!-- 线性渐变 -->
                <LinearGradient x="#screen_width" y="0" x1="0" y1="#screen_height" tile="clamp">
                    <GradientStop color="#050B2A" position="0" />
                    <GradientStop color="#81425D" position="0.5" />
                    <GradientStop color="#C87960" position="1" />
                </LinearGradient>
                <!-- 放射渐变 -->
                <RadialGradient x="" y="" rX="" rY="" tile="">
                    <GradientStop color="" position="" />
                    <GradientStop color="" position="" />
                </RadialGradient>
                <!-- 扫描渐变 -->
                <SweepGradient x="" y="" rotation="" tile="">
                    <GradientStop color="" position="" />
                    <GradientStop color="" position="" />
                </SweepGradient>
            </FillShaders>
            <StrokeShaders tag="0" />
        </ArcProgressBar>
        <!-- 转换为 -->
        <VirtualElement name="ArcProgressBar_progress_随机不重复字符串" float1="0" folmeMode="true" />
        <FolmeState name="progress_随机不重复字符串" float1="ArcProgressBar.sweep * ArcProgressBar.progress" />
        <FolmeConfig name="config_随机不重复字符串" ease="-2,1,0.5" onBegin="'fra120_随机不重复字符串'" onUpdate="'fra120_随机不重复字符串'" onEnd="'fra120_随机不重复字符串'" />
        <Function name="fra120_随机不重复字符串">
            <AnimationCommand target="fra" command="play" />
        </Function>
        <!-- 如果已经有name=fra的FramerateController，则不添加FramerateController -->
        <FramerateController name="fra" initPause="true" loop="false">
            <ControlPoint frameRate="120" time="0" />
            <ControlPoint frameRate="120" time="1000" />
            <ControlPoint frameRate="0" time="1500" />
        </FramerateController>
        <!-- 更新进度 -->
        <Var name="随机不重复name" expression="ArcProgressBar.progress" type="string">
            <Trigger>
                <FolmeCommand target="ArcProgressBar_progress_随机不重复字符串" states="'progress_随机不重复字符串'" command="to" config="'config_随机不重复字符串'" />
            </Trigger>
        </Var>

        <!-- 背景层 -->
        <Arc x="ArcProgressBar.x" y="ArcProgressBar.y" w="ArcProgressBar.radius" h="ArcProgressBar.radius" startAngle="ArcProgressBar.startAngle" sweep="ArcProgressBar.sweep" close="false" strokeColor="ArcProgressBar.strokeColor[1]" weight="ArcProgressBar.weight[1]" cap="ArcProgressBar.cap" alpha="ArcProgressBar.alpha[1]">
            <!-- 继承tag=0的FillShaders和StrokeShaders -->
            <FillShaders />
            <StrokeShaders />
        </Arc>
        <!-- 进度层 -->
        <Arc x="ArcProgressBar.x" y="ArcProgressBar.y" w="ArcProgressBar.radius" h="ArcProgressBar.radius" startAngle="ArcProgressBar.startAngle" sweep="#ArcProgressBar_progress_随机不重复字符串.float1" close="false" strokeColor="ArcProgressBar.strokeColor[2]" weight="(ArcProgressBar.weight[2] - ArcProgressBar.padding * 2).value" cap="ArcProgressBar.cap" alpha="ArcProgressBar.alpha[2]">
            <!-- 继承tag=1的FillShaders和StrokeShaders -->
            <FillShaders />
            <StrokeShaders />
        </Arc>

    </Group>



</root>