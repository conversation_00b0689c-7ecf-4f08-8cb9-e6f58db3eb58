// 资源同步脚本 - 确保在不同环境下资源文件可用
// 将src/assets目录中的资源复制到public/assets和dist/assets目录

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取项目根目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');
const srcAssetsDir = path.join(rootDir, 'src', 'assets');
const publicAssetsDir = path.join(rootDir, 'public', 'assets');
const distAssetsDir = path.join(rootDir, 'dist', 'assets');

/**
 * 复制文件夹内容到目标文件夹
 * @param {string} source 源文件夹路径
 * @param {string} target 目标文件夹路径
 */
function copyDir(source, target) {
    // 确保目标目录存在
    if (!fs.existsSync(target)) {
        console.log(`创建目标目录: ${target}`);
        fs.mkdirSync(target, { recursive: true });
    }

    // 读取源目录内容
    try {
        const items = fs.readdirSync(source);

        // 遍历所有文件和子目录
        for (const item of items) {
            const sourcePath = path.join(source, item);
            const targetPath = path.join(target, item);

            // 检查是目录还是文件
            const stat = fs.statSync(sourcePath);

            if (stat.isDirectory()) {
                // 递归复制子目录
                copyDir(sourcePath, targetPath);
            } else {
                // 复制文件
                try {
                    // 检查目标文件是否存在及是否需要更新
                    let needCopy = true;

                    if (fs.existsSync(targetPath)) {
                        const sourceModTime = stat.mtimeMs;
                        const targetStat = fs.statSync(targetPath);
                        const targetModTime = targetStat.mtimeMs;

                        // 只有当源文件比目标文件新时才复制
                        if (sourceModTime <= targetModTime) {
                            needCopy = false;
                            console.log(`跳过未修改文件: ${item}`);
                        }
                    }

                    if (needCopy) {
                        fs.copyFileSync(sourcePath, targetPath);
                        console.log(`复制文件: ${sourcePath} -> ${targetPath}`);
                    }
                } catch (fileErr) {
                    console.error(`复制文件失败: ${sourcePath} -> ${targetPath}`, fileErr);
                }
            }
        }

        console.log(`完成资源目录同步: ${source} -> ${target}`);
    } catch (err) {
        console.error(`读取源目录失败: ${source}`, err);
    }
}

// 执行复制操作
try {
    console.log('开始同步资源文件...');
    console.log(`源目录: ${srcAssetsDir}`);

    // 复制到public/assets目录
    console.log(`目标目录1: ${publicAssetsDir}`);
    copyDir(srcAssetsDir, publicAssetsDir);

    // 如果dist目录存在，也复制到dist/assets目录
    if (fs.existsSync(path.join(rootDir, 'dist'))) {
        console.log(`目标目录2: ${distAssetsDir}`);
        copyDir(srcAssetsDir, distAssetsDir);
    } else {
        console.log('dist目录不存在，跳过复制到dist/assets');
    }

    console.log('资源文件同步完成!');
} catch (error) {
    console.error('资源文件同步过程中发生错误:', error);
    process.exit(1);
}