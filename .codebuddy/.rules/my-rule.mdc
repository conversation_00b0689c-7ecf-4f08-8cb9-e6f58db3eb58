---
type: "always_apply"
description: "Mini Edit Pro - Tauri 2 桌面应用开发规范"
---
# Mini Edit Pro 开发规范

## 🎯 项目概述
Mini Edit Pro 是基于 Tauri 2 + TypeScript 开发的小米主题制作工具，提供主题解包、颜色选择、9patch 处理、APK 操作等功能。

## 🔧 核心开发原则

### 语言与沟通规范
- **始终使用中文回复**
- 代码注释和日志必须为中文，其余代码保持英文
- 错误处理：代码运行出现错误时不得阻塞进程，应打印中文错误日志并继续执行后续流程

### 工作流程
1. **[模式：研究]** - 深入理解用户需求，收集相关信息
2. **[模式：构思]** - 提供至少两种可行方案及详细评估
- 格式：`方案1：[简要描述] - 优势：[...] - 劣势：[...]`
- 完成后使用 MCP 服务 `interactive-feedback` 请求用户决策

### MCP 服务使用
- **interactive_feedback**: 用于用户反馈征询
- **Context7**: 查询最新库文档和示例代码
- **优先级**: 优先使用 MCP 服务获取准确信息
- **反馈时机**: 研究/构思遇疑问时主动征询，任务完成前必须征询用户意见

## 🛠️ Tauri 2 技术规范

### API 使用规范
- `invoke` 调用方法来自 `@tauri-apps/api/core`
- 权限配置位于 `src-tauri/capabilities/default.json`
- 参考文档：[Tauri 权限文档](https://v2.tauri.org.cn/reference/acl/core-permissions)

### 包管理
- **包管理工具**: 使用 **yarn**（不使用 npm 或 pnpm）
- **依赖管理**: 使用包管理器命令，避免手动编辑配置文件
- **版本控制**: package.json 和 Cargo.toml 版本号必须保持同步

### 配置限制
- **严禁**在 `tauri.conf.json` 中配置：
```json
{"tauri": {"windows": [{"devtools": true}]}}
```
- **窗口配置**: 应用窗口固定为 220x420 像素，始终置顶，无边框透明

### 文件系统操作示例
```typescript
import { exists, BaseDirectory } from '@tauri-apps/plugin-fs';

// 检查 APPDATA 路径下是否存在文件
const fileExists = await exists('avatar.png', {
baseDir: BaseDirectory.AppData
})                                              ;
```

## 📁 文件处理规范

### 压缩/解压要求
- **路径结构保持**: 压缩文件时确保解压后路径结构不变
- 正确：目录 `a` → `a.zip` → 解压得到 `a/文件`
- 错误：目录 `a` → `a.zip` → 解压得到 `a/a/文件`
- **格式识别**: 解压未知格式文件前需根据实际格式补全后缀
- **跨平台兼容**: 路径处理需注意平台分隔符差异

### 性能优化策略
- **异步 IO**: 优先使用异步文件操作
- **并行处理**: 充分利用多核处理能力
- **多线程处理**:
- 确保线程安全
- 线程数根据 CPU 核心数自动配置
- **组合优化**: 同时使用多种加速方式
- **一致性保证**: 优化不得影响原有功能逻辑和结果一致性

## 💻 代码质量标准

### 核心代码原则
- **简约性 (Simplicity)**:
- 代码逻辑清晰直接，避免过度设计和不必要的复杂性
- 优先选择简单明了的实现方案，避免炫技式编程
- 删除冗余代码，保持代码库整洁
- **单个函数不超过 100 行**，复杂逻辑必须拆分
- **清晰性 (Clarity)**:
- 变量和函数命名具有明确语义，见名知意
- 代码结构层次分明，逻辑流程易于理解
- 关键业务逻辑必须添加中文注释说明
- **避免深层嵌套**，最大嵌套深度不超过 4 层
- **健壮性 (Robustness)**:
- 完善的错误处理机制，预防和处理异常情况
- 输入验证和边界条件检查
- 优雅降级，确保系统稳定性
- **异步操作必须有超时处理**

### TypeScript/JavaScript 规范
- **第三方包使用**: 必须使用 Context7 查阅官方文档，确保使用实际导出的方法
- **样式分离**: 禁止在 TS/JS 文件中直接书写 CSS 样式
- **样式命名**: 新增 CSS 样式时避免与现有样式类名冲突
- **代码组织**:
- 单一职责原则，每个函数只做一件事
- 合理的函数长度，避免超长函数
- 适当的代码分层和模块化
- **全局变量管理**:
- 避免过度使用全局变量，优先使用模块作用域
- 全局状态必须有明确的初始化和清理机制
- 使用 TypeScript 声明文件管理全局类型

### Rust 开发规范
- **编译检查**: 每次修改 Rust 代码后必须检查编译错误
- **错误处理**:
- 先通读整体代码，理解上下文后再修复
- 多次尝试无效时，使用 MCP 服务查询正确 API 使用方法
- 使用 `Result<T, E>` 类型进行错误传播
- 避免 `unwrap()` 和 `expect()`，优先使用模式匹配
- **所有 Tauri 命令必须返回 Result<T, String>**
- **代码理解**: 基于对现有功能的准确理解进行优化
- **内存安全**: 充分利用 Rust 的所有权系统，避免内存泄漏
- **性能优化**:
- 大文件操作使用内存映射 (memmap2)
- 并行处理使用 rayon 库
- 异步操作使用 tokio
- **日志规范**: 使用 log 宏记录中文日志，错误级别要准确

### 代码优化原则
- **功能完整性**: 所有优化必须在不影响功能完整性的前提下进行
- **性能提升**: 在保证结果一致性的基础上进行性能优化
- **代码重构**: 基于深入理解现有代码结构进行重构
- **可维护性**: 代码应易于理解、修改和扩展
- **测试覆盖**: 关键功能必须有相应的测试用例
- **向后兼容**: 修改现有 API 时必须保持向后兼容性

## 🚫 开发限制与约束

### 命令执行限制
- **禁止运行**: `npm run tauri dev`
- **禁止运行**: 其他 dev 或 build 命令（除非用户明确要求）
- **禁止操作**: 直接修改 package.json、Cargo.toml 等配置文件（使用包管理器）

### 代码质量约束
- **文件大小限制**: 单个 TypeScript 文件不超过 500 行
- **函数复杂度**: 单个函数不超过 100 行，复杂逻辑必须拆分
- **全局变量**: 严格控制全局变量使用，必须有清理机制
- **内存管理**: 及时清理事件监听器、定时器、DOM 元素

### 调试偏好
- **全面分析**: 调试问题时进行全面文件分析，而非创建分析报告
- **直接修复**: 优先提供直接的代码修复方案
- **避免片段**: 避免只读取文件的部分片段
- **性能监控**: 关注内存使用和性能瓶颈

## 📋 项目特定规范

### 颜色选择器模块
- 必须有完善的状态管理和清理机制
- 全局快捷键注册后必须正确注销
- 鼠标事件监听器必须及时移除
- 平台差异化处理（Windows/macOS）

### 文件处理模块
- 大文件操作必须使用流式处理
- 压缩/解压保持目录结构完整性
- 异步操作必须有进度反馈
- 错误处理不能中断整体流程

### UI 交互规范
- 所有操作必须有视觉反馈
- 长时间操作显示进度条
- 错误信息用户友好，技术细节记录日志
- 响应式设计适配固定窗口尺寸

## 🔧 大文件重构指南

### 重构前准备工作

#### 代码分析步骤
1. **功能模块识别**：
- 统计文件行数和函数数量
- 识别主要功能模块（状态管理、事件处理、UI渲染、数据处理）
- 标记纯函数和有副作用的函数
- 分析函数复杂度和调用频率

2. **依赖关系梳理**：
- 绘制函数调用关系图
- 识别全局变量的读写位置
- 记录模块间的数据流向
- 标记循环依赖和紧耦合部分
- 分析外部依赖和导入关系

3. **测试用例准备**：
- 记录关键函数的输入输出样例
- 准备边界条件和异常情况测试
- 建立性能基准测试
- 设计用户体验一致性验证

#### 以 color_picker.ts (2208行) 为例的分析结果
- **状态管理**：9个全局变量需要集中管理
- **平台配置**：平台检测和配置参数
- **数据库操作**：颜色数据的加载和查询
- **事件处理**：键盘、鼠标事件的注册和清理
- **UI渲染**：DOM创建、样式设置、动画效果
- **颜色提取**：Canvas操作和颜色计算
- **生命周期管理**：初始化、清理、重置功能

### 拆分策略与原则

#### 按功能职责拆分标准
1. **状态管理模块**：集中管理所有全局状态变量
2. **平台适配模块**：处理不同操作系统的差异化配置
3. **事件处理模块**：统一管理所有事件监听器的注册和清理
4. **UI渲染模块**：负责DOM操作和样式管理
5. **工具函数模块**：提取可复用的纯函数
6. **数据库模块**：处理数据的加载、缓存和查询
7. **主控制器模块**：协调各模块，提供统一API

#### 模块边界定义原则
- **单一职责**：每个模块只负责一个核心功能
- **低耦合**：模块间通过接口通信，避免直接访问内部状态
- **高内聚**：相关功能集中在同一模块内
- **依赖倒置**：高层模块不依赖低层模块，都依赖抽象

#### 接口设计原则
- 保持原有公共API不变，确保向后兼容
- 内部模块接口要简洁明确
- 使用TypeScript接口定义模块契约
- 提供统一的错误处理和清理机制

### 分步执行计划

#### 渐进式重构流程

**第一阶段：提取纯函数和工具类**
- 识别无副作用的工具函数
- 提取平台检测和配置相关代码
- 移动颜色处理和计算函数
- 创建独立的工具模块文件

**第二阶段：分离状态管理**
- 创建状态管理类，逐步迁移全局变量
- 在原文件中添加兼容层，确保功能不中断
- 提供状态访问和修改的统一接口
- 实现状态的初始化和清理机制

**第三阶段：重构主流程**
- 创建主控制器，整合各个模块
- 重新实现主要功能，使用模块化方式
- 保持原有导出接口不变
- 添加错误处理和异常恢复机制

#### 每个步骤的验证检查点
- **功能验证**：确保重构前后行为完全一致
- **性能验证**：检查执行时间不超过原版本的110%
- **内存验证**：确保无内存泄漏，多次执行后内存稳定
- **兼容性验证**：确保外部调用代码无需修改

#### 回滚机制
- **Git分支策略**：为每个重构阶段创建独立分支
- **功能开关**：在关键重构点添加开关，支持快速切换
- **备份保留**：保留原文件作为备份参考
- **监控告警**：设置性能和错误监控，及时发现问题

### 质量保证措施

#### 重构过程中的代码审查要点
- 每个新文件不超过500行
- 每个函数不超过100行
- 模块间依赖关系清晰，无循环依赖
- 全局变量已迁移到状态管理类
- 所有事件监听器有对应的清理机制
- 错误处理完善，异常情况有恢复机制

#### 性能影响评估方法
- 建立性能基准测试工具
- 设置性能阈值和告警机制
- 监控内存使用情况
- 测试并发和高频调用场景
- 验证不同平台的性能表现

#### 用户体验一致性检查标准
- 功能响应时间保持一致
- 交互行为完全相同
- 错误提示和处理方式不变
- 视觉效果和动画保持原样
- 快捷键和操作习惯不受影响

## 🔍 常见问题与解决方案

### 内存泄漏预防
- **事件监听器**: 使用 AbortController 或在组件销毁时手动移除
- **定时器**: 使用 clearTimeout/clearInterval 清理
- **全局状态**: 提供重置函数，避免状态残留
- **DOM 引用**: 及时清理不再使用的 DOM 元素引用

### 性能优化策略
- **文件操作**: 大文件使用流式处理，避免一次性加载到内存
- **并发控制**: 限制同时进行的异步操作数量
- **缓存机制**: 合理使用缓存，避免重复计算
- **懒加载**: 按需加载模块和资源

### 跨平台兼容性
- **路径处理**: 使用 Tauri 的 path API，避免硬编码路径分隔符
- **文件权限**: 考虑不同平台的文件权限差异
- **系统调用**: 使用 Tauri 插件而非直接系统调用
- **UI 适配**: 考虑不同平台的 UI 规范差异

## 📝 代码审查清单

### 重构专项检查
- [ ] 原有公共API保持不变
- [ ] 所有模块文件不超过300行
- [ ] 模块间无循环依赖
- [ ] 全局变量已正确迁移
- [ ] 事件监听器有清理机制
- [ ] 性能测试通过
- [ ] 内存泄漏测试通过
- [ ] 用户体验保持一致

### 提交前检查
- [ ] 代码编译无错误无警告
- [ ] 所有异步操作有错误处理
- [ ] 全局变量有清理机制
- [ ] 事件监听器正确注销
- [ ] 中文注释完整准确
- [ ] 函数长度符合规范
- [ ] 无硬编码路径和配置
- [ ] 性能敏感操作已优化
