1.   删除空属性

     ```xml
     
     ```

     

2.   删除未使用变量 

     ```xml
     
     ```

     

3.   生成数组范围

     ```xml
     <!-- 对于以下 <VariableCommand> 标签： -->
     <LoopCommand count="10" indexName="i">
       <VariableCommand name="items" expression="#i" index="#i" type="string[]" />
     </LoopCommand>
     <!-- 会生成以下 <Var> 标签： -->
     <Var name="items" size="10" type="string[]" const="true" />
     
     <!-- 对于没有明确范围的变量命令： -->
     <VariableCommand name="data" type="number[]" />
     <!-- 会生成以下 <Var> 标签： -->
     <Var name="data" size="99" type="number[]" const="true" />
     
     <!-- 对于明确范围的变量命令： -->
     <VariableCommand name="items" type="string[]" size="999" />
     <!-- 会生成以下 <Var> 标签： -->
     <Var name="items" size="999" type="string[]" const="true" />
     ```

     