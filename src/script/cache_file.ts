import { invoke } from "@tauri-apps/api/core";
/**
 * 执行缓存文件命令，将指定路径的文件缓存到应用本地的"缓存"目录中
 * @param sourcePath 需要缓存的文件夹路径
 * @returns 缓存文件路径
 */
export async function 缓存文件(sourcePath: string, timestamp?: number) {
    try {
        const result = await invoke<string>('缓存文件', {
            sourcePath: sourcePath,
            timestamp: timestamp
        });
        return result;
    } catch (error) {
        console.error('缓存文件失败:', error);
        return '';
    }
}
