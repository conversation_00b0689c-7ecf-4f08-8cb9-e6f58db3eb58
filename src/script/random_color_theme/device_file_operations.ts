/**
 * 设备检查和文件操作模块
 * 负责设备连接检查、剪贴板操作和文件处理
 */

import { join } from '@tauri-apps/api/path';
import { exists, readFile } from "@tauri-apps/plugin-fs";
import { writeImage } from '@tauri-apps/plugin-clipboard-manager';
import { invoke } from '@tauri-apps/api/core';
import { messageError, messageSuccess } from '../prompt_message';
import { getCurrentSelectedScreenshot, getPath } from './screenshot_manager';

/**
 * 智能设备检查和选择
 * @returns 如果设备可用返回true，否则返回false
 */
export async function checkAndSelectDevice(): Promise<boolean> {
    try {
        const { getDevices } = await import('../commands');
        const devices = await getDevices();

        // 过滤出在线设备
        const onlineDevices = devices.filter(device =>
            device.status === 'device'
        );

        if (onlineDevices.length === 0) {
            messageError('未检测到连接的设备，请确保设备已连接并启用USB调试');
            return false;
        }

        // 由于random_color_theme函数已经处理了设备选择，这里只需要验证设备可用性
        console.log(`检测到 ${onlineDevices.length} 个可用设备`);
        return true;

    } catch (error) {
        console.error('设备检查失败:', error);
        messageError(`设备检查失败: ${error}`);
        return false;
    }
}

/**
 * 使用shell命令复制图片到剪贴板（macOS）
 */
async function copyImageWithShellCommand(fullPath: string): Promise<boolean> {
    try {
        const startTime = performance.now();

        // 使用shellx插件执行osascript命令
        const result = await invoke('plugin:shellx|execute', {
            program: 'osascript',
            args: [
                '-e',
                `set the clipboard to (read (POSIX file "${fullPath}") as JPEG picture)`
            ],
            options: {}
        });

        const endTime = performance.now();
        console.log(`Shell命令复制耗时: ${(endTime - startTime).toFixed(2)}ms`);
        console.log('Shell命令执行结果:', result);

        return true;
    } catch (error) {
        console.error('Shell命令复制失败:', error);
        return false;
    }
}

/**
 * 复制选中的图片到剪贴板
 */
export async function copySelectedImageToClipboard(): Promise<void> {
    try {
        // 检查是否有选中的截图
        const currentSelectedScreenshot = getCurrentSelectedScreenshot();
        if (!currentSelectedScreenshot) {
            messageError('请先选择一张图片');
            return;
        }

        // 获取图片文件路径
        const fullPath = await join(getPath(), currentSelectedScreenshot);

        // 检查文件是否存在
        if (!await exists(fullPath)) {
            messageError('图片文件不存在');
            return;
        }

        // 首先尝试使用shell命令复制（推荐方法）
        const shellCopySuccess = await copyImageWithShellCommand(fullPath);
        if (shellCopySuccess) {
            messageSuccess(`图片 ${currentSelectedScreenshot} 已复制到剪贴板`);
            console.log('图片复制成功（Shell命令）:', currentSelectedScreenshot);
            return;
        }

        console.log('Shell命令复制失败，回退到标准方法');

        // 回退到标准方法
        const startTime = performance.now();

        // 直接读取图片文件字节
        const imageBytes = await readFile(fullPath);
        const readTime = performance.now();

        console.log(`文件大小: ${imageBytes.length} 字节, 读取耗时: ${(readTime - startTime).toFixed(2)}ms`);

        // 复制到剪贴板
        const writeStart = performance.now();
        await writeImage(imageBytes);
        const endTime = performance.now();

        // 输出性能信息
        console.log(`图片复制性能: 读取耗时 ${(readTime - startTime).toFixed(2)}ms, 写入耗时 ${(endTime - writeStart).toFixed(2)}ms, 总耗时 ${(endTime - startTime).toFixed(2)}ms`);

        messageSuccess(`图片 ${currentSelectedScreenshot} 已复制到剪贴板`);
        console.log('图片复制成功:', currentSelectedScreenshot);
    } catch (error) {
        console.error('复制图片到剪贴板失败:', error);
        messageError(`复制失败: ${error}`);
    }
}

/**
 * 设置键盘事件监听器处理图片复制
 */
export function setupImageCopyKeyboardHandler(): void {
    // 添加键盘事件监听器处理图片复制
    document.addEventListener('keydown', async (event: KeyboardEvent) => {
        // 检查是否按下了Ctrl+C (Windows/Linux) 或 Command+C (macOS)
        if ((event.ctrlKey || event.metaKey) && event.key === 'c') {
            // 检查当前焦点是否在搜索框中
            const activeElement = document.activeElement;
            const isSearchInputFocused = activeElement &&
                (activeElement.id === 'color-range-search-input' ||
                    activeElement.tagName === 'INPUT' ||
                    activeElement.tagName === 'TEXTAREA');

            // 如果焦点在输入框中，不拦截复制操作，让浏览器处理文本复制
            if (isSearchInputFocused) {
                console.log('焦点在输入框中，允许复制文本内容');
                return;
            }

            // 检查是否有选中的截图
            const currentSelectedScreenshot = getCurrentSelectedScreenshot();
            if (currentSelectedScreenshot) {
                // 阻止默认的复制行为
                event.preventDefault();
                event.stopPropagation();

                // 复制选中的图片到剪贴板
                await copySelectedImageToClipboard();
            } else {
                // 如果没有选中图片，给用户提示
                console.log('没有选中的图片，跳过复制操作');
            }
        }
    });

    console.log('键盘事件监听器已设置，支持Ctrl+C/Command+C复制选中图片');
}

/**
 * 打开随机颜色主题目录
 */
export async function openRandomThemeFolder(): Promise<void> {
    try {
        const { messageLoading } = await import('../prompt_message');
        const { get_the_root_directory_path } = await import('../get_root_dir');

        messageLoading('正在获取主题目录...');
        const rootDir = await get_the_root_directory_path('random_color_theme');
        const themePath = await join(rootDir, '随机颜色主题');
        await invoke('打开目录或文件', { path: themePath });
        messageSuccess('已请求打开目录');
    } catch (error) {
        console.error('打开随机颜色主题目录失败:', error);
        messageError(`打开目录失败: ${error}`);
    }
}