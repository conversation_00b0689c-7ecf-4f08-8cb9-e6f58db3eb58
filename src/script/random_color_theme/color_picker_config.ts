/**
 * 颜色取色器核心配置和状态管理模块
 * 负责平台检测、配置管理和全局状态变量
 */

// 存储颜色数据的全局变量
export let colorDatabase: Record<string, { name: string, target: string }> = {};
// 标记数据库是否已加载
export let isDatabaseLoaded = false;
// 标记路径是否已初始化
export let isPathInitialized = false;
// 数据文件路径
export let dataJsonPath = '';

// 为了解决取色器激活后频繁开启关闭的问题，添加锁定机制
export let colorPickerLocked = false;
export let colorPickerLockedTimeout: number | null = null;

// 存储清理函数的全局变量
export let globalCleanupHandler: (() => void) | null = null;

// 存储系统平台信息
export let currentPlatform: string = 'unknown';

// 设置器函数，用于更新全局状态
export function setColorDatabase(db: Record<string, { name: string, target: string }>) {
    colorDatabase = db;
}

export function setIsDatabaseLoaded(loaded: boolean) {
    isDatabaseLoaded = loaded;
}

export function setIsPathInitialized(initialized: boolean) {
    isPathInitialized = initialized;
}

export function setDataJsonPath(path: string) {
    dataJsonPath = path;
}

export function setColorPickerLocked(locked: boolean) {
    colorPickerLocked = locked;
}

export function setColorPickerLockedTimeout(timeout: number | null) {
    colorPickerLockedTimeout = timeout;
}

export function setGlobalCleanupHandler(handler: (() => void) | null) {
    globalCleanupHandler = handler;
}

export function setCurrentPlatform(platform: string) {
    currentPlatform = platform;
}

// 检测当前操作系统平台
export function detectPlatform() {
    try {
        // 使用window.platform替代plugin-os的platform()
        currentPlatform = window.platform || 'unknown';
        return currentPlatform;
    } catch (error) {
        console.error('检测平台失败:', error);
        return 'unknown';
    }
}

// 定义平台相关的配置参数
export const platformConfig = {
    win32: {
        smoothFactor: 0.05,      // Windows下的平滑因子，显著降低以减缓移动速度
        throttleTime: 16,        // Windows下的节流时间（毫秒）
        magnifierSize: 200,      // 正常放大镜大小
        magnifierSizeExpanded: 600, // 扩展后的放大镜大小 (200 * 3)
        captureSize: 17,         // 正常捕获范围
        captureSizeExpanded: 51  // 扩展后的捕获范围 (17 * 3)
    },
    default: {
        smoothFactor: 0.8,       // 其他平台的平滑因子
        throttleTime: 4,         // 其他平台的节流时间
        magnifierSize: 200,      // 正常放大镜大小
        magnifierSizeExpanded: 600, // 扩展后的放大镜大小 (200 * 3)
        captureSize: 17,         // 正常捕获范围
        captureSizeExpanded: 51  // 扩展后的捕获范围 (17 * 3)
    }
};

// 获取当前平台配置
export function getPlatformConfig() {
    return currentPlatform === 'win32' ? platformConfig.win32 : platformConfig.default;
}

// 创建一个自定义光标
function createTransparentCursor() {
    // 在Windows平台上，使用cursor: none会导致移动速度变快，所以使用非常小的十字线光标代替

    // 创建Canvas绘制小十字线
    const canvas = document.createElement('canvas');
    canvas.width = 3;
    canvas.height = 3;

    const ctx = canvas.getContext('2d');
    if (ctx) {
        // 设置背景透明
        ctx.clearRect(0, 0, 2, 2);

        // 绘制极小的十字线
        ctx.strokeStyle = 'rgba(200, 200, 200, 0.1)'; // 非常淡的灰色，几乎看不见
        ctx.lineWidth = 0.1;

        // 横线
        ctx.beginPath();
        ctx.moveTo(0, 1.5);
        ctx.lineTo(3, 1.5);
        ctx.stroke();

        // 竖线
        ctx.beginPath();
        ctx.moveTo(1.5, 0);
        ctx.lineTo(1.5, 3);
        ctx.stroke();
    }

    // 转换为 data URL
    const dataURL = canvas.toDataURL('image/png');

    // 返回CSS cursor值，中心点为1,1
    return `url(${dataURL}) 1 1, crosshair`;
}

// 缓存透明光标
export const transparentCursor = createTransparentCursor();

// 全局ESC键事件处理函数，确保在任何情况下都能关闭取色器
export function globalEscapeKeyHandler(e: KeyboardEvent) {
    if (e.key === 'Escape' && window._colorPickerActive) {
        e.preventDefault();
        e.stopPropagation();
        // 这里需要在主文件中实现resetColorPickerState函数
        if (window.resetColorPickerState) {
            window.resetColorPickerState();
        }
    }
}