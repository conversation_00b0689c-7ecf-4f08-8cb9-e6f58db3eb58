/**
 * 颜色范围搜索核心模块
 * 提供基于正则表达式的颜色范围匹配功能
 */

import { messageError, messageSuccess, messageLoading } from '../prompt_message';
import { readTextFile } from '@tauri-apps/plugin-fs';
import { getDataJsonPath } from './color_database';
import { generateColorRegex } from './regex_processor';

// 类型定义
export interface ColorData {
    name: string;
    target: string;
}

export interface ColorSearchResult {
    color: string;
    data: ColorData;
}

export interface GroupedSearchResults {
    [ target: string ]: ColorSearchResult[];
}

/**
 * 颜色范围搜索管理器
 */
export class ColorRangeSearchManager {
    private isSearching = false;
    private currentAbortController: AbortController | null = null;

    /**
     * 执行颜色范围搜索
     * @param colorInput 用户输入的颜色值
     * @param rangeValue 可选的范围值(1-30)，如果不提供则使用默认值
     * @returns 按target分组的搜索结果
     */
    async searchColorRange(colorInput: string, rangeValue?: number): Promise<GroupedSearchResults | null> {
        if (this.isSearching) {
            messageError('正在搜索中，请稍候...');
            return null;
        }

        try {
            this.isSearching = true;
            this.currentAbortController = new AbortController();

            // 1. 解析和验证输入
            const parseResult = this.parseColorInput(colorInput);
            if (!parseResult) {
                messageError('请输入有效的颜色值格式 (如: 3de597 5, #3de597 5, 或 3de597)');
                return null;
            }

            // 使用解析结果中的范围值，如果没有则使用传入的rangeValue
            const finalRangeValue = parseResult.rangeValue || rangeValue;

            // 2. 显示加载提示
            messageLoading('正在生成颜色范围正则表达式...');

            // 3. 调用tt命令生成正则表达式
            const regex = await this.generateColorRegex(parseResult.colorValue, finalRangeValue);
            if (!regex) {
                messageError('生成正则表达式失败');
                return null;
            }

            // 4. 显示新的加载提示
            messageLoading('正在搜索匹配的颜色...');

            // 5. 读取并搜索data.json
            const results = await this.searchInDataJson(regex);
            if (!results || Object.keys(results).length === 0) {
                messageError('未找到匹配的颜色');
                return null;
            }

            // 6. 显示成功消息
            const totalMatches = Object.values(results).reduce((sum, arr) => sum + arr.length, 0);
            messageSuccess(`找到 ${totalMatches} 个匹配颜色，分布在 ${Object.keys(results).length} 个target中`);

            return results;

        } catch (error) {
            console.error('颜色范围搜索失败:', error);
            messageError(`搜索失败: ${error instanceof Error ? error.message : '未知错误'}`);
            return null;
        } finally {
            this.isSearching = false;
            this.currentAbortController = null;
        }
    }

    /**
     * 解析颜色输入，支持带范围值的格式
     * @param input 用户输入
     * @returns 解析结果 {colorValue: string, rangeValue?: number} 或 null
     */
    private parseColorInput(input: string): { colorValue: string, rangeValue?: number } | null {
        if (!input || typeof input !== 'string') {
            return null;
        }

        const trimmed = input.trim();

        // 如果输入为空，返回null
        if (!trimmed) {
            return null;
        }

        // 尝试解析格式: "颜色值 范围值" 或 "#颜色值 范围值"
        const match = trimmed.match(/^(#?[0-9A-Fa-f]+)(?:\s+(\d+))?$/);

        if (match) {
            const colorPart = match[ 1 ];
            const rangePart = match[ 2 ];

            // 验证颜色部分（移除#号后应该是6位十六进制）
            const cleanColor = colorPart.replace(/^#/, '');
            if (!/^[0-9A-Fa-f]{6}$/.test(cleanColor)) {
                return null;
            }

            // 验证范围部分（如果存在）
            let rangeValue: number | undefined;
            if (rangePart) {
                const range = parseInt(rangePart, 10);
                if (range >= 1 && range <= 30) {
                    rangeValue = range;
                } else {
                    return null; // 范围值无效
                }
            }

            return {
                colorValue: cleanColor,
                rangeValue: rangeValue
            };
        }

        return null;
    }

    // 已删除未使用的 cleanColorInput 方法

    /**
     * 调用tt命令生成颜色范围正则表达式
     * @param color 清理后的颜色值
     * @param rangeValue 可选的范围值
     * @returns 正则表达式字符串或null
     */
    private async generateColorRegex(color: string, rangeValue?: number): Promise<string | null> {
        try {
            // 使用正则表达式处理器，传递范围值
            const result = await generateColorRegex(color, rangeValue);

            if (result.success && result.regex) {
                console.log('生成的正则表达式:', result.regex);
                return result.regex;
            } else {
                console.error('生成正则表达式失败:', result.error);
                return null;
            }

        } catch (error) {
            console.error('调用tt命令失败:', error);
            throw new Error('生成颜色范围正则表达式失败');
        }
    }



    /**
     * 在data.json中搜索匹配的颜色
     * @param regexPattern 正则表达式模式
     * @returns 按target分组的搜索结果
     */
    private async searchInDataJson(regexPattern: string): Promise<GroupedSearchResults | null> {
        try {
            // 获取data.json路径
            const dataJsonPath = getDataJsonPath();
            if (!dataJsonPath) {
                throw new Error('data.json路径未初始化');
            }

            // 读取data.json文件
            const jsonContent = await readTextFile(dataJsonPath);
            const colorData = JSON.parse(jsonContent) as Record<string, ColorData>;

            // 创建正则表达式对象
            const regex = new RegExp(regexPattern, 'i');

            // 搜索匹配的颜色
            const results: GroupedSearchResults = {};

            for (const [ colorKey, data ] of Object.entries(colorData)) {
                // 移除颜色值前的#号进行匹配
                const cleanColorKey = colorKey.replace(/^#/, '');

                if (regex.test(cleanColorKey)) {
                    const target = data.target;

                    if (!results[ target ]) {
                        results[ target ] = [];
                    }

                    results[ target ].push({
                        color: colorKey,
                        data: data
                    });
                }
            }

            // 对每个target内的结果按颜色值排序
            for (const target in results) {
                results[ target ].sort((a, b) => a.color.localeCompare(b.color));
            }

            return results;

        } catch (error) {
            console.error('搜索data.json失败:', error);
            throw new Error('搜索颜色数据失败');
        }
    }

    /**
     * 中断当前搜索
     */
    abortSearch(): void {
        if (this.currentAbortController) {
            this.currentAbortController.abort();
            this.currentAbortController = null;
        }
        this.isSearching = false;
    }

    /**
     * 检查是否正在搜索
     */
    isCurrentlySearching(): boolean {
        return this.isSearching;
    }
}

// 导出单例实例
export const colorRangeSearchManager = new ColorRangeSearchManager();

// 导出便捷函数
export async function searchColorRange(colorInput: string, rangeValue?: number): Promise<GroupedSearchResults | null> {
    return colorRangeSearchManager.searchColorRange(colorInput, rangeValue);
}

export function abortColorRangeSearch(): void {
    colorRangeSearchManager.abortSearch();
}
