/**
 * 颜色数据库管理模块
 * 负责路径初始化、数据库加载和颜色数据管理
 */

import { messageError } from '../prompt_message';
import { get_the_root_directory_path } from '../get_root_dir';
import { join } from '@tauri-apps/api/path';
import { readTextFile, exists } from '@tauri-apps/plugin-fs';
import {
    colorDatabase,
    isDatabaseLoaded,
    isPathInitialized,
    dataJsonPath,
    setColorDatabase,
    setIsDatabaseLoaded,
    setIsPathInitialized,
    setDataJsonPath
} from './color_picker_config';

/**
 * 初始化路径
 * 将路径初始化与其他功能解耦，避免一个功能失败导致全部功能失效
 */
export async function initPaths() {
    try {
        const rootDirPath = await get_the_root_directory_path('random_color_theme');
        const jsonPath = await join(rootDirPath, '随机颜色主题', 'data.json');
        setDataJsonPath(jsonPath);
        setIsPathInitialized(true);
        return true;
    } catch (error) {
        console.error('路径初始化失败:', error);
        return false;
    }
}

/**
 * 初始化颜色数据库
 * 先确保路径已初始化
 * @param forceReload 是否强制重新加载，默认为false
 */
export async function initColorDatabase(forceReload: boolean = false) {
    // 如果数据库已加载且不强制重新加载，直接返回成功
    if (isDatabaseLoaded && !forceReload) {
        console.log('颜色数据库已加载，跳过重复加载');
        return true;
    }

    // 如果路径未初始化，先初始化路径
    if (!isPathInitialized) {
        const pathInitSuccess = await initPaths();
        if (!pathInitSuccess) {
            console.error('无法初始化颜色数据库：路径初始化失败');
            return false;
        }
    }

    try {
        // 检查文件是否存在
        const fileExists = await exists(dataJsonPath);
        if (!fileExists) {
            // 仅在控制台输出警告，不向用户显示错误提示
            console.warn('颜色数据文件不存在，取色器将无法获取颜色资源');
            return false;
        }

        console.log(forceReload ? '强制重新加载颜色数据库...' : '首次加载颜色数据库...');
        const jsonContent = await readTextFile(dataJsonPath);

        try {
            const parsedData = JSON.parse(jsonContent);
            setColorDatabase(parsedData);
            setIsDatabaseLoaded(true);
            console.log('颜色数据库加载成功，包含', Object.keys(parsedData).length, '个颜色条目');
            return true;
        } catch (parseError) {
            messageError(`颜色数据JSON解析失败: ${parseError}`);
            return false;
        }
    } catch (error) {
        messageError(`加载颜色数据失败: ${error}`);
        return false;
    }
}

/**
 * 获取颜色数据库
 */
export function getColorDatabase() {
    return colorDatabase;
}

/**
 * 检查数据库是否已加载
 */
export function getIsDatabaseLoaded() {
    return isDatabaseLoaded;
}

/**
 * 获取数据文件路径
 */
export function getDataJsonPath() {
    return dataJsonPath;
}

/**
 * 检查路径是否已初始化
 */
export function getIsPathInitialized() {
    return isPathInitialized;
}

/**
 * 根据颜色HEX值查找颜色信息
 * @param colorHex 颜色HEX值（如：#FF0000）
 * @returns 颜色信息或null
 */
export function findColorByHex(colorHex: string) {
    if (!isDatabaseLoaded) {
        return null;
    }

    const colorKey = colorHex.toUpperCase();
    return colorDatabase[ colorKey ] || null;
}

/**
 * 检查指定颜色是否存在于数据库中
 * @param colorHex 颜色HEX值
 * @returns 是否存在
 */
export function hasColor(colorHex: string) {
    if (!isDatabaseLoaded) {
        return false;
    }

    const colorKey = colorHex.toUpperCase();
    return colorKey in colorDatabase;
}