/**
 * 搜索颜色工具页面初始化和事件绑定模块
 * 负责页面加载后的初始化工作和事件绑定
 */

import {
    loadModuleStatusFromConfig,
    saveModuleStatusToConfig
} from './search_color_config';
import {
    createGenerateThemeButton,
    createApplyThemeButton
} from './search_color_ui';
import {
    init,
    renderScreenshots,
    addScreenshot,
    clearAllScreenshots,
    updateScreenshotSelection
} from './screenshot_manager';
import {
    setupImageCopyKeyboardHandler,
    openRandomThemeFolder
} from './device_file_operations';
import {
    setupGenerateModuleEvents,
    reloadDataJsonAndRefresh
} from './module_config_manager';
import { initializeColorRangeSearch } from './color_range_search_ui';
// 开发环境测试模块 - 暂时注释掉避免自动执行
// import './color_range_search_test';

/**
 * 创建并绑定主要功能按钮
 */
function createAndBindMainButtons(mainButtonsContainer: HTMLElement): void {
    // 创建添加截图按钮（纯文字版） - 最右侧
    const addScreenshotBtn = document.createElement('button');
    addScreenshotBtn.id = 'add-screenshot-button';
    addScreenshotBtn.className = 'add-screenshot-button';
    addScreenshotBtn.textContent = '添加截图';
    mainButtonsContainer.appendChild(addScreenshotBtn);

    // 创建清空所有截图按钮（纯文字版）
    const clearAllBtn = document.createElement('button');
    clearAllBtn.id = 'clear-all-button';
    clearAllBtn.className = 'clear-all-button';
    clearAllBtn.textContent = '清空截图';
    mainButtonsContainer.appendChild(clearAllBtn);

    // 创建生成随机颜色按钮（下拉版）
    const generateThemeBtn = createGenerateThemeButton();
    mainButtonsContainer.appendChild(generateThemeBtn);

    // 创建应用完整主题包按钮
    const applyFullBtn = createApplyThemeButton('混合版', 'full');
    mainButtonsContainer.appendChild(applyFullBtn);

    // 创建应用图片版本按钮
    const applyImageBtn = createApplyThemeButton('图片版', 'image');
    mainButtonsContainer.appendChild(applyImageBtn);

    // 创建应用颜色值版本按钮 - 最左侧
    const applyColorBtn = createApplyThemeButton('颜色版', 'color');
    mainButtonsContainer.appendChild(applyColorBtn);

    // 创建打开随机颜色主题目录按钮
    const openThemeFolderBtn = document.createElement('button');
    openThemeFolderBtn.className = 'add-screenshot-button';
    openThemeFolderBtn.textContent = '打开目录';
    openThemeFolderBtn.id = 'open-random-theme-folder-btn';
    mainButtonsContainer.appendChild(openThemeFolderBtn);

    // 绑定按钮事件
    bindButtonEvents(addScreenshotBtn, clearAllBtn, openThemeFolderBtn);
}

/**
 * 绑定按钮事件
 */
function bindButtonEvents(
    addScreenshotBtn: HTMLButtonElement,
    clearAllBtn: HTMLButtonElement,
    openThemeFolderBtn: HTMLButtonElement
): void {
    // 绑定添加截图按钮事件
    if (addScreenshotBtn) {
        addScreenshotBtn.addEventListener('click', async () => {
            try {
                // 调用截图功能
                await addScreenshot();
            } catch (error) {
                console.error('添加截图失败:', error);
                const { messageError } = await import('../prompt_message');
                messageError('添加截图失败');
            }
        });
    } else {
        console.warn('未找到添加截图按钮');
    }

    // 绑定清空所有截图按钮事件
    if (clearAllBtn) {
        clearAllBtn.addEventListener('click', async () => {
            try {
                // 调用清空所有截图功能
                await clearAllScreenshots();
            } catch (error) {
                console.error('清空所有截图失败:', error);
                const { messageError } = await import('../prompt_message');
                messageError('清空所有截图失败');
            }
        });
    } else {
        console.warn('未找到清空所有截图按钮');
    }

    // 绑定打开目录按钮事件
    openThemeFolderBtn.addEventListener('click', openRandomThemeFolder);
}

/**
 * 设置截图容器点击事件
 */
function setupScreenshotContainerEvents(): void {
    // 给截图容器添加点击事件
    const container = document.getElementById('screenshots-container');
    if (container) {
        // 直接添加新的点击事件处理器
        container.addEventListener('click', (e) => {
            try {
                const target = e.target as HTMLElement;
                if (!target) return;

                const imgContainer = target.closest('.screenshot-img-container');
                if (!imgContainer) return;

                const screenshotItem = target.closest('.screenshot-item');
                if (!screenshotItem) return;

                const filename = screenshotItem.getAttribute('data-filename');
                if (!filename) return;

                const img = imgContainer.querySelector('.screenshot-img') as HTMLImageElement;
                if (!img || !img.src || img.src.includes('data:image/svg+xml')) return;

                // 更新当前选中的截图
                import('./screenshot_manager').then(({ setCurrentSelectedScreenshot }) => {
                    setCurrentSelectedScreenshot(filename);
                });

                // 更新UI状态
                updateScreenshotSelection(screenshotItem, img.src);

                console.log('截图点击处理成功');
            } catch (error) {
                console.error('截图点击处理失败:', error);
            }
        });

        console.log('截图容器点击事件设置成功');
    }
}

/**
 * 创建页面基础元素
 */
function createPageBaseElements(): void {
    // 创建按钮容器
    const guideContainer = document.createElement('div');
    guideContainer.className = 'guide-container';
    document.body.appendChild(guideContainer);

    // 创建添加截图按钮（纯文字版）
    const guide = document.createElement('div');
    guide.id = 'guide';
    guide.className = 'guide';
    guide.textContent = '点击对应颜色可获取资源名，按下 CTRL 可放大取色区域。';
    guideContainer.appendChild(guide);

    // 创建主按钮容器（统一容器，从右到左排列）
    const mainButtonsContainer = document.createElement('div');
    mainButtonsContainer.className = 'main-buttons-container';
    document.body.appendChild(mainButtonsContainer);

    // 创建并绑定主要功能按钮
    createAndBindMainButtons(mainButtonsContainer);
}

/**
 * 初始化页面功能
 */
async function initializePageFeatures(): Promise<void> {
    // 确保初始化
    await init();

    // 加载模块状态配置
    console.log('开始加载模块状态配置...');
    await loadModuleStatusFromConfig();
    console.log('模块状态配置加载完成');

    // 渲染截图
    await renderScreenshots();
}

/**
 * 设置配置自动保存
 */
function setupConfigAutoSave(): void {
    // 页面卸载前保存模块状态配置
    window.addEventListener('beforeunload', async () => {
        await saveModuleStatusToConfig();
    });

    // 页面隐藏时也保存（例如切换应用时）
    document.addEventListener('visibilitychange', async () => {
        if (document.visibilityState === 'hidden') {
            await saveModuleStatusToConfig();
        }
    });
}

/**
 * 设置全局窗口函数
 */
function setupGlobalWindowFunctions(): void {
    // 将重新加载函数暴露到全局窗口
    window.reloadDataJsonAndRefresh = reloadDataJsonAndRefresh;
}

/**
 * 主初始化函数
 */
export async function initializeSearchColorTools(): Promise<void> {
    try {
        // 确保当前页面是颜色搜索页面，避免在主窗口中显示这些按钮
        if (!document.querySelector('.color-search-window')) {
            return; // 如果不是颜色搜索页面，退出初始化
        }

        // 初始化页面功能
        await initializePageFeatures();

        // 设置截图容器点击事件
        setupScreenshotContainerEvents();

        // 创建页面基础元素
        createPageBaseElements();

        // 设置键盘事件监听器
        setupImageCopyKeyboardHandler();

        // 设置配置自动保存
        setupConfigAutoSave();

        // 设置全局窗口函数
        setupGlobalWindowFunctions();

        // 初始化颜色范围搜索功能
        initializeColorRangeSearch();

        console.log('搜索颜色工具初始化完成');

    } catch (error) {
        console.error('搜索颜色工具初始化失败:', error);
    }
}

/**
 * DOMContentLoaded事件处理
 */
document.addEventListener('DOMContentLoaded', async () => {
    await initializeSearchColorTools();
});

// 导出初始化函数以供外部调用
export { setupGenerateModuleEvents };
export { setupConfigAutoSave };