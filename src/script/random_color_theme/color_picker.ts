import {
    detectPlatform,
    getPlatformConfig
} from './color_picker_config';
import {
    createAllUIElements
} from './color_picker_ui';
import {
    createScreenshotFunction,
    createMagnifierDrawFunction
} from './magnifier_renderer';
import {
    createMouseMoveHandler,
    create<PERSON>eyDownHandler,
    create<PERSON>ey<PERSON>pHandler,
    createMouseOutHandler,
    createEscapeKeyHandler,
    createMousePollingFunction,
    setupFastMouseOutDetection,
    setupMouseBoundaryInterval,
    addEventListeners
} from './color_picker_events';
import {
    createPickColorHandler,
    createMagnifierSizeUpdater,
    createColorPickerCleanup,
    setupColorPickerGlobalState,
    createColorPickerConfig,
    resetColorPickerState as resetColorPickerStateCore,
    initColorDatabase
} from './color_picker_core';
import {
    activateColorPicker as activateColorPickerFn,
    initializePreviewAreaAutoColorPicking,
    initializeGlobalEventListeners
} from './color_picker_automation';
import {
    initializeGlobalVariables,
    exportGlobalFunctions
} from './color_picker_utils';
import {
    getColorPickerStateMachine,
    ColorPickerEvent,
    ColorPickerState
} from './color_picker_state_machine';





/**
 * 强制重置取色器状态
 * 用于在取色器卡住或状态异常时手动恢复
 */
export async function resetColorPickerState() {
    const stateMachine = getColorPickerStateMachine();
    if (stateMachine.getCurrentState() !== ColorPickerState.IDLE) {
        await stateMachine.executeEvent(ColorPickerEvent.RESET);
    }
    return await resetColorPickerStateCore();
}

/**
 * 纯前端实现的颜色取色器函数
 * 执行函数后鼠标变成取色器，点击后吸取点击位置颜色格式为#HEX
 * @returns Promise<string | undefined> 返回获取到的颜色值，如果失败则返回undefined
 */
export async function colorPicker(): Promise<string | undefined> {
    // 获取状态机实例
    const stateMachine = getColorPickerStateMachine();

    // 检查当前状态，避免重复激活
    if (stateMachine.getCurrentState() !== ColorPickerState.IDLE) {
        console.warn('取色器已在激活状态，忽略重复激活请求');
        return undefined;
    }

    // 颜色数据库已在页面加载时初始化，这里不需要重复初始化
    // 如果需要颜色信息，会在 updateColorInfo 函数中检查数据库状态

    // 创建取色器配置
    const config = createColorPickerConfig();
    const { finalTransition, globalTimeout } = config;

    // 开始激活流程
    const activationSuccess = await stateMachine.executeEvent(ColorPickerEvent.ACTIVATE);
    if (!activationSuccess) {
        console.error('取色器激活失败');
        return undefined;
    }

    // 设置取色器全局状态
    setupColorPickerGlobalState();

    // 创建一个用于存储最后结果的变量
    let resultColor: { value: string | undefined } = { value: undefined };

    // 创建一个闭包变量来存储Promise的resolve函数
    let promiseResolve: ((value: string | undefined) => void) | null = null;

    // 使用Promise来封装异步操作，允许调用方等待取色结果
    return new Promise<string | undefined>(async (resolvePromise, reject) => {
        // 存储Promise的resolve函数到闭包变量
        promiseResolve = resolvePromise;

        try {
            // 标记激活成功
            await stateMachine.executeEvent(ColorPickerEvent.ACTIVATION_SUCCESS);
        } catch (error) {
            console.error('取色器激活失败:', error);
            await stateMachine.executeEvent(ColorPickerEvent.ACTIVATION_FAILED);
            reject(error);
            return;
        }

        try {
            // 创建所有UI元素
            const uiElements = createAllUIElements(finalTransition);
            const { eventCatcher, magnifier, canvas, colorInfoElement, screenCanvas, tempCanvas, screenCtx, tempCtx } = uiElements;

            // 获取当前平台配置
            const platformConfig = getPlatformConfig();

            // 创建屏幕截图函数
            const takeScreenshot = createScreenshotFunction(screenCanvas, screenCtx);

            // 初始拍摄屏幕快照
            takeScreenshot();

            // 控制键状态跟踪
            const isControlPressed = { value: false }; // 用于跟踪Ctrl键是否被按下
            let currentCaptureSize = platformConfig.captureSize; // 当前捕获大小
            let currentMagnifierSize = platformConfig.magnifierSize; // 当前放大镜大小

            // 创建放大镜绘制函数
            const drawMagnifierImpl = createMagnifierDrawFunction(
                magnifier,
                canvas,
                colorInfoElement,
                screenCanvas,
                screenCtx,
                tempCanvas,
                tempCtx,
                () => currentCaptureSize,
                () => currentMagnifierSize
            );

            // 创建放大镜尺寸更新函数
            const updateMagnifierSizeImpl = createMagnifierSizeUpdater(
                magnifier,
                isControlPressed,
                drawMagnifierImpl
            );

            // 声明事件处理函数（在cleanup函数定义后再实现）
            let handleMouseMove: (e: MouseEvent) => void;
            let handleEscapeKey: (e: KeyboardEvent) => void;
            let handleKeyDown: (event: KeyboardEvent) => void;
            let handleKeyUp: (event: KeyboardEvent) => void;
            let handleMouseOut: (event: MouseEvent) => void;
            let startMousePolling: () => void;

            // 创建取色点击处理函数
            const pickColor = createPickColorHandler(
                screenCanvas,
                screenCtx,
                resultColor,
                () => cleanup() // 传入cleanup的引用
            );

            // 创建一个统一的清理函数
            const cleanup = createColorPickerCleanup(
                promiseResolve,
                resultColor,
                globalTimeout,
                uiElements,
                {
                    get handleMouseMove() { return handleMouseMove; },
                    get handleEscapeKey() { return handleEscapeKey; },
                    get handleKeyDown() { return handleKeyDown; },
                    get handleKeyUp() { return handleKeyUp; },
                    get handleMouseOut() { return handleMouseOut; },
                    get startMousePolling() { return startMousePolling; }
                },
                eventCatcher,
                pickColor,
                stateMachine
            );

            // 在cleanup函数定义后，创建所有事件处理函数
            handleMouseMove = createMouseMoveHandler((x: number, y: number) => {
                drawMagnifierImpl(x, y);
            });

            handleEscapeKey = createEscapeKeyHandler(cleanup);

            handleKeyDown = createKeyDownHandler(
                isControlPressed,
                () => {
                    const sizes = updateMagnifierSizeImpl();
                    currentMagnifierSize = sizes.currentMagnifierSize;
                    currentCaptureSize = sizes.currentCaptureSize;
                },
                cleanup
            );

            handleKeyUp = createKeyUpHandler(
                isControlPressed,
                () => {
                    const sizes = updateMagnifierSizeImpl();
                    currentMagnifierSize = sizes.currentMagnifierSize;
                    currentCaptureSize = sizes.currentCaptureSize;
                }
            );

            handleMouseOut = createMouseOutHandler(cleanup);

            // 设置快速鼠标移出检测
            setupFastMouseOutDetection(resetColorPickerState);

            // 设置窗口边界检测间隔
            setupMouseBoundaryInterval(cleanup);

            // 使用事件模块创建的鼠标轮询函数
            startMousePolling = createMousePollingFunction(cleanup);

            // 开始鼠标位置轮询
            startMousePolling();

            // 设置全局超时，确保取色器不会无限期挂起
            setTimeout(() => {
                if (window._colorPickerActive) {
                    resetColorPickerState();
                    // 使用闭包变量解析Promise
                    if (promiseResolve) {
                        promiseResolve(resultColor.value || window._lastPickedColor);
                    }
                }
            }, globalTimeout);

            // 将清理函数暴露到全局，以便resetColorPickerState可以调用
            window._colorPickerCleanup = cleanup;

            // 添加所有事件监听器
            addEventListeners(
                handleMouseMove,
                handleEscapeKey,
                handleKeyDown,
                handleKeyUp,
                handleMouseOut,
                eventCatcher,
                pickColor
            );

        } catch (error) {
            console.error('取色器初始化失败:', error);
            reject(error);
        }
    });
}

/**
 * 手动激活取色器
 * 可以通过按钮或其他UI元素调用此函数
 */
export async function activateColorPicker() {
    return await activateColorPickerFn(colorPicker);
}




// 使用DOMContentLoaded事件或直接放在底部，确保DOM和Tauri API都已准备好
document.addEventListener('DOMContentLoaded', async () => {
    // 等待Tauri 2.0 API完全初始化
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 首先检测当前平台
    try {
        // 确保window.platform已加载
        if (typeof window.platform === 'undefined') {
            // 等待平台信息加载完成
            await new Promise(resolve => {
                const checkPlatform = () => {
                    if (typeof window.platform !== 'undefined') {
                        resolve(true);
                    } else {
                        setTimeout(checkPlatform, 100);
                    }
                };
                checkPlatform();
            });
        }

        detectPlatform();
    } catch (error) {
        console.error('平台检测失败，将使用默认配置:', error);
    }

    // 初始化全局变量
    initializeGlobalVariables();

    // 导出全局函数
    exportGlobalFunctions(
        colorPicker,
        resetColorPickerState,
        activateColorPicker
    );

    // 初始化全局事件监听器
    const globalEventCleanup = initializeGlobalEventListeners();

    // 并行初始化路径、数据库
    // 注意这里不使用await，确保一项初始化失败不会阻塞其他初始化

    // 初始化颜色数据库（不强制重新加载，避免重复加载）
    initColorDatabase(false)
        .then((success: boolean) => {
            if (success) {
                console.log('颜色数据库初始化成功');
            } else {
                console.warn('颜色数据库初始化失败，取色器将无法获取颜色资源');
            }
        })
        .catch((error: any) => console.error('颜色数据库初始化过程中出错:', error));

    // 初始化预览区域自动取色功能
    const previewAreaCleanup = initializePreviewAreaAutoColorPicking(() => activateColorPicker());

    // 在页面卸载时清理事件监听器
    window.addEventListener('beforeunload', () => {
        globalEventCleanup.cleanup();
        previewAreaCleanup.cleanup();
        resetColorPickerState();
    });
});


