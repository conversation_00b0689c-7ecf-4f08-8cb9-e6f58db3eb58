import { WebviewWindow } from '@tauri-apps/api/webviewWindow';

// 导入相关模块
import '../title_bar';
import '../help';
import '../user_name';
import '../current_platform';
import '../fix_env_vars';
import '../screenshot';
import '../get_root_dir';
import './random_color_theme';
import './search_color_tools';
import './color_picker';
import { init as initSearchColorTools, renderScreenshots, reloadDataJsonAndRefresh } from './search_color_tools';

/**
 * 初始化颜色搜索窗口
 * 该函数在颜色搜索窗口加载完成后调用，用于初始化UI和功能
 */
async function initSearchColorWindow(): Promise<void> {
    try {
        const startTime = performance.now();
        console.log('开始初始化颜色搜索窗口');

        // 添加窗口标识类
        document.body.classList.add('color-search-window');

        // 强制设置 HTML 和 body 尺寸，确保内容区域大小正确
        document.documentElement.style.overflow = 'hidden';
        document.documentElement.style.margin = '0';
        document.documentElement.style.padding = '0';

        document.body.style.overflow = 'hidden';
        document.body.style.margin = '0';
        document.body.style.padding = '0';

        // 初始化颜色搜索工具
        await initSearchColorTools();

        // 渲染截图
        await renderScreenshots();

        // 初始化取色器
        const colorPickerContainer = document.getElementById('color-picker-container');
        if (colorPickerContainer) {
            // 强制刷新布局，确保尺寸正确
            colorPickerContainer.style.display = 'none';
            setTimeout(() => {
                if (colorPickerContainer) {
                    colorPickerContainer.style.display = 'block';
                    console.log('取色器容器刷新完成');
                }
            }, 50);
        }

        // 强制重新计算布局
        setTimeout(() => {
            // 强制重绘
            window.dispatchEvent(new Event('resize'));

            console.log('强制重新计算布局完成');
        }, 200);

        const endTime = performance.now();
        console.log(`颜色搜索窗口初始化完成，耗时: ${endTime - startTime}ms`);
    } catch (error) {
        console.error('初始化颜色搜索窗口失败:', error);
    }
}

/**
 * 打开颜色搜索窗口
 */
export async function openSearchColorWindow(): Promise<void> {
    try {
        // 记录开始时间
        const startTime = performance.now();

        // 检查窗口是否已存在
        const existingWindow = await WebviewWindow.getByLabel('search_color');

        if (existingWindow) {
            // 如果窗口已存在，只需激活窗口（设置焦点和取消最小化）
            await existingWindow.setFocus();
            await existingWindow.unminimize();
            return;
        }

        // 检测当前平台
        const isWindows = window.platform === 'win32';
        console.log(`当前平台: ${isWindows ? 'Windows' : '非Windows'}, 开始创建颜色搜索窗口`);

        // 设置正确的初始尺寸，避免窗口打开时的尺寸跳跃
        const WINDOW_WIDTH = 1250;
        const WINDOW_HEIGHT = 1080;

        const windowConfig = {
            url: './search_color.html',
            title: '颜色搜索',
            width: WINDOW_WIDTH,
            height: WINDOW_HEIGHT,
            center: true,
            resizable: true,
            backgroundColor: "#00000000",
            decorations: false,
            transparent: true,
            acceptFirstMouse: false,
            alwaysOnTop: false,
            hiddenTitle: true,
            shadow: true,
            focus: false,
            dragDropEnabled: false,
            fullscreen: false,
            maximized: false,
            visibleOnAllWorkspaces: false,
            visible: false // 延迟显示，确保所有设置完成后再显示
        };

        console.log('开始创建窗口，平台信息:', isWindows ? 'Windows' : '非Windows');

        // 创建窗口实例
        const webview = new WebviewWindow('search_color', windowConfig);

        // 错误处理
        webview.once('tauri://error', (e: unknown) => {
            console.error('创建颜色搜索窗口失败:', e);
            if (e instanceof Error) {
                console.error('错误详情:', e.message, e.stack);
            }
        });

        // 监听窗口创建完成事件
        webview.once('tauri://created', async () => {
            try {
                console.log('颜色搜索窗口创建完成，等待 init_window.ts 完成初始化');

                // 简单的平台特殊处理
                if (isWindows) {
                    console.log('在Windows平台上进行特殊处理');
                    // Windows平台上需要短暂等待
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                // 不在这里显示窗口，让 init_window.ts 控制显示时机
                // 这样可以避免窗口在尺寸设置完成前就显示出来
                console.log('窗口创建完成，等待 DOM 加载和尺寸设置');

                const endTime = performance.now();
                console.log(`颜色搜索窗口创建完成，耗时: ${endTime - startTime}ms`);
            } catch (error) {
                console.error('颜色搜索窗口创建过程中发生错误:', error);
            }
        });

        // 监听窗口显示事件
        webview.once('tauri://shown', async () => {
            console.log('颜色搜索窗口显示事件触发');
            // 尺寸管理由 init_window.ts 处理，这里只记录日志
        });

        // 监听资源加载错误
        webview.listen('tauri://resource-not-found', (event) => {
            console.error('资源加载失败:', event);
        });
    } catch (error) {
        console.error('创建颜色搜索窗口过程中发生错误:', error);
        if (error instanceof Error) {
            console.error('错误详情:', error.message, error.stack);
        }
    }
}

// 页面加载完成后初始化功能
document.addEventListener('DOMContentLoaded', () => {

    // 延迟处理DOM操作，确保所有元素都已完全加载
    setTimeout(() => {
        // 为搜索颜色按钮添加点击事件监听 (仅在主窗口中执行)
        const searchColorBtn = document.getElementById('search-color');
        if (searchColorBtn) {
            searchColorBtn.addEventListener('click', () => {
                openSearchColorWindow();
            });
        } else {
        }

        // 只在颜色搜索窗口中初始化UI，通过窗口标签判断
        const currentWindow = WebviewWindow.getCurrent();
        if (currentWindow.label === 'search_color') {
            initSearchColorWindow();
        }
    }, 300); // 延迟300ms等待DOM完全加载
});

// 在生成主题后调用刷新
declare global {
    interface Window {
        reloadDataJsonAndRefresh?: typeof reloadDataJsonAndRefresh;
    }
}
window.reloadDataJsonAndRefresh = reloadDataJsonAndRefresh;
