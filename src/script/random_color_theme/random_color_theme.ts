import { invoke } from "@tauri-apps/api/core";
import { join } from '@tauri-apps/api/path';
import JSON5 from 'json5';
import { readTextFile, exists, mkdir, writeFile } from '@tauri-apps/plugin-fs';
import { messageError, messageSuccess, messageLoading } from '../prompt_message';
import { executeCommand } from '../terminal_commands';
import { open_uri } from '../file_operations';
// import { 缓存文件 } from '../cache_file';
import { get_the_root_directory_path } from '../get_root_dir';

/**
 * 生成主题描述文件
 * @param outputDir 输出目录路径
 * @returns Promise<boolean | { error: string }>
 */
export async function generateDescriptionFile(outputDir: string): Promise<boolean | { error: string }> {
    try {
        // 描述文件内容
        const descriptionContent = `<?xml version="1.0" encoding="utf-8"?>
<MIUI-Theme>
    <title>随机颜色主题</title>
    <designer>小米主题</designer>
    <author>小米主题</author>
    <version>1.0</version>
    <uiVersion>9999</uiVersion>
    <resourceType>theme</resourceType>
    <miuiAdapterVersion>9999</miuiAdapterVersion>
</MIUI-Theme>`;

        // 生成description.xml文件路径
        const descriptionPath = await join(outputDir, 'description.xml');

        // 将内容写入文件
        await writeFile(descriptionPath, new TextEncoder().encode(descriptionContent));

        return true;
    } catch (error) {
        console.error(`生成描述文件失败: ${error}`);
        return { error: `生成描述文件失败: ${error}` };
    }
}


/**
 * 反编译APK文件
 */
async function 反编译APK(
    savePath: string,
): Promise<string | void> {
    try {
        // 查找工具路径
        const apktoolPath = await invoke<string>('find_tool_path', { toolName: 'apktool' });
        if (!apktoolPath) {
            open_uri('https://apktool.org/docs/install');
            messageError('apktool 未安装，请先安装 apktool。如果你知道什么是包管理器，建议通过包管理器进行安装。');
            return;
        }

        // 执行反编译
        const outputDecompilePath = savePath.replace('.apk', '');
        await executeCommand(`${apktoolPath} d -f -s --keep-broken-res --resource-mode keep "${savePath}" -o "${outputDecompilePath}"`);
        return outputDecompilePath;
    } catch (error) {
        console.error(`反编译APK失败: ${error}`);
    }
}

/**
 * 从integers.xml内容中提取包含alpha字符串的integer标签
 * @param integersContent integers.xml文件内容
 * @returns 包含alpha的integer标签数组
 */
function extractAlphaIntegers(integersContent: string): string[] {
    const alphaIntegers: string[] = [];

    try {
        // 使用正则表达式匹配包含alpha的integer标签
        const integerRegex = /<integer\s+name="[^"]*alpha[^"]*"[^>]*>.*?<\/integer>/gi;
        const matches = integersContent.match(integerRegex);

        if (matches) {
            matches.forEach(match => {
                // 将integer标签的值改为255
                const modifiedInteger = match.replace(/>.*?<\/integer>/i, '>255</integer>');
                alphaIntegers.push(modifiedInteger);
            });
        }
    } catch (error) {
        console.error('提取alpha integer标签时出错:', error);
    }

    return alphaIntegers;
}

/**
 * 将alpha相关的integer标签合并到theme_values.xml内容中
 * @param themeContent 当前theme_values.xml内容
 * @param alphaIntegers alpha相关的integer标签数组
 * @returns 合并后的内容
 */
function mergeAlphaIntegers(themeContent: string, alphaIntegers: string[]): string {
    if (!themeContent || alphaIntegers.length === 0) {
        return themeContent;
    }

    try {
        // 如果没有现有内容，创建基本结构
        if (!themeContent.trim()) {
            themeContent = `<?xml version="1.0" encoding="utf-8"?>
<MIUI_Theme_Values>
</MIUI_Theme_Values>`;
        }

        // 在闭合标签前插入integer标签
        const insertPosition = themeContent.lastIndexOf('</MIUI_Theme_Values>');
        if (insertPosition !== -1) {
            const beforeClosing = themeContent.substring(0, insertPosition);
            const afterClosing = themeContent.substring(insertPosition);

            // 添加缩进和换行
            const integerSection = alphaIntegers.map(integer => `    ${integer}`).join('\n');
            const separator = beforeClosing.trim().endsWith('>') ? '\n' : '';

            return `${beforeClosing}${separator}    <!-- Alpha integers -->\n${integerSection}\n${afterClosing}`;
        } else {
            console.warn('未找到MIUI_Theme_Values闭合标签，无法插入integer标签');
            return themeContent;
        }
    } catch (error) {
        console.error('合并alpha integer标签时出错:', error);
        return themeContent;
    }
}

/**
 * 批量提取APK文件并反编译
 */
export async function random_color_theme(configOverride?: any) {
    // 记录开始时间
    const startTime = performance.now();

    try {
        const modules = [];
        let config: any;
        let configPath: string;

        if (configOverride) {
            console.log('使用传入的配置对象');
            config = configOverride;
            configPath = await invoke<string>('获取配置文件路径', { fileName: 'random_color_theme_config.json5' });
        } else {
            console.log('从文件读取配置');
            configPath = await invoke<string>('获取配置文件路径', { fileName: 'random_color_theme_config.json5' });
            const content = await readTextFile(configPath);
            config = JSON5.parse(content);
        }

        // 检查模块开关状态，只添加已启用的模块
        if (config.module && Array.isArray(config.module)) {
            // 如果存在模块状态配置，则按照状态过滤模块
            if (config.module_status && typeof config.module_status === 'object') {
                console.log('发现模块状态配置，只添加已启用的模块');
                // 添加状态为 true 或未设置状态的模块
                config.module.forEach((moduleName: string) => {
                    // 如果模块状态未定义或为 true，则添加
                    if (config.module_status[ moduleName ] === undefined || config.module_status[ moduleName ] === true) {
                        modules.push(moduleName);
                    } else {
                        console.log(`模块 ${moduleName} 已禁用，跳过处理`);
                    }
                });
            } else {
                // 如果不存在模块状态配置，则添加所有模块
                console.log('未发现模块状态配置，添加所有模块');
                modules.push(...config.module);
            }
        }

        console.log(`开始处理 ${modules.length} 个模块:`, modules);

        // 获取根目录路径
        const rootDirPath = await get_the_root_directory_path('random_color_theme');

        // apk路径
        const apkPath = await join(rootDirPath, 'apk');

        // 主题包路径
        const themePath = await join(rootDirPath, '随机颜色主题', '完整主题包');

        // 删除 random_color_theme 目录
        if (await exists(rootDirPath)) {
            messageLoading('正在清理旧文件...');
            await invoke('删除目录', { dirPath: rootDirPath });
        }

        // 创建新目录
        await mkdir(apkPath, { recursive: true });

        // 批量反编译apk
        messageLoading(`正在提取 ${modules.length} 个APK资源...`);

        // 判断packageName是否为空
        if (modules.length > 0) {
            messageLoading('正在提取资源...');

            // 获取设备选择功能
            const { checkAndSelectDeviceGeneral } = await import('../commands');

            // 检查并选择设备
            const selectedDevice = await checkAndSelectDeviceGeneral('选择设备进行随机颜色主题生成');
            if (!selectedDevice) {
                throw new Error('未选择设备，操作已取消');
            }

            console.log(`使用设备: ${selectedDevice.model} (${selectedDevice.id})`);

            try {
                // 智能多线程处理：根据CPU核心数确定并发数，避免ADB服务过载
                const cpuCores = navigator.hardwareConcurrency || 4; // 获取CPU核心数，默认4核
                const maxConcurrency = Math.min(cpuCores, 3); // 最大并发数不超过3，避免ADB过载
                console.log(`使用 ${maxConcurrency} 线程并发处理 ${modules.length} 个模块`);

                const { adb } = await import('../commands');
                let completedCount = 0;

                // 分批处理模块
                for (let i = 0; i < modules.length; i += maxConcurrency) {
                    const batch = modules.slice(i, i + maxConcurrency);
                    const batchPromises = batch.map(async (pkg, batchIndex) => {
                        const moduleIndex = i + batchIndex + 1;
                        const progress = `(${moduleIndex}/${modules.length})`;

                        try {
                            messageLoading(`正在处理 ${progress} ${pkg}...`);
                            console.log(`开始处理模块 ${progress}: ${pkg}`);

                            // 获取APK路径
                            const result = await adb([ '-s', selectedDevice.id, 'shell', 'pm', 'path', pkg ]);
                            const apkFilePath = result.replace('package:', '').trim();

                            if (!apkFilePath) {
                                console.warn(`模块 ${pkg} 未找到APK路径，跳过处理`);
                                return;
                            }

                            const savePackageName = pkg === 'android' ? 'framework-res' : pkg;
                            const savePath = await join(apkPath, `${savePackageName}.apk`);

                            // 提取APK文件
                            console.log(`提取 ${progress} ${pkg} APK文件...`);
                            await adb([ '-s', selectedDevice.id, 'pull', apkFilePath, savePath ]);

                            // 反编译APK
                            console.log(`反编译 ${progress} ${pkg}...`);
                            const outputDecompilePath = await 反编译APK(savePath);

                            if (outputDecompilePath) {
                                try {
                                    // 获取路径各个部分
                                    const pathParts = outputDecompilePath.split(/[/\\]/);
                                    // 获取最后一个目录名
                                    const lastDirName = pathParts[ pathParts.length - 1 ];

                                    // 创建子目录
                                    const targetDir = await join(themePath, lastDirName, 'res', 'drawable-xxhdpi');
                                    await mkdir(targetDir, { recursive: true });

                                    const colorsXmlPath = await join(outputDecompilePath, 'res', 'values', 'colors.xml');
                                    const integersXmlPath = await join(outputDecompilePath, 'res', 'values', 'integers.xml');
                                    const themeValuesXmlPath = await join(themePath, lastDirName, 'theme_values.xml');

                                    let finalContent = '';

                                    // 处理colors.xml
                                    if (await exists(colorsXmlPath)) {
                                        const colorsContent = await readTextFile(colorsXmlPath);
                                        finalContent = colorsContent
                                            .replace(/<resources/g, '<MIUI_Theme_Values')
                                            .replace(/<\/resources>/g, '</MIUI_Theme_Values>');
                                    }

                                    // 处理integers.xml中包含alpha的integer标签
                                    if (await exists(integersXmlPath)) {
                                        try {
                                            const integersContent = await readTextFile(integersXmlPath);
                                            const alphaIntegers = extractAlphaIntegers(integersContent);

                                            if (alphaIntegers.length > 0) {
                                                console.log(`在 ${pkg} 中找到 ${alphaIntegers.length} 个alpha相关的integer标签`);
                                                finalContent = mergeAlphaIntegers(finalContent, alphaIntegers);
                                            }
                                        } catch (error) {
                                            console.error(`处理 ${pkg} 的integers.xml时出错:`, error);
                                        }
                                    }

                                    // 写入最终的theme_values.xml
                                    if (finalContent) {
                                        await writeFile(themeValuesXmlPath, new TextEncoder().encode(finalContent));
                                    }

                                    completedCount++;
                                    console.log(`模块 ${pkg} 处理完成 (${completedCount}/${modules.length})`);
                                } catch (error) {
                                    console.error(`处理路径 ${outputDecompilePath} 时出错:`, error);
                                }
                            }
                        } catch (error) {
                            console.error(`处理包 ${pkg} 时出错:`, error);
                            // 继续处理下一个包，不中断整个流程
                        }
                    });

                    // 等待当前批次完成后再处理下一批次
                    await Promise.all(batchPromises);
                    messageLoading(`批次处理完成，已完成 ${Math.min(i + maxConcurrency, modules.length)}/${modules.length} 个模块...`);
                }

                await generateDescriptionFile(themePath);

            } catch (error) {
                messageError(`提取失败: ${error}`);
                throw error;
            }
        } else {
            throw new Error('没有找到要处理的模块');
        }

        messageLoading('正在处理资源...');

        // 调用后端处理图片
        const result = await invoke('process_theme', {
            inputDir: apkPath,
            outputDir: themePath,
            configPath: configPath
        });

        // 保留APK文件，不删除apkPath目录，方便用户调试和重复使用
        console.log(`APK文件保留在: ${apkPath}`);

        messageSuccess(`生成随机颜色主题成功: ${result}，总耗时: ${((performance.now() - startTime) / 1000).toFixed(2)}秒`);

    } catch (error) {
        console.error('生成随机颜色主题失败:', error);
        messageError(`生成随机颜色主题失败: ${error}，总耗时: ${((performance.now() - startTime) / 1000).toFixed(2)}秒`);
        throw error; // 重新抛出错误，让调用者知道失败了
    }
}

// 将函数导出为全局函数
declare global {
    interface Window {
        random_color_theme: typeof random_color_theme;
    }
}

// 添加到window对象，使其成为全局函数
window.random_color_theme = random_color_theme;
// random_color_theme()
