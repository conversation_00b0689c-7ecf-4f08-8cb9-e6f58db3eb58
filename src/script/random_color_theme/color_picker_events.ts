/**
 * 颜色取色器事件处理模块
 * 负责鼠标事件、键盘事件、窗口边界检测和鼠标位置跟踪
 */

import { getPlatformConfig, currentPlatform } from './color_picker_config';

/**
 * 事件处理器接口定义
 */
export interface ColorPickerEventHandlers {
    handleMouseMove: (e: MouseEvent) => void;
    handleKeyDown: (event: KeyboardEvent) => void;
    handleKeyUp: (event: KeyboardEvent) => void;
    handleMouseOut: (event: MouseEvent) => void;
    handleEscapeKey: (e: KeyboardEvent) => void;
    startMousePolling: () => void;
    updateMousePosition: (x: number, y: number) => void;
    cleanup: () => void;
}

/**
 * 鼠标边界检测间隔ID
 */
let mouseBoundaryInterval: number | null = null;

/**
 * 鼠标轮询间隔ID
 */
let mousePollInterval: number | null = null;

/**
 * 设置窗口边界检测间隔
 */
export function setupMouseBoundaryInterval(cleanup: () => void) {
    // 添加窗口边界检测 - 设置检测间隔
    mouseBoundaryInterval = setInterval(() => {
        // 如果取色器不活跃，不进行检查
        if (!window._colorPickerActive) return;

        // 如果处于强制锁定状态，暂时不关闭
        if (window._colorPickerForceLocked) return;

        // 获取当前鼠标位置
        const mouseX = window._lastMouseX;
        const mouseY = window._lastMouseY;

        // 检查鼠标是否在窗口边界外
        if (mouseX <= 0 || mouseY <= 0 ||
            mouseX >= window.innerWidth ||
            mouseY >= window.innerHeight) {

            cleanup();
        }
    }, 100); // 提高检测频率，从200ms改为100ms

    return mouseBoundaryInterval;
}

/**
 * 更新鼠标位置函数
 */
export function updateMousePosition(x: number, y: number) {
    window._lastMouseX = x;
    window._lastMouseY = y;
}

/**
 * 创建鼠标移动处理函数
 */
export function createMouseMoveHandler(drawMagnifierImpl: (x: number, y: number) => void) {
    let lastUpdateTime = 0;
    let animationFrameId: number | null = null;

    const handler = (e: MouseEvent) => {
        // 获取平台配置
        const config = getPlatformConfig();

        // 在Windows平台上节流以降低CPU使用率，防止过于频繁的重绘
        if (currentPlatform === 'win32') {
            const now = Date.now();
            if (now - lastUpdateTime < config.throttleTime) {
                return;
            }
            lastUpdateTime = now;
        }

        // 取消之前的动画帧请求，避免堆积
        if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
        }

        // 使用requestAnimationFrame确保平滑渲染
        animationFrameId = requestAnimationFrame(() => {
            // 将鼠标坐标直接传递给核心绘制函数
            drawMagnifierImpl(e.clientX, e.clientY);
            animationFrameId = null;
        });
    };

    // 暴露animationFrameId用于清理
    (handler as any).animationFrameId = animationFrameId;

    return handler;
}

/**
 * 创建键盘按下事件处理函数
 */
export function createKeyDownHandler(
    isControlPressed: { value: boolean },
    updateMagnifierSizeImpl: () => void,
    cleanup: () => void
) {
    return (event: KeyboardEvent) => {
        if (event.key === 'Control') {
            if (!isControlPressed.value) {
                isControlPressed.value = true;
                updateMagnifierSizeImpl(); // 更新放大镜尺寸
            }
        } else if (event.key === 'Escape') {
            cleanup();
        }
    };
}

/**
 * 创建键盘释放事件处理函数
 */
export function createKeyUpHandler(
    isControlPressed: { value: boolean },
    updateMagnifierSizeImpl: () => void
) {
    return (event: KeyboardEvent) => {
        if (event.key === 'Control') {
            if (isControlPressed.value) {
                isControlPressed.value = false;
                updateMagnifierSizeImpl(); // 更新放大镜尺寸
            }
        }
    };
}

/**
 * 创建鼠标移出事件处理函数
 */
export function createMouseOutHandler(cleanup: () => void) {
    return (event: MouseEvent) => {
        // 如果处于强制锁定状态，忽略此事件
        if (window._colorPickerForceLocked) {
            return;
        }

        // 检查鼠标是否真的离开了窗口
        // 使用relatedTarget来判断鼠标是否离开了文档
        if (!event.relatedTarget ||
            !(event.relatedTarget instanceof Node) ||
            !document.contains(event.relatedTarget as Node)) {

            // 记录最后鼠标位置
            window._lastMouseX = event.clientX;
            window._lastMouseY = event.clientY;

            // 立即检查鼠标位置是否在边界附近
            const mouseX = event.clientX;
            const mouseY = event.clientY;

            // 检查鼠标是否在窗口边界外
            if (mouseX <= 0 || mouseY <= 0 ||
                mouseX >= window.innerWidth ||
                mouseY >= window.innerHeight) {

                cleanup();
            }
        }
    };
}

/**
 * 创建ESC键处理函数
 */
export function createEscapeKeyHandler(cleanup: () => void) {
    return (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
            e.preventDefault();
            e.stopPropagation();
            if (window._colorPickerActive) {
                cleanup();
            }
        }
    };
}

/**
 * 创建鼠标位置轮询函数
 */
export function createMousePollingFunction(cleanup: () => void) {
    return function startMousePolling() {
        // 清除可能存在的之前的轮询
        if (mousePollInterval) {
            clearInterval(mousePollInterval);
            mousePollInterval = null;
        }

        // 用于节流的变量
        let lastProcessedTime = 0;
        let processingFrame = false;
        let lastProcessedX = 0;
        let lastProcessedY = 0;

        // 使用事件驱动替代高频轮询
        const mouseMoveHandler = (e: MouseEvent) => {
            // 立即更新鼠标位置记录
            window._lastMouseX = e.clientX;
            window._lastMouseY = e.clientY;

            // 如果已经在处理中或不需要更新，跳过
            const now = Date.now();
            const moveDistance = Math.abs(e.clientX - lastProcessedX) + Math.abs(e.clientY - lastProcessedY);
            if (processingFrame ||
                (now - lastProcessedTime < 30 && moveDistance < 5)) {
                return;
            }

            // 更新状态
            lastProcessedTime = now;
            lastProcessedX = e.clientX;
            lastProcessedY = e.clientY;
            processingFrame = true;

            // 使用requestAnimationFrame处理检查，降低性能开销
            requestAnimationFrame(() => {
                try {
                    // 检查取色器状态
                    if (!window._colorPickerActive) {
                        processingFrame = false;
                        return;
                    }

                    // 获取当前鼠标位置
                    const mouseX = window._lastMouseX;
                    const mouseY = window._lastMouseY;

                    // 检查鼠标是否在窗口内
                    const isInWindow =
                        mouseX > 0 && mouseX < window.innerWidth &&
                        mouseY > 0 && mouseY < window.innerHeight;

                    // 如果鼠标不在窗口内，立即关闭取色器
                    if (!isInWindow && !window._colorPickerForceLocked) {
                        cleanup();
                        return;
                    }

                    // 处理强制锁定状态
                    if (window._colorPickerForceLocked &&
                        Date.now() - window._colorPickerActivatedTime > 200) {
                        window._colorPickerForceLocked = false;
                    }

                    // 如果仍处于强制锁定状态，不进行其他检查
                    if (window._colorPickerForceLocked) {
                        processingFrame = false;
                        return;
                    }

                    // 检查是否离开预览区域
                    if (window._colorPickerSourceElement) {
                        // 获取最新的元素位置，预览区域可能会移动或调整大小
                        const rect = window._colorPickerSourceElement.getBoundingClientRect();

                        // 检查鼠标是否在预览区域外
                        const isOutside =
                            mouseX < rect.left ||
                            mouseX > rect.right ||
                            mouseY < rect.top ||
                            mouseY > rect.bottom;

                        // 如果鼠标在预览区域外，立即关闭取色器
                        if (isOutside) {
                            cleanup();
                        }
                    }
                } catch (error) {
                    console.error('鼠标位置检测出错:', error);
                } finally {
                    processingFrame = false;
                }
            });
        };

        // 添加事件监听器
        document.addEventListener('mousemove', mouseMoveHandler, { passive: true });

        // 清理原有轮询函数
        if (typeof (startMousePolling as any).cleanupHandler === 'function') {
            (startMousePolling as any).cleanupHandler();
        }

        // 保存事件清理函数，方便后续在cleanup中移除
        const cleanupHandler = () => {
            document.removeEventListener('mousemove', mouseMoveHandler);
            document.removeEventListener('mouseleave', mouseLeaveHandler);
        };

        // 同时存储到局部变量和全局变量
        (startMousePolling as any).cleanupHandler = cleanupHandler;

        // 添加window边界/鼠标离开检测
        const mouseLeaveHandler = (e: MouseEvent) => {
            if (window._colorPickerActive && !window._colorPickerForceLocked) {
                if (!e.relatedTarget || !(e.relatedTarget instanceof Node) ||
                    !document.contains(e.relatedTarget as Node)) {
                    cleanup();
                }
            }
        };

        document.addEventListener('mouseleave', mouseLeaveHandler);
    };
}

/**
 * 设置快速鼠标移出检测
 */
export function setupFastMouseOutDetection(resetColorPickerState: () => void) {
    // 为处理快速移动，添加一个额外的mouseout事件处理器，专注于快速响应
    document.addEventListener('mouseout', (event) => {
        if (window._colorPickerActive && !window._colorPickerForceLocked) {
            // 快速检查鼠标是否离开窗口
            if (event.clientY <= 0 || event.clientX <= 0 ||
                event.clientX >= window.innerWidth || event.clientY >= window.innerHeight) {
                resetColorPickerState();
            }
        }
    }, true); // 使用捕获阶段以确保尽早处理
}

/**
 * 清理所有事件监听器和间隔
 */
export function cleanupEventHandlers(
    handleMouseMove: (e: MouseEvent) => void,
    handleEscapeKey: (e: KeyboardEvent) => void,
    handleKeyDown: (event: KeyboardEvent) => void,
    handleKeyUp: (event: KeyboardEvent) => void,
    handleMouseOut: (event: MouseEvent) => void,
    eventCatcher: HTMLDivElement,
    pickColor: (event: MouseEvent) => void,
    startMousePolling: () => void
) {
    // 清除鼠标位置轮询
    if (mousePollInterval) {
        clearInterval(mousePollInterval);
        mousePollInterval = null;
    }

    // 清理鼠标事件处理函数
    if (typeof (startMousePolling as any).cleanupHandler === 'function') {
        (startMousePolling as any).cleanupHandler();
    }

    // 清除边界检测间隔
    if (mouseBoundaryInterval) {
        clearInterval(mouseBoundaryInterval);
        mouseBoundaryInterval = null;
    }

    // 移除所有事件监听器
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('keydown', handleEscapeKey);
    document.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('keyup', handleKeyUp);
    document.removeEventListener('mouseout', handleMouseOut);
    document.removeEventListener('mouseleave', handleMouseOut); // 移除mouseleave事件监听

    if (eventCatcher) {
        eventCatcher.removeEventListener('click', pickColor);
        eventCatcher.removeEventListener('mousemove', handleMouseMove);
    }

    // 取消任何可能正在进行的动画帧
    if ((handleMouseMove as any).animationFrameId) {
        cancelAnimationFrame((handleMouseMove as any).animationFrameId);
        (handleMouseMove as any).animationFrameId = null;
    }
}

/**
 * 添加所有事件监听器
 */
export function addEventListeners(
    handleMouseMove: (e: MouseEvent) => void,
    handleEscapeKey: (e: KeyboardEvent) => void,
    handleKeyDown: (event: KeyboardEvent) => void,
    handleKeyUp: (event: KeyboardEvent) => void,
    handleMouseOut: (event: MouseEvent) => void,
    eventCatcher: HTMLDivElement,
    pickColor: (event: MouseEvent) => void
) {
    // 添加事件监听器
    document.addEventListener('mousemove', handleMouseMove, { passive: false });
    document.addEventListener('keydown', handleEscapeKey);
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);
    document.addEventListener('mouseout', handleMouseOut);
    document.addEventListener('mouseleave', handleMouseOut); // 添加mouseleave事件监听
    eventCatcher.addEventListener('click', pickColor);
    eventCatcher.addEventListener('mousemove', handleMouseMove);
}