/**
 * 正则表达式处理模块
 * 专门处理tt命令的调用和正则表达式的解析提取
 */

import { executeCommand, AbortError } from '../terminal_commands';

/**
 * tt命令执行结果
 */
export interface TtCommandResult {
    success: boolean;
    regex?: string;
    originalOutput?: string;
    error?: string;
}

/**
 * 批量操作结果
 */
export interface BatchOperationResult {
    success: boolean;
    output?: string;
    error?: string;
}

/**
 * 正则表达式处理器
 */
export class RegexProcessor {
    private static instance: RegexProcessor;
    private commandTimeout = 30000; // 30秒超时

    private constructor() { }

    /**
     * 获取单例实例
     */
    static getInstance(): RegexProcessor {
        if (!RegexProcessor.instance) {
            RegexProcessor.instance = new RegexProcessor();
        }
        return RegexProcessor.instance;
    }

    /**
     * 调用tt命令生成颜色范围正则表达式
     * @param colorValue 颜色值（不带#号）
     * @param rangeValue 可选的范围值(1-30)
     * @returns tt命令执行结果
     */
    async generateColorRegex(colorValue: string, rangeValue?: number): Promise<TtCommandResult> {
        try {
            // 验证颜色值格式
            if (!this.isValidColorValue(colorValue)) {
                return {
                    success: false,
                    error: '无效的颜色值格式'
                };
            }

            // 构建tt命令，如果有范围值则添加
            const command = rangeValue ? `tt ${colorValue} ${rangeValue}` : `tt ${colorValue}`;
            console.log('执行tt命令:', command);

            // 执行命令
            const output = await this.executeWithTimeout(command);

            // 解析输出
            const regex = this.extractRegexFromOutput(output);

            if (regex) {
                return {
                    success: true,
                    regex: regex,
                    originalOutput: output
                };
            } else {
                return {
                    success: false,
                    error: '无法从输出中提取正则表达式',
                    originalOutput: output
                };
            }

        } catch (error) {
            console.error('tt命令执行失败:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '未知错误'
            };
        }
    }

    /**
     * 执行批量操作命令
     * @param nameList 名称列表，逗号分隔
     * @param firstInterval 首次间隔时间（秒）
     * @param otherInterval 其他间隔时间（秒）
     * @param signal 中断信号
     * @param onProgress 可选的进度回调函数
     * @returns 批量操作结果
     */
    async executeBatchOperation(
        nameList: string,
        firstInterval: number = 10,
        otherInterval: number = 2,
        signal?: AbortSignal,
        onProgress?: (progress: string) => void
    ): Promise<BatchOperationResult> {
        try {
            // 检查是否已中断
            if (signal?.aborted) {
                throw new AbortError();
            }

            // 验证参数
            if (!nameList || nameList.trim().length === 0) {
                return {
                    success: false,
                    error: '名称列表不能为空'
                };
            }

            // 构建批量操作命令
            const command = `tt "${nameList}" ${firstInterval} ${otherInterval}`;
            console.log('执行批量操作命令:', command);

            // 执行命令（支持中断和实时输出）
            const output = await this.executeWithTimeout(command, 120000, signal, onProgress); // 2分钟超时

            return {
                success: true,
                output: output
            };

        } catch (error) {
            // 区分中断和真正的错误
            if (error instanceof AbortError) {
                console.log('批量操作被用户中断');
                return {
                    success: false,
                    error: '操作已中断'
                };
            } else {
                console.error('批量操作命令执行失败:', error);
                return {
                    success: false,
                    error: error instanceof Error ? error.message : '未知错误'
                };
            }
        }
    }

    /**
     * 验证颜色值格式
     * @param colorValue 颜色值
     * @returns 是否有效
     */
    private isValidColorValue(colorValue: string): boolean {
        if (!colorValue || typeof colorValue !== 'string') {
            return false;
        }

        // 检查是否为6位十六进制值
        return /^[0-9A-Fa-f]{6}$/.test(colorValue.trim());
    }

    /**
     * 从tt命令输出中提取正则表达式
     * @param output tt命令的完整输出
     * @returns 提取的正则表达式或null
     */
    private extractRegexFromOutput(output: string): string | null {
        try {
            if (!output || typeof output !== 'string') {
                return null;
            }

            // 方法1: 查找 rule = "..." 模式
            const ruleMatch = output.match(/rule\s*=\s*"([^"]+)"/);
            if (ruleMatch && ruleMatch[ 1 ]) {
                console.log('通过rule模式提取到正则表达式');
                return ruleMatch[ 1 ];
            }

            // 方法2: 查找 rule="..." 模式（无空格）
            const ruleMatch2 = output.match(/rule="([^"]+)"/);
            if (ruleMatch2 && ruleMatch2[ 1 ]) {
                console.log('通过rule模式（无空格）提取到正则表达式');
                return ruleMatch2[ 1 ];
            }

            // 方法3: 查找可能的正则表达式模式（包含括号和管道符的复杂模式）
            const regexMatch = output.match(/([A-F0-9|()[\]{}+*?\\-]+)/);
            if (regexMatch && regexMatch[ 1 ] && regexMatch[ 1 ].length > 10) {
                console.log('通过模式匹配提取到正则表达式');
                return regexMatch[ 1 ];
            }

            console.warn('无法从输出中提取正则表达式:', output);
            return null;

        } catch (error) {
            console.error('解析正则表达式时出错:', error);
            return null;
        }
    }

    /**
     * 带超时的命令执行
     * @param command 要执行的命令
     * @param timeout 超时时间（毫秒）
     * @param signal 中断信号
     * @param onProgress 可选的进度回调函数
     * @returns 命令输出
     */
    private async executeWithTimeout(
        command: string,
        timeout: number = this.commandTimeout,
        signal?: AbortSignal,
        onProgress?: (progress: string) => void
    ): Promise<string> {
        return new Promise((resolve, reject) => {
            // 检查是否已中断
            if (signal?.aborted) {
                reject(new AbortError());
                return;
            }

            const timeoutId = setTimeout(() => {
                reject(new Error(`命令执行超时 (${timeout}ms): ${command}`));
            }, timeout);

            // 监听中断信号
            const abortHandler = () => {
                clearTimeout(timeoutId);
                reject(new AbortError());
            };

            if (signal) {
                signal.addEventListener('abort', abortHandler);
            }

            executeCommand(command, signal, onProgress)
                .then(output => {
                    clearTimeout(timeoutId);
                    if (signal) {
                        signal.removeEventListener('abort', abortHandler);
                    }
                    resolve(output);
                })
                .catch(error => {
                    clearTimeout(timeoutId);
                    if (signal) {
                        signal.removeEventListener('abort', abortHandler);
                    }
                    reject(error);
                });
        });
    }

    /**
     * 设置命令超时时间
     * @param timeout 超时时间（毫秒）
     */
    setCommandTimeout(timeout: number): void {
        if (timeout > 0) {
            this.commandTimeout = timeout;
        }
    }

    /**
     * 获取当前命令超时时间
     * @returns 超时时间（毫秒）
     */
    getCommandTimeout(): number {
        return this.commandTimeout;
    }
}

// 导出单例实例
export const regexProcessor = RegexProcessor.getInstance();

// 导出便捷函数
export async function generateColorRegex(colorValue: string, rangeValue?: number): Promise<TtCommandResult> {
    return regexProcessor.generateColorRegex(colorValue, rangeValue);
}

export async function executeBatchOperation(
    nameList: string,
    firstInterval?: number,
    otherInterval?: number,
    signal?: AbortSignal,
    onProgress?: (progress: string) => void
): Promise<BatchOperationResult> {
    return regexProcessor.executeBatchOperation(nameList, firstInterval, otherInterval, signal, onProgress);
}
