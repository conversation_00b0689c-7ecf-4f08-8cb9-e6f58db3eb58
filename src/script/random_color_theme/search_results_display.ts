/**
 * 搜索结果展示组件
 * 负责结果窗口的创建、target分组显示、批量操作等UI交互功能
 */

import { GroupedSearchResults, ColorSearchResult, searchColorRange } from './color_range_search';
import { messageError, messageSuccess, messageLoading } from '../prompt_message';
import { resetColorPickerState } from './color_picker_core';
import { executeCommand } from '../terminal_commands';

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @returns 是否复制成功
 */
async function copyToClipboard(text: string): Promise<boolean> {
    try {
        if (navigator.clipboard && window.isSecureContext) {
            // 使用现代 Clipboard API
            await navigator.clipboard.writeText(text);
            return true;
        } else {
            // 降级到传统方法
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            const successful = document.execCommand('copy');
            document.body.removeChild(textArea);
            return successful;
        }
    } catch (error) {
        console.error('复制到剪贴板失败:', error);
        return false;
    }
}

/**
 * 搜索结果展示管理器
 */
export class SearchResultsDisplay {
    private static instance: SearchResultsDisplay;
    private currentResultsWindow: HTMLElement | null = null;
    private lastClickTime: number = 0;
    private readonly DEBOUNCE_DELAY = 2000; // 2秒防抖延迟
    private currentAbortController: AbortController | null = null;
    private originalSearchColor: string = ''; // 存储原始搜索的颜色值
    private currentRangeValue: number = 5; // 当前的偏差范围值

    private constructor() { }

    /**
     * 获取单例实例
     */
    static getInstance(): SearchResultsDisplay {
        if (!SearchResultsDisplay.instance) {
            SearchResultsDisplay.instance = new SearchResultsDisplay();
        }
        return SearchResultsDisplay.instance;
    }

    /**
     * 检查是否可以点击（防抖）
     * @returns 是否可以点击
     */
    private canClick(): boolean {
        const now = Date.now();
        if (now - this.lastClickTime < this.DEBOUNCE_DELAY) {
            console.log('点击过于频繁，请稍后再试');
            return false;
        }
        this.lastClickTime = now;
        return true;
    }

    /**
     * 显示搜索结果
     * @param results 按target分组的搜索结果
     * @param originalColor 原始搜索的颜色值
     */
    showResults(results: GroupedSearchResults, originalColor: string): void {
        try {
            // 存储原始搜索颜色和重置偏差范围
            this.originalSearchColor = originalColor;
            this.currentRangeValue = 5; // 重置为默认值

            // 关闭取色器（如果正在运行）
            if (window._colorPickerActive) {
                resetColorPickerState();
            }

            // 关闭之前的结果窗口
            this.closeCurrentWindow();

            // 创建结果窗口
            this.currentResultsWindow = this.createResultsWindow(results, originalColor);

            // 添加到页面
            document.body.appendChild(this.currentResultsWindow);

            // 添加ESC键关闭事件
            this.setupKeyboardEvents();

        } catch (error) {
            console.error('显示搜索结果失败:', error);
            // 不显示错误消息，保持静默
        }
    }

    /**
     * 创建结果窗口 - 采用设备选择界面风格
     * @param results 搜索结果
     * @param originalColor 原始颜色值
     * @returns 结果窗口元素
     */
    private createResultsWindow(results: GroupedSearchResults, originalColor: string): HTMLElement {
        const overlay = document.createElement('div');
        overlay.className = 'color-search-results-overlay';

        const container = document.createElement('div');
        container.className = 'color-search-results-container';

        // 创建固定头部（标题 + 关闭按钮）
        const header = document.createElement('div');
        header.className = 'color-search-results-header';

        const title = this.createTitle(originalColor);
        const closeBtn = this.createCloseButton();

        header.appendChild(title);
        header.appendChild(closeBtn);
        container.appendChild(header);

        // 创建可滚动内容区域
        const content = document.createElement('div');
        content.className = 'color-search-results-content';

        // 创建target分组按钮
        const targetSections = this.createTargetSections(results);
        targetSections.forEach(section => content.appendChild(section));

        container.appendChild(content);

        overlay.appendChild(container);

        // 点击遮罩关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                this.closeCurrentWindow();
            }
        });

        return overlay;
    }

    /**
     * 创建标题 - 采用设备选择界面风格
     * @param originalColor 原始颜色值
     * @returns 标题元素
     */
    private createTitle(originalColor: string): HTMLElement {
        const title = document.createElement('div');
        title.className = 'color-search-results-title';
        title.textContent = `颜色范围搜索结果 - ${originalColor}`;
        return title;
    }

    /**
     * 创建关闭按钮 - 采用设备选择界面风格
     * @returns 关闭按钮元素
     */
    private createCloseButton(): HTMLElement {
        const closeBtn = document.createElement('button');
        closeBtn.textContent = '关闭';
        closeBtn.className = 'color-search-results-close';
        closeBtn.addEventListener('click', () => this.closeCurrentWindow());
        return closeBtn;
    }

    /**
     * 创建target分组 - 采用设备选择界面风格
     * @param results 搜索结果
     * @returns target分组元素数组
     */
    private createTargetSections(results: GroupedSearchResults): HTMLElement[] {
        const sections: HTMLElement[] = [];

        // 按target分组显示
        const targets = Object.keys(results).sort();

        for (const target of targets) {
            const targetSection = this.createTargetSection(target, results[ target ]);
            sections.push(targetSection);
        }

        return sections;
    }

    /**
     * 创建target分组区域 - 采用设备选择界面风格，添加直接执行按钮
     * @param target target名称
     * @param colors 该target下的颜色列表
     * @returns target区域元素
     */
    private createTargetSection(target: string, colors: ColorSearchResult[]): HTMLElement {
        const section = document.createElement('div');
        section.className = 'color-search-target-section';
        section.setAttribute('data-target', target);

        // 创建target信息按钮
        const header = document.createElement('button');
        header.className = 'color-search-target-header';
        header.addEventListener('click', async () => {
            // 添加加载状态指示
            const hint = section.querySelector('.color-search-target-hint') as HTMLElement;
            const originalText = hint?.textContent || '点击展开';
            if (hint) {
                hint.textContent = '加载中...';
            }

            try {
                await this.toggleTargetExpansion(section, colors);
            } catch (error) {
                console.error('展开颜色列表失败:', error);
                if (hint) {
                    hint.textContent = originalText;
                }
            }
        });

        const title = document.createElement('span');
        title.textContent = `${target} (${colors.length}个颜色)`;
        title.className = 'color-search-target-title';

        const clickHint = document.createElement('span');
        clickHint.textContent = '点击展开';
        clickHint.className = 'color-search-target-hint';

        header.appendChild(title);
        header.appendChild(clickHint);
        section.appendChild(header);

        // 分析颜色类型（添加防御性检查）
        const colorValues = colors.filter(c => c.data && c.data.name && !c.data.name.includes('.png'));
        const imageFiles = colors.filter(c => c.data && c.data.name && c.data.name.includes('.png'));

        // 创建颜色值复制按钮容器
        if (colorValues.length > 0) {
            const colorActionContainer = document.createElement('div');
            colorActionContainer.className = 'color-search-action-container';

            const colorLabel = document.createElement('div');
            colorLabel.className = 'color-search-action-label';

            // 只显示总数量
            colorLabel.innerHTML = `颜色值 (${colorValues.length}个)`;
            colorActionContainer.appendChild(colorLabel);

            const colorButtonsContainer = document.createElement('div');
            colorButtonsContainer.className = 'color-search-buttons-row';

            const colorButtons = this.createCopyButtonGroup(target, colorValues, 'color');
            colorButtons.forEach(btn => colorButtonsContainer.appendChild(btn));

            colorActionContainer.appendChild(colorButtonsContainer);
            section.appendChild(colorActionContainer);
        }

        // 创建图片复制按钮容器
        if (imageFiles.length > 0) {
            const imageActionContainer = document.createElement('div');
            imageActionContainer.className = 'color-search-action-container';

            const imageLabel = document.createElement('div');
            imageLabel.className = 'color-search-action-label';

            // 只显示总数量
            imageLabel.innerHTML = `图片 (${imageFiles.length}个)`;
            imageActionContainer.appendChild(imageLabel);

            const imageButtonsContainer = document.createElement('div');
            imageButtonsContainer.className = 'color-search-buttons-row';

            const imageButtons = this.createCopyButtonGroup(target, imageFiles, 'image');
            imageButtons.forEach(btn => imageButtonsContainer.appendChild(btn));

            imageActionContainer.appendChild(imageButtonsContainer);
            section.appendChild(imageActionContainer);
        }

        return section;
    }

    /**
     * 创建二分查找按钮组
     * @param target target名称
     * @param items 项目列表
     * @param type 按钮类型
     * @returns 按钮元素数组
     */
    private createCopyButtonGroup(target: string, items: ColorSearchResult[], type: 'color' | 'image'): HTMLElement[] {
        const buttons: HTMLElement[] = [];
        const totalCount = items.length;

        // 如果只有1个项目，直接创建单个按钮
        if (totalCount === 1) {
            const singleButton = this.createSingleCopyButton(
                `全部`,
                type,
                () => this.handleCopyCommand(target, items, type),
                true
            );
            buttons.push(singleButton);
            return buttons;
        }

        // 初始化二分查找状态
        const binarySearchState = {
            allItems: items,
            currentItems: items,
            searchHistory: [] as Array<{ range: string, items: ColorSearchResult[], result?: 'yes' | 'no' }>,
            isSearching: false
        };

        // 创建初始的"全部"按钮，容器将在需要时动态创建
        const initialButton = this.createBinarySearchButton(
            `全部`,
            type,
            items,
            binarySearchState,
            target
        );

        buttons.push(initialButton);
        return buttons;
    }




    /**
     * 创建二分查找按钮
     * @param text 按钮文字
     * @param type 按钮类型
     * @param items 当前项目列表
     * @param searchState 二分查找状态
     * @param target target名称
     * @returns 按钮元素
     */
    private createBinarySearchButton(
        text: string,
        type: 'color' | 'image',
        items: ColorSearchResult[],
        searchState: any,
        target: string
    ): HTMLElement {
        const button = document.createElement('button');
        button.className = `color-search-action-btn color-search-action-${type} binary-search-btn`;
        button.textContent = text;

        button.addEventListener('click', async () => {
            // 防抖检查
            if (!this.canClick()) {
                return;
            }

            // 添加点击过的样式
            button.classList.add('clicked');

            // 执行tt命令（二分查找过程中直接执行，不复制到剪贴板）
            await this.handleExecuteCommand(target, items, type);

            // 如果不是在查找过程中，开始二分查找
            if (!searchState.isSearching && items.length > 1) {
                // 找到按钮的父容器（按钮行容器）
                const buttonContainer = button.parentElement;
                if (buttonContainer) {
                    this.startBinarySearch(items, searchState, buttonContainer, target, type, button);
                }
            }
        });

        return button;
    }

    /**
     * 创建单个复制按钮
     * @param text 按钮文字
     * @param type 按钮类型
     * @param onClick 点击事件
     * @param isFullCopy 是否为完整复制按钮
     * @returns 按钮元素
     */
    private createSingleCopyButton(text: string, type: 'color' | 'image', onClick: () => void, isFullCopy: boolean = false): HTMLElement {
        const button = document.createElement('button');
        let className = `color-search-action-btn color-search-action-${type}`;
        if (isFullCopy) {
            className += ' full-copy';
        }
        button.className = className;
        button.textContent = text;
        button.addEventListener('click', () => {
            // 添加点击过的样式
            button.classList.add('clicked');
            // 执行原始点击处理
            onClick();
        });
        return button;
    }

    /**
     * 切换target展开状态 - 采用设备选择界面风格，支持大列表异步渲染
     * @param section target区域元素
     * @param colors 颜色列表
     */
    private async toggleTargetExpansion(section: HTMLElement, colors: ColorSearchResult[]): Promise<void> {
        let colorList = section.querySelector('.color-search-color-list') as HTMLElement;

        if (!colorList) {
            // 首次展开，创建颜色列表
            colorList = document.createElement('div');
            colorList.className = 'color-search-color-list';

            // 先添加到DOM中，然后异步渲染内容
            section.appendChild(colorList);

            // 如果列表很大，使用分批渲染避免阻塞UI
            if (colors.length > 100) {
                await this.renderColorListBatch(colorList, colors);
            } else {
                // 小列表直接渲染
                for (const colorResult of colors) {
                    const colorItem = this.createColorItem(colorResult);
                    colorList.appendChild(colorItem);
                }
            }
        }

        // 切换展开状态
        colorList.classList.toggle('expanded');

        // 更新提示文字
        const hint = section.querySelector('.color-search-target-hint') as HTMLElement;
        if (hint) {
            hint.textContent = colorList.classList.contains('expanded') ? '点击收起' : '点击展开';
        }
    }

    /**
     * 分批渲染颜色列表，避免大列表阻塞UI
     * @param colorList 颜色列表容器
     * @param colors 颜色数据
     */
    private async renderColorListBatch(colorList: HTMLElement, colors: ColorSearchResult[]): Promise<void> {
        const batchSize = 50; // 每批渲染50个项目
        let index = 0;

        const renderBatch = () => {
            return new Promise<void>((resolve) => {
                const endIndex = Math.min(index + batchSize, colors.length);

                // 渲染当前批次
                for (let i = index; i < endIndex; i++) {
                    const colorItem = this.createColorItem(colors[ i ]);
                    colorList.appendChild(colorItem);
                }

                index = endIndex;

                if (index < colors.length) {
                    // 还有更多项目，使用requestAnimationFrame异步渲染下一批
                    requestAnimationFrame(() => {
                        renderBatch().then(resolve);
                    });
                } else {
                    // 全部渲染完成
                    resolve();
                }
            });
        };

        await renderBatch();
    }

    /**
     * 创建颜色项 - 采用设备选择界面风格，支持点击复制
     * @param colorResult 颜色结果
     * @returns 颜色项元素
     */
    private createColorItem(colorResult: ColorSearchResult): HTMLElement {
        const item = document.createElement('div');
        item.className = 'color-search-color-item';

        // 颜色预览
        const colorPreview = document.createElement('div');
        colorPreview.className = 'color-search-color-preview';
        colorPreview.style.backgroundColor = colorResult.color;

        // 颜色信息
        const colorInfo = document.createElement('div');
        colorInfo.className = 'color-search-color-info';

        const colorValue = document.createElement('span');
        colorValue.textContent = colorResult.color;
        colorValue.className = 'color-search-color-value clickable';
        colorValue.title = '点击复制颜色值';

        const colorName = document.createElement('span');
        colorName.textContent = colorResult.data?.name || '未知名称';
        colorName.className = 'color-search-color-name clickable';
        colorName.title = '点击复制资源名称';

        // 为颜色值添加点击复制功能
        colorValue.addEventListener('click', async (e) => {
            e.stopPropagation();
            try {
                await copyToClipboard(colorResult.color);
                messageSuccess('复制颜色值成功', 1000)
            } catch (error) {
                console.error('复制颜色值失败:', error);
            }
        });

        // 为资源名称添加点击复制功能
        colorName.addEventListener('click', async (e) => {
            e.stopPropagation();
            try {
                const resourceName = colorResult.data?.name;
                if (resourceName) {
                    await copyToClipboard(resourceName);
                    messageSuccess('复制资源名称成功', 1000)
                }
            } catch (error) {
                console.error('复制资源名称失败:', error);
            }
        });

        colorInfo.appendChild(colorValue);
        colorInfo.appendChild(colorName);

        item.appendChild(colorPreview);
        item.appendChild(colorInfo);

        return item;
    }

    // 已删除未使用的 handleTargetClick 方法

    /**
     * 处理复制命令到剪贴板
     * @param target target名称
     * @param colors 颜色列表
     * @param type 执行类型
     */
    private async handleCopyCommand(_target: string, colors: ColorSearchResult[], _type: 'color' | 'image'): Promise<void> {
        try {
            // 收集name值（添加防御性检查）
            const validColors = colors.filter(color => color.data && color.data.name);

            if (validColors.length === 0) {
                console.warn('没有找到有效的项目名称');
                return;
            }

            // 根据值的数量构建不同的命令格式
            let command: string;
            if (validColors.length === 1) {
                // 只有一个值时使用 tt 值 格式
                command = `tt ${validColors[ 0 ].data.name}`;
            } else {
                // 多个值时使用原有格式
                const nameList = validColors.map(color => color.data.name).join(',');
                command = `tt "${nameList}"`;
            }

            // 复制到剪贴板（静默操作，不显示消息提示）
            const success = await copyToClipboard(command);

            if (success) {
                console.log(`已复制命令到剪贴板: ${command}`);
            } else {
                console.error('复制失败，命令:', command);
            }

        } catch (error) {
            console.error('复制命令失败:', error);
        }
    }

    /**
     * 直接执行tt命令（不复制到剪贴板）
     * @param target target名称
     * @param colors 颜色列表
     * @param type 执行类型
     */
    private async handleExecuteCommand(_target: string, colors: ColorSearchResult[], _type: 'color' | 'image'): Promise<void> {
        try {
            // 收集name值（添加防御性检查）
            const validColors = colors.filter(color => color.data && color.data.name);

            if (validColors.length === 0) {
                console.warn('没有找到有效的项目名称');
                return;
            }

            // 根据值的数量构建不同的命令格式
            let command: string;
            if (validColors.length === 1) {
                // 只有一个值时使用 tt 值 格式
                command = `tt ${validColors[ 0 ].data.name}`;
            } else {
                // 多个值时使用原有格式
                const nameList = validColors.map(color => color.data.name).join(',');
                command = `tt "${nameList}"`;
            }

            console.log(`正在执行命令: ${command}`);

            // 直接执行命令
            const result = await executeCommand(command);
            console.log(`命令执行完成: ${command}`);
            console.log(`执行结果: ${result}`);

        } catch (error) {
            console.error('执行命令失败:', error);
        }
    }

    /**
     * 开始二分查找流程
     * @param items 当前项目列表
     * @param searchState 查找状态
     * @param container 容器元素
     * @param target target名称
     * @param type 按钮类型
     * @param originalButton 原始按钮
     */
    private startBinarySearch(
        items: ColorSearchResult[],
        searchState: any,
        container: HTMLElement,
        target: string,
        type: 'color' | 'image',
        originalButton: HTMLElement
    ): void {
        searchState.isSearching = true;
        searchState.currentItems = items;

        // 初始化时，用户已经测试了全部范围并确认目标在其中
        // 现在开始二分：测试前一半
        const midPoint = Math.floor(items.length / 2);
        searchState.currentTestItems = items.slice(0, midPoint);
        searchState.remainingItems = items.slice(midPoint);

        // 隐藏原始按钮
        originalButton.style.display = 'none';

        // 执行第一次二分测试（测试前一半）
        this.handleExecuteCommand(target, searchState.currentTestItems, type);

        // 创建二分查找控制面板
        this.createBinarySearchPanel(searchState, container, target, type, originalButton);
    }

    /**
     * 更新标题显示测试次数
     * @param searchState 查找状态
     * @param container 按钮容器
     */
    private updateTitleWithTestCount(searchState: any, container: HTMLElement): void {
        // 计算剩余测试次数
        const totalRemaining = searchState.currentItems.length;
        const remainingTests = Math.ceil(Math.log2(totalRemaining));

        // 从按钮容器向上查找对应的 action-container，然后找到其中的标题
        let actionContainer: Element | null = container.closest('.color-search-action-container');
        if (!actionContainer && container.parentElement) {
            // 如果没找到，可能容器就是 buttons-row，向上查找父容器
            actionContainer = container.parentElement.closest('.color-search-action-container');
        }

        if (actionContainer) {
            const label = actionContainer.querySelector('.color-search-action-label') as HTMLElement;
            if (label) {
                const originalText = label.textContent || '';

                // 移除之前的测试次数信息
                const baseText = originalText.replace(/\s*\(还需测试\d+次\)/, '');

                // 添加新的测试次数信息
                label.textContent = `${baseText} (还需测试${remainingTests}次)`;
            }
        }
    }

    /**
     * 创建二分查找控制面板
     * @param searchState 查找状态
     * @param container 容器元素
     * @param target target名称
     * @param type 按钮类型
     * @param originalButton 原始按钮
     */
    private createBinarySearchPanel(
        searchState: any,
        container: HTMLElement,
        target: string,
        type: 'color' | 'image',
        originalButton: HTMLElement
    ): void {
        // 清除之前的二分查找按钮
        const existingButtons = container.querySelectorAll('.binary-action-yes, .binary-action-no, .binary-action-cancel');
        existingButtons.forEach(button => button.remove());

        // 更新标题显示测试次数
        this.updateTitleWithTestCount(searchState, container);

        // 创建"在其中"和"不在其中"按钮，直接添加到容器
        const yesButton = this.createBinaryActionButton('在其中', 'yes', searchState, container, target, type, originalButton);
        const noButton = this.createBinaryActionButton('不在其中', 'no', searchState, container, target, type, originalButton);
        const cancelButton = this.createBinaryActionButton('取消查找', 'cancel', searchState, container, target, type, originalButton);

        container.appendChild(yesButton);
        container.appendChild(noButton);
        container.appendChild(cancelButton);
    }

    /**
     * 创建二分查找操作按钮
     * @param text 按钮文字
     * @param action 操作类型
     * @param searchState 查找状态
     * @param container 容器元素
     * @param target target名称
     * @param type 按钮类型
     * @param originalButton 原始按钮
     * @returns 按钮元素
     */
    private createBinaryActionButton(
        text: string,
        action: 'yes' | 'no' | 'cancel',
        searchState: any,
        container: HTMLElement,
        target: string,
        type: 'color' | 'image',
        originalButton: HTMLElement
    ): HTMLElement {
        const button = document.createElement('button');
        button.className = `color-search-action-btn binary-action-${action}`;
        button.textContent = text;

        button.addEventListener('click', () => {
            // 防抖检查
            if (!this.canClick()) {
                return;
            }
            this.handleBinarySearchAction(action, searchState, container, target, type, originalButton);
        });

        return button;
    }

    /**
     * 处理二分查找操作
     * @param action 操作类型
     * @param searchState 查找状态
     * @param container 容器元素
     * @param target target名称
     * @param type 按钮类型
     * @param originalButton 原始按钮
     */
    private async handleBinarySearchAction(
        action: 'yes' | 'no' | 'cancel',
        searchState: any,
        container: HTMLElement,
        target: string,
        type: 'color' | 'image',
        originalButton: HTMLElement
    ): Promise<void> {
        if (action === 'cancel') {
            // 取消查找，恢复原始状态
            this.resetBinarySearch(searchState, container, originalButton);
            return;
        }

        const currentItems = searchState.currentItems;

        if (currentItems.length <= 1) {
            // 已经找到最终结果
            if (action === 'yes') {
                messageSuccess(`找到目标值: ${currentItems[ 0 ].data.name}`, 3000);
                // 最终结果复制到剪贴板
                await this.handleCopyCommand(target, currentItems, type);
            }
            this.resetBinarySearch(searchState, container, originalButton);
            return;
        }

        // 获取当前正在测试的范围和剩余范围
        let nextItems: ColorSearchResult[];

        if (searchState.currentTestItems) {
            // 如果有明确的测试范围，用户反馈是针对这个测试范围
            if (action === 'yes') {
                // 目标在测试范围中，继续在测试范围中查找
                nextItems = searchState.currentTestItems;
            } else {
                // 目标不在测试范围中，在剩余范围中查找
                nextItems = searchState.remainingItems;
            }
        } else {
            // 初始状态，用户反馈是针对全部范围
            if (action === 'yes') {
                // 目标在全部范围中，二分查找
                const midPoint = Math.floor(currentItems.length / 2);
                nextItems = currentItems.slice(0, midPoint);
            } else {
                // 用户选择"不在其中"，说明当前搜索结果中没有目标值
                // 需要重新搜索并增加偏差范围
                await this.handleExpandSearchRange(searchState, container, target, type, originalButton);
                return;
            }
        }

        searchState.currentItems = nextItems;

        if (nextItems.length === 1) {
            // 只剩一个项目，直接测试
            messageLoading(`最后一个候选值，正在测试...`);
            // 最后一个候选值时执行命令（不复制到剪贴板，等待用户确认）
            await this.handleExecuteCommand(target, nextItems, type);
            this.createFinalConfirmPanel(searchState, container, target, type, originalButton);
            messageSuccess('所有值已测试完毕，请确认是否为最终目标值');
        } else {
            // 继续二分查找，准备测试下一半
            const midPoint = Math.floor(nextItems.length / 2);
            const testItems = nextItems.slice(0, midPoint);
            const remainingItems = nextItems.slice(midPoint);

            // 设置测试状态
            searchState.currentTestItems = testItems;
            searchState.remainingItems = remainingItems;

            // 执行测试命令（直接执行，不复制到剪贴板）
            await this.handleExecuteCommand(target, testItems, type);
            this.createBinarySearchPanel(searchState, container, target, type, originalButton);
        }
    }

    /**
     * 创建最终确认面板
     * @param searchState 查找状态
     * @param container 容器元素
     * @param target target名称
     * @param type 按钮类型
     * @param originalButton 原始按钮
     */
    private createFinalConfirmPanel(
        searchState: any,
        container: HTMLElement,
        target: string,
        type: 'color' | 'image',
        originalButton: HTMLElement
    ): void {
        // 清除之前的二分查找按钮
        const existingButtons = container.querySelectorAll('.binary-action-yes, .binary-action-no, .binary-action-cancel');
        existingButtons.forEach(button => button.remove());

        const yesButton = document.createElement('button');
        yesButton.className = 'color-search-action-btn binary-action-yes';
        yesButton.textContent = '是的，就是这个';
        yesButton.addEventListener('click', async () => {
            // 防抖检查
            if (!this.canClick()) {
                return;
            }
            messageSuccess(`找到目标值: ${searchState.currentItems[ 0 ].data.name}`, 3000);
            // 最终确认后复制到剪贴板
            await this.handleCopyCommand(target, searchState.currentItems, type);
            this.resetBinarySearch(searchState, container, originalButton);
        });

        const noButton = document.createElement('button');
        noButton.className = 'color-search-action-btn binary-action-no';
        noButton.textContent = '不是，重新开始';
        noButton.addEventListener('click', () => {
            // 防抖检查
            if (!this.canClick()) {
                return;
            }
            // 重新开始查找
            searchState.currentItems = searchState.allItems;
            searchState.isSearching = false;
            this.resetBinarySearch(searchState, container, originalButton);
        });

        container.appendChild(yesButton);
        container.appendChild(noButton);
    }

    /**
     * 恢复原始标题
     * @param container 按钮容器
     */
    private restoreOriginalTitle(container: HTMLElement): void {
        // 从按钮容器向上查找对应的 action-container，然后找到其中的标题
        let actionContainer: Element | null = container.closest('.color-search-action-container');
        if (!actionContainer && container.parentElement) {
            // 如果没找到，可能容器就是 buttons-row，向上查找父容器
            actionContainer = container.parentElement.closest('.color-search-action-container');
        }

        if (actionContainer) {
            const label = actionContainer.querySelector('.color-search-action-label') as HTMLElement;
            if (label) {
                const currentText = label.textContent || '';
                // 移除测试次数信息，恢复原始标题
                const originalText = currentText.replace(/\s*\(还需测试\d+次\)/, '');
                label.textContent = originalText;
            }
        }
    }

    /**
     * 处理扩展搜索范围
     * @param searchState 查找状态
     * @param container 容器元素
     * @param target target名称
     * @param type 类型
     * @param originalButton 原始按钮
     */
    private async handleExpandSearchRange(
        searchState: any,
        container: HTMLElement,
        target: string,
        type: 'color' | 'image',
        originalButton: HTMLElement
    ): Promise<void> {
        try {
            // 增加偏差范围
            this.currentRangeValue += 2;

            messageLoading(`当前搜索结果中未找到目标值，正在扩大搜索范围到 ${this.currentRangeValue}...`);

            // 重新搜索颜色
            const newResults = await searchColorRange(this.originalSearchColor, this.currentRangeValue);

            if (!newResults || Object.keys(newResults).length === 0) {
                messageError(`扩大搜索范围到 ${this.currentRangeValue} 后仍未找到匹配的颜色`);
                this.resetBinarySearch(searchState, container, originalButton);
                return;
            }

            // 找到对应target的新结果
            const newTargetResults = newResults[ target ];
            if (!newTargetResults || newTargetResults.length === 0) {
                messageError(`在 ${target} 中未找到新的匹配颜色`);
                this.resetBinarySearch(searchState, container, originalButton);
                return;
            }

            // 更新搜索状态
            searchState.allItems = newTargetResults;
            searchState.currentItems = newTargetResults;
            searchState.currentTestItems = null;
            searchState.remainingItems = [];
            searchState.searchHistory = [];

            const totalMatches = Object.values(newResults).reduce((sum, arr) => sum + arr.length, 0);
            messageSuccess(`扩大搜索范围成功！找到 ${totalMatches} 个匹配颜色，开始新的二分查找`);

            // 重新开始二分查找
            this.startBinarySearch(newTargetResults, searchState, container, target, type, originalButton);

        } catch (error) {
            console.error('扩展搜索范围失败:', error);
            messageError(`扩展搜索范围失败: ${error instanceof Error ? error.message : '未知错误'}`);
            this.resetBinarySearch(searchState, container, originalButton);
        }
    }

    /**
     * 重置二分查找状态
     * @param searchState 查找状态
     * @param container 容器元素
     * @param originalButton 原始按钮
     */
    private resetBinarySearch(
        searchState: any,
        container: HTMLElement,
        originalButton: HTMLElement
    ): void {
        // 清除二分查找按钮
        const existingButtons = container.querySelectorAll('.binary-action-yes, .binary-action-no, .binary-action-cancel');
        existingButtons.forEach(button => button.remove());

        // 恢复原始标题
        this.restoreOriginalTitle(container);

        // 恢复原始按钮
        originalButton.style.display = '';
        originalButton.classList.remove('clicked');

        // 重置状态
        searchState.isSearching = false;
        searchState.currentItems = searchState.allItems;
        searchState.searchHistory = [];
    }


    /**
     * 设置键盘事件
     */
    private setupKeyboardEvents(): void {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Escape') {
                // 如果取色器正在运行，优先让取色器处理ESC键
                if (window._colorPickerActive) {
                    return; // 不处理，让取色器的ESC处理器处理
                }

                // 取色器未运行时，关闭搜索结果窗口
                this.closeCurrentWindow();
                document.removeEventListener('keydown', handleKeyDown);
            }
        };
        document.addEventListener('keydown', handleKeyDown);
    }

    /**
     * 关闭当前窗口
     */
    closeCurrentWindow(): void {
        if (this.currentResultsWindow) {
            document.body.removeChild(this.currentResultsWindow);
            this.currentResultsWindow = null;
        }

        // 中断正在进行的操作
        if (this.currentAbortController) {
            this.currentAbortController.abort();
            this.currentAbortController = null;
        }
    }
}

// 导出单例实例
export const searchResultsDisplay = SearchResultsDisplay.getInstance();

// 导出便捷函数
export function showSearchResults(results: GroupedSearchResults, originalColor: string): void {
    searchResultsDisplay.showResults(results, originalColor);
}

export function closeSearchResults(): void {
    searchResultsDisplay.closeCurrentWindow();
}
