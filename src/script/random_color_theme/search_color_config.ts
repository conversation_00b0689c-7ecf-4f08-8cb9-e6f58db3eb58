/**
 * 搜索颜色工具配置管理模块
 * 负责用户权限检查、模块状态配置的加载和保存
 */

import { join } from '@tauri-apps/api/path';
import { exists } from "@tauri-apps/plugin-fs";
import { get_the_root_directory_path } from '../get_root_dir';
import { invoke } from '@tauri-apps/api/core';

// 全局变量：保存模块状态的修改（键值对形式）
export let moduleStatusModifications: { [ key: string ]: boolean } = {};
export let hasModifications = false;

/**
 * 设置模块状态修改
 */
export function setModuleStatusModifications(modifications: { [ key: string ]: boolean }): void {
    moduleStatusModifications = modifications;
}

/**
 * 标记有修改
 */
export function markHasModifications(): void {
    hasModifications = true;
}

/**
 * 重置修改标记
 */
export function resetModifications(): void {
    hasModifications = false;
}

/**
 * 获取用户名
 */
export async function getUserName(): Promise<string> {
    try {
        const { readTextFile } = await import('@tauri-apps/plugin-fs');
        const cachePath = await join(await get_the_root_directory_path('cache'), 'cache.json');

        if (await exists(cachePath)) {
            const cacheContent = await readTextFile(cachePath);
            const cache = JSON.parse(cacheContent);
            return cache.username || '';
        }
        return '';
    } catch (error) {
        console.error('获取用户名失败:', error);
        return '';
    }
}

/**
 * 检查是否为特权用户
 */
export async function checkPrivilegedUser(): Promise<boolean> {
    try {
        const username = await getUserName();
        console.log('当前用户名:', username);
        // 暂时直接返回true以便测试特权用户功能
        return true; // username === 'zhangchuanqiang';
    } catch (error) {
        console.error('检查特权用户失败:', error);
        return true; // 错误时也返回true以便测试
    }
}

/**
 * 加载配置文件中的模块状态
 */
export async function loadModuleStatusFromConfig(): Promise<void> {
    try {
        // 使用正确的配置文件路径 - 通过Tauri命令获取
        const configPath = await invoke('获取配置文件路径', { fileName: 'random_color_theme_config.json5' }) as string;
        console.log('配置文件路径:', configPath);

        if (await exists(configPath)) {
            const { readTextFile } = await import('@tauri-apps/plugin-fs');
            const configContent = await readTextFile(configPath);
            console.log('配置文件内容预览:', configContent.substring(0, 200) + '...');

            const config = JSON.parse(configContent.replace(/\/\*[\s\S]*?\*\/|\/\/.*$/gm, ''));
            console.log('解析后的配置对象:', config);
            console.log('配置中的module字段:', config.module);
            console.log('module字段类型:', typeof config.module);
            console.log('是否为数组:', Array.isArray(config.module));

            // 优先读取配置文件中的module对象
            if (config.module && typeof config.module === 'object' && !Array.isArray(config.module)) {
                // 直接使用配置文件中的module对象，包含所有键值对（true和false）
                moduleStatusModifications = { ...config.module };
                console.log('✅ 从配置文件加载模块状态 (对象格式):', moduleStatusModifications);
                console.log('✅ 模块数量:', Object.keys(moduleStatusModifications).length);
                console.log('✅ 模块列表:', Object.keys(moduleStatusModifications));
            } else if (Array.isArray(config.module)) {
                // 如果配置文件中是数组格式，转换为对象格式（兼容旧版本）
                const moduleObj: { [ key: string ]: boolean } = {};
                config.module.forEach((moduleName: string) => {
                    moduleObj[ moduleName ] = true; // 数组中的模块默认为启用
                });

                moduleStatusModifications = { ...moduleObj };
                console.log('✅ 从配置文件数组格式转换模块状态:', moduleStatusModifications);
                console.log('✅ 模块数量:', Object.keys(moduleStatusModifications).length);
            } else {
                console.error('❌ 配置文件中没有找到有效的module配置');
                throw new Error('配置文件中没有找到有效的module配置');
            }
        } else {
            console.error('❌ 配置文件不存在:', configPath);
            throw new Error('配置文件不存在');
        }
    } catch (error) {
        console.error('❌ 加载模块状态配置失败:', error);
        console.log('🔄 使用默认模块配置');

        // 使用默认配置（包含常见的模块）
        const defaultModuleStatus: { [ key: string ]: boolean } = {
            "com.android.settings": true,
            "com.android.contacts": true,
            "com.android.mms": true,
            "com.android.systemui": true,
            "miui.systemui.plugin": true,
            "com.miui.notification": true,
            "com.miui.securitycenter": true,
            "android": true,
            "com.miui.home": true,
            "com.miui.fliphome": false // 默认不启用这个模块
        };

        moduleStatusModifications = { ...defaultModuleStatus };
        console.log('✅ 使用默认模块状态:', moduleStatusModifications);
        console.log('✅ 默认模块数量:', Object.keys(moduleStatusModifications).length);
    }
}

/**
 * 保存模块状态到配置文件
 */
export async function saveModuleStatusToConfig(): Promise<void> {
    if (!hasModifications) {
        return; // 没有修改就不保存
    }

    try {
        // 使用正确的配置文件路径 - 通过Tauri命令获取
        const configPath = await invoke('获取配置文件路径', { fileName: 'random_color_theme_config.json5' }) as string;

        let config: any = {};
        if (await exists(configPath)) {
            const { readTextFile } = await import('@tauri-apps/plugin-fs');
            const configContent = await readTextFile(configPath);
            config = JSON.parse(configContent.replace(/\/\*[\s\S]*?\*\/|\/\/.*$/gm, ''));
        }

        // 更新module对象
        config.module = { ...moduleStatusModifications };

        const { writeTextFile } = await import('@tauri-apps/plugin-fs');
        await writeTextFile(configPath, JSON.stringify(config, null, 4));

        console.log('模块状态已保存到配置文件');
        hasModifications = false; // 重置修改标记
    } catch (error) {
        console.error('保存模块状态到配置文件失败:', error);
    }
}

/**
 * 获取模块状态修改对象的副本
 */
export function getModuleStatusModifications(): { [ key: string ]: boolean } {
    return { ...moduleStatusModifications };
}

/**
 * 检查是否有修改
 */
export function getHasModifications(): boolean {
    return hasModifications;
}