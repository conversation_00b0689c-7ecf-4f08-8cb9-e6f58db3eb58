/**
 * 主题工具模块
 * 提供深色模式检测和主题相关的工具函数
 */

/**
 * 检测当前是否为深色模式
 */
export function isDarkMode(): boolean {
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
}

/**
 * 监听主题变化
 */
export function onThemeChange(callback: (isDark: boolean) => void): () => void {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handler = (e: MediaQueryListEvent) => {
        callback(e.matches);
    };
    
    mediaQuery.addEventListener('change', handler);
    
    // 返回清理函数
    return () => {
        mediaQuery.removeEventListener('change', handler);
    };
}

/**
 * 获取主题相关的颜色值
 */
export function getThemeColors() {
    const isDark = isDarkMode();
    
    return {
        // 背景色
        background: {
            primary: isDark ? 'rgba(30, 30, 30, 0.95)' : 'rgba(255, 255, 255, 0.95)',
            secondary: isDark ? 'rgba(40, 40, 40, 0.8)' : 'rgba(245, 245, 247, 0.9)',
            overlay: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(0, 0, 0, 0.3)',
        },
        
        // 文字颜色
        text: {
            primary: isDark ? '#fff' : '#333',
            secondary: isDark ? '#ccc' : '#666',
            muted: isDark ? '#888' : '#999',
        },
        
        // 边框颜色
        border: {
            primary: isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.15)',
            secondary: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.06)',
            focus: '#007AFF',
        },
        
        // 按钮颜色
        button: {
            primary: isDark ? 'rgba(60, 60, 60, 0.8)' : 'rgba(235, 235, 245, 1)',
            hover: isDark ? 'rgba(70, 70, 70, 0.9)' : 'rgba(225, 225, 235, 1)',
            active: isDark ? 'rgba(50, 50, 50, 0.9)' : 'rgba(215, 215, 225, 1)',
        },
        
        // 状态颜色
        status: {
            success: '#52c759',
            warning: '#ff9500',
            error: '#ff453a',
            info: isDark ? '#5b9bff' : '#007AFF',
        }
    };
}

/**
 * 应用主题样式到元素
 */
export function applyThemeStyles(element: HTMLElement, styles: Record<string, string>): void {
    const colors = getThemeColors();
    
    Object.entries(styles).forEach(([property, value]) => {
        // 替换颜色变量
        let processedValue = value;
        
        // 简单的变量替换
        processedValue = processedValue.replace(/\$background-primary/g, colors.background.primary);
        processedValue = processedValue.replace(/\$background-secondary/g, colors.background.secondary);
        processedValue = processedValue.replace(/\$text-primary/g, colors.text.primary);
        processedValue = processedValue.replace(/\$text-secondary/g, colors.text.secondary);
        processedValue = processedValue.replace(/\$border-primary/g, colors.border.primary);
        processedValue = processedValue.replace(/\$border-secondary/g, colors.border.secondary);
        
        element.style.setProperty(property, processedValue);
    });
}

/**
 * 创建响应式主题元素
 */
export function createThemedElement(
    tagName: string, 
    className?: string, 
    styles?: Record<string, string>
): HTMLElement {
    const element = document.createElement(tagName);
    
    if (className) {
        element.className = className;
    }
    
    if (styles) {
        applyThemeStyles(element, styles);
    }
    
    // 监听主题变化并重新应用样式
    const cleanup = onThemeChange(() => {
        if (styles) {
            applyThemeStyles(element, styles);
        }
    });
    
    // 在元素被移除时清理监听器
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            mutation.removedNodes.forEach((node) => {
                if (node === element) {
                    cleanup();
                    observer.disconnect();
                }
            });
        });
    });
    
    observer.observe(document.body, { childList: true, subtree: true });
    
    return element;
}
