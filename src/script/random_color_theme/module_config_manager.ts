/**
 * 模块配置管理模块
 * 负责模块生成、事件处理和模块选择逻辑
 */

import { messageError, messageLoading } from '../prompt_message';
import { random_color_theme } from './random_color_theme';
import { invoke } from '@tauri-apps/api/core';
import { exists } from "@tauri-apps/plugin-fs";
import {
    moduleStatusModifications,
    markHasModifications,
    loadModuleStatusFromConfig
} from './search_color_config';

/**
 * 生成选中的模块
 * @param selectedModules 选中的模块列表
 */
export async function generateSelectedModules(selectedModules: string[]): Promise<void> {
    try {
        // 使用正确的配置文件路径读取原始配置
        const configPath = await invoke('获取配置文件路径', { fileName: 'random_color_theme_config.json5' }) as string;

        if (await exists(configPath)) {
            const { readTextFile } = await import('@tauri-apps/plugin-fs');
            const configContent = await readTextFile(configPath);
            const originalConfig = JSON.parse(configContent.replace(/\/\*[\s\S]*?\*\/|\/\/.*$/gm, ''));

            // 获取所有可用的模块名称
            let allModules: string[] = [];
            if (originalConfig.module) {
                if (Array.isArray(originalConfig.module)) {
                    allModules = originalConfig.module;
                } else if (typeof originalConfig.module === 'object') {
                    allModules = Object.keys(originalConfig.module);
                }
            }

            // 创建临时配置对象（不修改原始配置文件）
            const tempConfig = {
                ...originalConfig,
                module: allModules, // 确保module是数组格式供生成函数使用
                module_status: {} as { [ key: string ]: boolean }
            };

            // 将所有模块设为禁用
            allModules.forEach((moduleName: string) => {
                tempConfig.module_status[ moduleName ] = false;
            });

            // 启用选中的模块
            selectedModules.forEach(moduleName => {
                tempConfig.module_status[ moduleName ] = true;
            });

            console.log('临时配置对象:', tempConfig);
            console.log('启用的模块:', selectedModules);

            // 调用生成函数，传递临时配置
            await random_color_theme(tempConfig);

            // 生成主题后自动刷新 data.json 和页面颜色数据
            if (window.reloadDataJsonAndRefresh) {
                await window.reloadDataJsonAndRefresh();
            }
        } else {
            throw new Error('配置文件不存在');
        }

    } catch (error) {
        console.error('生成选中模块失败:', error);
        throw error;
    }
}

/**
 * 保存模块选择状态到全局变量
 * @param moduleItems 模块项目列表
 */
export function saveModuleSelections(moduleItems: NodeListOf<HTMLElement>): void {
    moduleItems.forEach(item => {
        const moduleName = item.dataset.module;
        if (moduleName) {
            const isSelected = item.classList.contains('selected');
            moduleStatusModifications[ moduleName ] = isSelected;
            markHasModifications();
        }
    });
}

/**
 * 设置生成模块的事件监听
 * @param dropdownMenu 下拉菜单元素
 */
export function setupGenerateModuleEvents(dropdownMenu: HTMLElement): void {
    const selectAllItem = dropdownMenu.querySelector('.select-all') as HTMLElement;
    const moduleItems = dropdownMenu.querySelectorAll('.scrollable-module-list .apply-theme-dropdown-item') as NodeListOf<HTMLElement>;
    const generateButton = dropdownMenu.querySelector('.apply-theme-apply-btn') as HTMLButtonElement;

    // 全选/取消全选事件
    selectAllItem?.addEventListener('click', () => {
        const isAllSelected = selectAllItem.classList.contains('selected');

        if (isAllSelected) {
            // 取消全选
            selectAllItem.classList.remove('selected');
            selectAllItem.style.background = 'rgba(0, 122, 255, 0.06)';
            selectAllItem.style.color = '#007AFF';
            selectAllItem.style.borderColor = 'rgba(0, 122, 255, 0.15)';
            moduleItems.forEach(item => item.classList.remove('selected'));
        } else {
            // 全选
            selectAllItem.classList.add('selected');
            selectAllItem.style.background = '#007AFF';
            selectAllItem.style.color = 'white';
            selectAllItem.style.borderColor = '#007AFF';
            moduleItems.forEach(item => item.classList.add('selected'));
        }

        // 保存选择状态
        saveModuleSelections(moduleItems);
    });

    // 单个模块选择事件
    moduleItems.forEach(item => {
        item.addEventListener('click', () => {
            // 切换选中状态
            item.classList.toggle('selected');

            // 检查是否所有模块都被选中
            const selectedModules = Array.from(moduleItems).filter(module => module.classList.contains('selected'));
            const allSelected = selectedModules.length === moduleItems.length;

            if (allSelected) {
                selectAllItem.classList.add('selected');
                selectAllItem.style.background = '#007AFF';
                selectAllItem.style.color = 'white';
                selectAllItem.style.borderColor = '#007AFF';
            } else {
                selectAllItem.classList.remove('selected');
                selectAllItem.style.background = 'rgba(0, 122, 255, 0.06)';
                selectAllItem.style.color = '#007AFF';
                selectAllItem.style.borderColor = 'rgba(0, 122, 255, 0.15)';
            }

            // 保存选择状态
            saveModuleSelections(moduleItems);
        });
    });

    // 生成按钮事件
    generateButton?.addEventListener('click', async () => {
        try {
            const selectedModules = Array.from(moduleItems)
                .filter(item => item.classList.contains('selected'))
                .map(item => item.dataset.module)
                .filter(Boolean) as string[];

            if (selectedModules.length === 0) {
                messageError('请至少选择一个模块');
                return;
            }

            console.log('开始生成选中模块的随机颜色主题:', selectedModules);
            messageLoading('正在检查设备连接...');

            // 智能设备检查和选择
            const deviceOk = await checkAndSelectDevice();
            if (!deviceOk) {
                return;
            }

            messageLoading(`正在生成 ${selectedModules.length} 个模块的随机颜色主题...`);

            // 调用生成函数，通过修改配置文件实现选择性生成
            await generateSelectedModules(selectedModules);

            console.log('生成随机颜色主题完成');
            // messageSuccess 已在 generateSelectedModules 或 random_color_theme 函数内部调用

            // 生成完成后更新截图显示区域和预览区域
            await updateScreenshotAndPreviewArea();

            // 生成成功后隐藏菜单
            dropdownMenu.style.display = 'none';
            const dropdownButton = dropdownMenu.parentElement?.querySelector('.apply-theme-dropdown-btn') as HTMLElement;
            if (dropdownButton) {
                dropdownButton.textContent = '▼';
                dropdownButton.classList.remove('active');
            }
            // 恢复取色器功能
            window._dropdownMenuOpen = false;

        } catch (error) {
            console.error('生成随机颜色主题失败:', error);
            messageError(`生成随机颜色主题失败: ${error}`);
            // 生成失败时也要恢复取色器功能
            window._dropdownMenuOpen = false;
        }
    });
}

/**
 * 智能设备检查和选择
 * @returns 如果设备可用返回true，否则返回false
 */
async function checkAndSelectDevice(): Promise<boolean> {
    try {
        const { getDevices } = await import('../commands');
        const devices = await getDevices();

        // 过滤出在线设备
        const onlineDevices = devices.filter(device =>
            device.status === 'device'
        );

        if (onlineDevices.length === 0) {
            messageError('未检测到连接的设备，请确保设备已连接并启用USB调试');
            return false;
        }

        // 由于random_color_theme函数已经处理了设备选择，这里只需要验证设备可用性
        console.log(`检测到 ${onlineDevices.length} 个可用设备`);
        return true;

    } catch (error) {
        console.error('设备检查失败:', error);
        messageError(`设备检查失败: ${error}`);
        return false;
    }
}

/**
 * 更新截图显示区域和预览区域
 * 在主题生成/应用完成后调用，确保UI与实际文件状态同步
 */
async function updateScreenshotAndPreviewArea(): Promise<void> {
    try {
        console.log('正在更新截图显示区域...');

        // 动态导入截图管理模块
        const { renderScreenshots } = await import('./screenshot_manager');
        await renderScreenshots();

        // 如果当前选中的截图已被删除，清除预览区域
        const { join } = await import('@tauri-apps/api/path');
        const { getPath, getCurrentSelectedScreenshot } = await import('./screenshot_manager');

        const currentSelectedScreenshot = getCurrentSelectedScreenshot();
        if (currentSelectedScreenshot) {
            const screenshotPath = await join(getPath(), currentSelectedScreenshot);
            if (!await exists(screenshotPath)) {
                console.log('当前选中的截图已被删除，清除预览区域');

                // 动态导入并设置截图选择状态
                const { setCurrentSelectedScreenshot } = await import('./screenshot_manager');
                setCurrentSelectedScreenshot(null);

                const existingPreview = document.querySelector('.central-preview-image');
                if (existingPreview) {
                    existingPreview.remove();
                }
            }
        }

        console.log('截图显示区域更新完成');
    } catch (error) {
        console.error('更新截图显示区域失败:', error);
    }
}

/**
 * 重新加载 data.json 并刷新页面颜色数据
 */
export async function reloadDataJsonAndRefresh(): Promise<void> {
    try {
        // 强制重新加载颜色数据库，确保获取最新的颜色数据
        const { initColorDatabase } = await import('./color_database');
        await initColorDatabase(true); // 强制重新加载

        // 重新加载模块状态配置
        await loadModuleStatusFromConfig();

        // 重新渲染截图和颜色数据
        const { renderScreenshots } = await import('./screenshot_manager');
        await renderScreenshots();

        // 如有其他颜色数据渲染函数，可在此补充调用
        // TODO: 若有更细粒度的颜色刷新逻辑，可补充
        console.log('data.json 已重新加载并刷新页面颜色数据');
    } catch (error) {
        console.error('重新加载 data.json 并刷新页面颜色数据失败:', error);
    }
}