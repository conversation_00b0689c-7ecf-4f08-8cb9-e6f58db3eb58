import { invoke } from '@tauri-apps/api/core';
import { join } from '@tauri-apps/api/path';
import { exists, readDir } from '@tauri-apps/plugin-fs';
import { messageError, messageLoading } from '../prompt_message';
import { get_the_root_directory_path } from '../get_root_dir';
import { 应用主题 } from '../apply/theme';


/**
 * 应用随机颜色主题 - 颜色值版本
 * @param selectedModules 选中的模块列表，如果为空则应用所有模块
 */
export async function applyColorValueTheme(selectedModules?: string[]): Promise<void> {
    try {
        messageLoading('正在应用颜色值版本主题...');

        const rootDir = await get_the_root_directory_path('random_color_theme');
        const colorThemePath = await join(rootDir, '随机颜色主题', '颜色值版本');

        if (!await exists(colorThemePath)) {
            messageError('颜色值版本主题不存在，请先生成随机颜色主题');
            return;
        }

        // 如果指定了模块，只应用选中的模块
        if (selectedModules && selectedModules.length > 0) {
            await applySelectedModules(colorThemePath, selectedModules, '颜色值版本');
        } else {
            // 应用整个主题包
            await applyFullThemeInternal(colorThemePath, '颜色值版本');
        }

    } catch (error) {
        console.error('应用颜色值版本主题失败:', error);
        messageError(`应用颜色值版本主题失败: ${error}`);
    }
}

/**
 * 应用随机颜色主题 - 图片版本
 * @param selectedModules 选中的模块列表，如果为空则应用所有模块
 */
export async function applyImageTheme(selectedModules?: string[]): Promise<void> {
    try {
        messageLoading('正在应用图片版本主题...');

        const rootDir = await get_the_root_directory_path('random_color_theme');
        const imgThemePath = await join(rootDir, '随机颜色主题', '图片版本');

        if (!await exists(imgThemePath)) {
            messageError('图片版本主题不存在，请先生成随机颜色主题');
            return;
        }

        // 如果指定了模块，只应用选中的模块
        if (selectedModules && selectedModules.length > 0) {
            await applySelectedModules(imgThemePath, selectedModules, '图片版本');
        } else {
            // 应用整个主题包
            await applyFullThemeInternal(imgThemePath, '图片版本');
        }

    } catch (error) {
        console.error('应用图片版本主题失败:', error);
        messageError(`应用图片版本主题失败: ${error}`);
    }
}

/**
 * 应用随机颜色主题 - 完整主题包（内部函数重载）
 */
async function applyFullThemeInternal(themePath: string, versionName: string): Promise<void> {
    try {
        // 直接使用主题路径
        window.dragFiles = themePath;
        window.cacheDir = themePath;

        // 应用主题，不使用缓存
        await 应用主题(themePath, undefined, `随机颜色主题-${versionName}`, undefined, false);

    } catch (error) {
        console.error(`应用${versionName}失败:`, error);
        messageError(`应用${versionName}失败: ${error}`);
    }
}

/**
 * 应用随机颜色主题 - 完整主题包
 * @param selectedModules 选中的模块列表，如果为空则应用所有模块
 */
export async function applyFullTheme(selectedModules?: string[]): Promise<void> {
    try {
        messageLoading('正在应用完整主题包...');
        const rootDir = await get_the_root_directory_path('random_color_theme');
        const themePath = await join(rootDir, '随机颜色主题', '完整主题包');

        if (!await exists(themePath)) {
            messageError('完整主题包不存在，请先生成随机颜色主题');
            return;
        }

        // 如果指定了模块，只应用选中的模块
        if (selectedModules && selectedModules.length > 0) {
            await applySelectedModules(themePath, selectedModules, '完整主题包');
        } else {
            // 应用整个主题包
            await applyFullThemeInternal(themePath, '完整主题包');
        }

    } catch (error) {
        console.error('应用完整主题包失败:', error);
        messageError(`应用完整主题包失败: ${error}`);
    }
}

/**
 * 应用选中的模块
 * @param themePath 主题路径
 * @param selectedModules 选中的模块列表
 * @param versionName 版本名称
 */
async function applySelectedModules(themePath: string, selectedModules: string[], versionName: string): Promise<void> {
    try {
        messageLoading(`正在应用${versionName}的选中模块...`);

        // 创建临时目录用于存放选中的模块
        const tempDir = await invoke<string>('获取缓存目录');
        const selectedThemeDir = await join(tempDir, `selected_${versionName.replace(/\s/g, '_')}_theme`);

        // 确保临时目录存在
        await invoke('创建目录', { path: selectedThemeDir });

        // 复制选中的模块到临时目录
        for (const moduleName of selectedModules) {
            const moduleSourcePath = await join(themePath, moduleName);
            const moduleTargetPath = await join(selectedThemeDir, moduleName);

            if (await exists(moduleSourcePath)) {
                await invoke('复制文件', {
                    from: moduleSourcePath,
                    to: moduleTargetPath
                });
                console.log(`已复制模块: ${moduleName}`);
            } else {
                console.warn(`模块不存在: ${moduleName}`);
            }
        }

        // 复制 description.xml 文件
        const descriptionSource = await join(themePath, 'description.xml');
        if (await exists(descriptionSource)) {
            const descriptionTarget = await join(selectedThemeDir, 'description.xml');
            await invoke('复制文件', {
                from: descriptionSource,
                to: descriptionTarget
            });
        }

        // 直接使用临时目录
        window.dragFiles = selectedThemeDir;
        window.cacheDir = selectedThemeDir;

        // 应用主题，不使用缓存
        await 应用主题(selectedThemeDir, undefined, `随机颜色主题-${versionName}(${selectedModules.join(', ')})`, undefined, false);

    } catch (error) {
        console.error(`应用选中模块失败:`, error);
        messageError(`应用选中模块失败: ${error}`);
    }
}

/**
 * 获取可用的模块列表
 * @param versionType 版本类型：'color' | 'image' | 'full'
 * @returns 模块名称列表
 */
export async function getAvailableModules(versionType: 'color' | 'image' | 'full'): Promise<string[]> {
    try {
        const rootDir = await get_the_root_directory_path('random_color_theme');
        let themePath: string;

        switch (versionType) {
            case 'color':
                themePath = await join(rootDir, '随机颜色主题', '颜色值版本');
                break;
            case 'image':
                themePath = await join(rootDir, '随机颜色主题', '图片版本');
                break;
            case 'full':
                themePath = await join(rootDir, '随机颜色主题', '完整主题包');
                break;
        }

        if (!await exists(themePath)) {
            return [];
        }

        const items = await readDir(themePath);
        const modules = items
            .filter(item => item.isDirectory && item.name !== null)
            .map(item => item.name!)
            .filter(name => !name.includes('description.xml')); // 排除非模块文件

        return modules;
    } catch (error) {
        console.error('获取模块列表失败:', error);
        return [];
    }
}

/**
 * 获取模块的显示名称
 * @param moduleName 模块名称
 * @returns 显示名称（直接返回原始模块名称）
 */
export function getModuleDisplayName(moduleName: string): string {
    return moduleName;
}