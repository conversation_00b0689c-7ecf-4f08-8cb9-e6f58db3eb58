/**
 * 颜色取色器核心逻辑模块
 * 负责主要的取色功能、Promise管理、状态控制和取色流程协调
 */

import { messageSuccess } from '../prompt_message';
import {
    setColorPickerLocked,
    setColorPickerLockedTimeout,
    colorPickerLockedTimeout,
    currentPlatform
} from './color_picker_config';
import {
    initColorDatabase as originalInitColorDatabase,
    getIsDatabaseLoaded,
    getColorDatabase,
    findColorByHex
} from './color_database';
import {
    updateMagnifierSize,
    cleanupUIElements,
    restoreMouseCursor,
    ColorPickerUIElements
} from './color_picker_ui';
import {
    getPixelColor
} from './magnifier_renderer';
import {
    updateMousePosition,
    cleanupEventHandlers
} from './color_picker_events';
import {
    setColorInput
} from './color_range_search_ui';

/**
 * 取色器核心配置接口
 */
export interface ColorPickerCoreConfig {
    finalTransition: string;
    globalTimeout: number;
    previewRect: DOMRect | null;
}

/**
 * 取色器Promise解析函数类型
 */
export type ColorPickerResolveFunction = (value: string | undefined) => void;

/**
 * 创建取色点击处理函数
 */
export function createPickColorHandler(
    screenCanvas: HTMLCanvasElement,
    screenCtx: CanvasRenderingContext2D,
    resultColor: { value: string | undefined },
    cleanup: () => void
) {
    return async (event: MouseEvent) => {
        try {
            // 阻止事件冒泡和默认行为
            event.preventDefault();
            event.stopPropagation();

            // 获取点击位置
            const x = event.clientX;
            const y = event.clientY;

            // 更新鼠标位置
            updateMousePosition(x, y);

            // 获取点击位置的颜色
            const pixelColor = getPixelColor(x, y, screenCanvas, screenCtx);

            // 搜索颜色
            const searchColor = pixelColor + ' 3';

            // 更新结果变量
            resultColor.value = pixelColor;
            window._lastPickedColor = pixelColor;

            // 检查是否在颜色数据库中存在此颜色
            const colorKey = pixelColor.toUpperCase();

            // 复制颜色到剪贴板，不管是否在数据库中
            try {
                await navigator.clipboard.writeText(pixelColor);
            } catch (clipErr) {
                console.error('复制颜色到剪贴板失败:', clipErr);
            }

            // 只有当数据库成功加载时才尝试查找和复制颜色资源
            if (getIsDatabaseLoaded()) {
                const colorData = findColorByHex(colorKey);

                if (colorData) {
                    // 复制颜色对应的name到剪贴板
                    try {
                        await navigator.clipboard.writeText(colorData.name);

                        if (colorData.name.includes('.png')) {
                            messageSuccess(`已复制图片：${colorData.name}`, 1500);
                        } else {
                            messageSuccess(`已复制颜色：${colorData.name}`, 1500);
                        }
                    } catch (err) {
                        console.error('复制到剪贴板失败:', err);
                        messageSuccess(`已复制颜色: ${pixelColor}`, 1500);
                    }
                } else {
                    // 如果颜色不在数据库中，将颜色值填入搜索框
                    try {
                        setColorInput(searchColor);
                    } catch (err) {
                        console.error('填充搜索框失败:', err);
                    }

                    messageSuccess(`已复制颜色: ${pixelColor}`, 1500);
                }
            } else {
                // 如果颜色数据库未加载，将颜色值填入搜索框
                try {
                    setColorInput(searchColor);
                } catch (err) {
                    console.error('填充搜索框失败:', err);
                }

                messageSuccess(`已复制颜色: ${pixelColor}`, 1500);
            }

            // 短暂锁定，避免点击后立即触发移出事件
            window._colorPickerForceLocked = true;

            // 在成功取色后，添加定时器保证解除锁定 - 使用更短的超时时间
            if (colorPickerLockedTimeout) {
                window.clearTimeout(colorPickerLockedTimeout);
            }

            setColorPickerLockedTimeout(window.setTimeout(() => {
                window._colorPickerForceLocked = false;

                // 解除锁定后，立即检查鼠标是否在预览区域外
                if (window._colorPickerSourceElement && window._colorPickerActive) {
                    const rect = window._colorPickerSourceElement.getBoundingClientRect();
                    const currentX = window._lastMouseX;
                    const currentY = window._lastMouseY;

                    // 检查鼠标是否在预览区域外
                    const isOutside =
                        currentX < rect.left ||
                        currentX > rect.right ||
                        currentY < rect.top ||
                        currentY > rect.bottom;

                    // 如果鼠标在预览区域外，立即关闭取色器
                    if (isOutside) {
                        cleanup();
                    }
                }
            }, 150)); // 缩短锁定时间到150ms，提高响应性

        } catch (error) {
            console.error('取色过程出错:', error);
            // 确保在错误情况下也解除锁定
            window._colorPickerForceLocked = false;
        }

        // 不在点击后关闭取色器，允许用户继续取色
        // 用户可以按ESC键或移出预览区域来关闭取色器
    };
}

/**
 * 创建放大镜尺寸更新函数
 */
export function createMagnifierSizeUpdater(
    magnifier: HTMLDivElement,
    isControlPressed: { value: boolean },
    drawMagnifierImpl: (x: number, y: number) => void
) {
    return function updateMagnifierSizeImpl() {
        const sizes = updateMagnifierSize(magnifier, isControlPressed.value);
        const currentMagnifierSize = sizes.currentMagnifierSize;
        const currentCaptureSize = sizes.currentCaptureSize;

        // 使用新尺寸重绘放大镜
        if (window._lastMouseX !== undefined && window._lastMouseY !== undefined) {
            drawMagnifierImpl(window._lastMouseX, window._lastMouseY);
        }

        return { currentMagnifierSize, currentCaptureSize };
    };
}

/**
 * 创建完整的取色器清理函数
 */
import { ColorPickerStateMachine, ColorPickerEvent, getColorPickerStateMachine, ColorPickerState } from './color_picker_state_machine';

export function createColorPickerCleanup(
    promiseResolve: ColorPickerResolveFunction | null,
    resultColor: { value: string | undefined },
    globalTimeout: number,
    uiElements: ColorPickerUIElements,
    eventHandlers: {
        handleMouseMove: (e: MouseEvent) => void;
        handleEscapeKey: (e: KeyboardEvent) => void;
        handleKeyDown: (event: KeyboardEvent) => void;
        handleKeyUp: (event: KeyboardEvent) => void;
        handleMouseOut: (event: MouseEvent) => void;
        startMousePolling: () => void;
    },
    eventCatcher: HTMLDivElement,
    pickColor: (event: MouseEvent) => void,
    stateMachine?: ColorPickerStateMachine
) {
    return async () => {
        // 防止重复清理
        if (!window._colorPickerActive) {
            return;
        }

        console.log('开始清理取色器...');

        // 先设置状态为非活跃，防止其他函数再次调用cleanup
        window._colorPickerActive = false;

        // 清除锁定状态
        window._colorPickerForceLocked = false;
        if (colorPickerLockedTimeout) {
            window.clearTimeout(colorPickerLockedTimeout);
            setColorPickerLockedTimeout(null);
        }

        // 清理鼠标事件处理函数
        if (typeof (eventHandlers.startMousePolling as any).cleanupHandler === 'function') {
            (eventHandlers.startMousePolling as any).cleanupHandler();
        }

        // 清除全局超时
        clearTimeout(globalTimeout);

        // 清理所有事件处理器
        cleanupEventHandlers(
            eventHandlers.handleMouseMove,
            eventHandlers.handleEscapeKey,
            eventHandlers.handleKeyDown,
            eventHandlers.handleKeyUp,
            eventHandlers.handleMouseOut,
            eventCatcher,
            pickColor,
            eventHandlers.startMousePolling
        );

        // 恢复所有元素的鼠标样式
        restoreMouseCursor();

        // 解除强制锁定
        window._colorPickerForceLocked = false;
        window._colorPickerSourceElement = null;
        window._colorPickerPreviewRect = null;

        // 移除DOM元素 - 在状态机转换之前完成
        console.log('开始移除UI元素...');
        cleanupUIElements(uiElements);

        // 验证UI元素是否已清理
        const remainingElements = document.querySelectorAll(
            '#color-picker-magnifier, #color-picker-overlay, #color-picker-click-catcher, #color-picker-info, #color-picker-result'
        );

        if (remainingElements.length > 0) {
            console.warn(`UI清理后仍有${remainingElements.length}个残留元素，强制清理`);
            for (const element of remainingElements) {
                try {
                    if (document.body.contains(element)) {
                        document.body.removeChild(element);
                    }
                } catch (error) {
                    console.error('强制清理残留元素失败:', error);
                }
            }
        }

        // 清理对自身的全局引用
        window._colorPickerCleanup = null;

        // 如果有状态机，使用状态机管理关闭流程
        if (stateMachine) {
            try {
                console.log('触发状态机关闭事件...');
                await stateMachine.executeEvent(ColorPickerEvent.DEACTIVATE);
                await stateMachine.executeEvent(ColorPickerEvent.DEACTIVATION_SUCCESS);
                console.log('状态机关闭完成');
            } catch (error) {
                console.error('状态机关闭失败，使用强制清理:', error);
                // 强制重置状态机
                await stateMachine.executeEvent(ColorPickerEvent.RESET);
            }
        }

        // 返回最后取色的结果
        if (promiseResolve) {
            promiseResolve(resultColor.value || window._lastPickedColor);
        }

        console.log('取色器清理完成');
    };
}

/**
 * 初始化颜色取色器预览区域设置
 */
export function initializePreviewArea(): { previewRect: DOMRect | null } {
    // 找到并记录预览图区域
    const centralPreviewImage = document.querySelector('.central-preview-image');
    let previewRect: DOMRect | null = null;

    if (centralPreviewImage) {
        previewRect = centralPreviewImage.getBoundingClientRect();

        // 保存中央预览图的元素引用，方便后续检测鼠标是否在元素内
        window._colorPickerSourceElement = centralPreviewImage;

        // 记录预览图的位置和尺寸
        window._colorPickerPreviewRect = {
            left: previewRect.left,
            top: previewRect.top,
            right: previewRect.right,
            bottom: previewRect.bottom,
            width: previewRect.width,
            height: previewRect.height
        };
    } else {
        console.warn('未找到中央预览图元素，取色器可能无法正常关闭');
    }

    return { previewRect };
}

/**
 * 设置取色器全局状态
 */
export function setupColorPickerGlobalState(): void {
    // 强制锁定状态，阻止任何预览区域事件处理
    window._colorPickerForceLocked = true;

    // 标记取色器为激活状态
    window._colorPickerActive = true;

    // 记录激活时间，用于避免事件冲突
    window._colorPickerActivatedTime = Date.now();
}

/**
 * 创建取色器核心配置
 */
export function createColorPickerConfig(): ColorPickerCoreConfig {
    // 定义统一的过渡动画效果
    const positionTransition = 'left 0.05s cubic-bezier(0.1, 0.7, 0.1, 1), top 0.05s cubic-bezier(0.1, 0.7, 0.1, 1)';
    const finalTransition = currentPlatform === 'win32' ? positionTransition : '';

    // 设置全局超时，确保取色器不会无限期挂起
    const globalTimeout = 120000; // 2分钟超时

    const { previewRect } = initializePreviewArea();

    return {
        finalTransition,
        globalTimeout,
        previewRect
    };
}

/**
 * 重置取色器状态
 */
export async function resetColorPickerState() {
    // 获取状态机实例
    const stateMachine = getColorPickerStateMachine();

    // 检查是否有活动的清理函数，这是首选的、最安全的关闭方式
    if (typeof window._colorPickerCleanup === 'function') {
        try {
            // 调用在colorPicker函数作用域内定义的完整清理函数
            await window._colorPickerCleanup();
            // 确保状态机回到IDLE状态
            if (stateMachine.getCurrentState() !== ColorPickerState.IDLE) {
                await stateMachine.executeEvent(ColorPickerEvent.RESET);
            }
            return; // 成功清理，退出函数
        } catch (error) {
            console.error('调用标准清理函数失败，将执行强制重置:', error);
        }
    }

    // --- Fallback/Hard Reset Logic ---
    // 如果_colorPickerCleanup不可用或执行失败，执行以下强制清理逻辑

    // 强制重置状态机到IDLE状态
    if (stateMachine.getCurrentState() !== ColorPickerState.IDLE) {
        await stateMachine.executeEvent(ColorPickerEvent.RESET);
    }

    // 重置全局状态
    window._colorPickerActive = false;
    window._colorPickerForceLocked = false;

    // 重置锁定状态
    setColorPickerLocked(false);
    if (colorPickerLockedTimeout) {
        window.clearTimeout(colorPickerLockedTimeout);
        setColorPickerLockedTimeout(null);
    }

    // 尝试清理可能残留的DOM元素
    try {
        // 使用更可靠的方式查找可能的残留元素
        const clearElements = [
            document.getElementById('color-picker-magnifier'),
            document.getElementById('color-picker-overlay'),
            document.getElementById('color-picker-click-catcher'),
            document.getElementById('color-picker-info'),
            document.getElementById('color-picker-result'),
            document.querySelector('div[style*="border-radius: 50%"][style*="z-index: 10000"]'),
            document.querySelector('div[style*="width: 100vw"][style*="height: 100vh"][style*="z-index: 9999"]'),
            document.querySelector('div[style*="width: 100vw"][style*="height: 100vh"][style*="z-index: 9998"]')
        ];

        // 移除所有找到的元素
        for (const element of clearElements) {
            if (element) {
                if (element instanceof HTMLElement) {
                    element.style.pointerEvents = 'none'; // 先禁用事件，避免点击冲突
                }
                element.remove();
            }
        }

        // 恢复正常鼠标样式
        document.body.style.cursor = '';

        // 恢复所有元素的鼠标样式
        try {
            const allElements = document.getElementsByTagName('*');
            for (let i = 0; i < allElements.length; i++) {
                const element = allElements[ i ];
                if (element instanceof HTMLElement) {
                    // 检查自定义光标(url开头)或none光标，并恢复默认样式
                    if (element.style.cursor === 'none' ||
                        element.style.cursor.startsWith('url(') ||
                        element.style.cursor.includes('transparent')) {
                        element.style.cursor = '';
                    }
                }
            }
        } catch (error) {
            console.error('恢复鼠标样式失败:', error);
        }
    } catch (e) {
        console.error('清理残留DOM元素失败:', e);
    }
}

/**
 * 初始化颜色数据库并显示进度信息
 */
export async function initializeColorDatabase(): Promise<boolean> {
    // 不强制重新加载，避免重复加载已缓存的数据
    console.log('开始初始化颜色数据库...');
    const dbInitialized = await originalInitColorDatabase(false); // 不强制重新加载

    // 如果初始化失败，显示错误提示但继续执行取色器（仅显示颜色值）
    if (!dbInitialized) {
        console.warn('颜色数据库初始化失败，取色器将只显示颜色值');
    } else {
        console.log('颜色数据库初始化成功，包含', Object.keys(getColorDatabase()).length, '个颜色条目');
    }

    return dbInitialized;
}

/**
 * 重新导出原始的 initColorDatabase 函数
 */
export const initColorDatabase = originalInitColorDatabase;