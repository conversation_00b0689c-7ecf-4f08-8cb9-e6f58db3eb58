/**
 * 颜色取色器绘制引擎模块
 * 负责屏幕截图、放大镜绘制逻辑、像素颜色获取和Canvas相关操作
 */

import { updateColorInfo } from './color_picker_ui';

/**
 * 绘制引擎接口定义
 */
export interface MagnifierDrawFunction {
    (x: number, y: number): void;
}

/**
 * 创建屏幕截图函数
 */
export function createScreenshotFunction(
    screenCanvas: HTMLCanvasElement,
    screenCtx: CanvasRenderingContext2D
): () => void {
    // 缓存变量，避免重复计算
    let lastScreenshotTime = 0;
    const throttleTime = 200; // 减少节流时间，提高响应性
    let cachedElements: Array<{ element: Element, rect: DOMRect, zIndex: number }> = [];
    let cacheTime = 0;
    const cacheTimeout = 500; // 减少缓存时间，确保更及时的更新

    return () => {
        const now = Date.now();
        // 如果距离上次截图时间不足阈值，跳过
        if (now - lastScreenshotTime < throttleTime) {
            return;
        }

        lastScreenshotTime = now;

        // 设置画布大小为当前窗口大小
        screenCanvas.width = window.innerWidth;
        screenCanvas.height = window.innerHeight;

        if (!screenCtx) {
            return;
        }

        try {
            // 清空画布并设置适当的背景色
            screenCtx.clearRect(0, 0, screenCanvas.width, screenCanvas.height);

            // 先渲染页面背景色，避免透明区域显示底层内容
            const bodyStyle = window.getComputedStyle(document.body);
            const bodyBgColor = bodyStyle.backgroundColor;
            if (bodyBgColor && bodyBgColor !== 'transparent' && bodyBgColor !== 'rgba(0, 0, 0, 0)') {
                screenCtx.fillStyle = bodyBgColor;
                screenCtx.fillRect(0, 0, screenCanvas.width, screenCanvas.height);
            } else {
                // 如果body没有背景色，使用白色作为默认背景
                screenCtx.fillStyle = '#ffffff';
                screenCtx.fillRect(0, 0, screenCanvas.width, screenCanvas.height);
            }

            // 使用缓存的元素或重新获取
            let allElements = cachedElements;
            if (now - cacheTime > cacheTimeout || cachedElements.length === 0) {
                allElements = [];

                // 收集重要的视觉元素，确保包含容器背景
                const importantSelectors = [
                    'body', // 页面背景
                    'html', // 根元素背景
                    'div', // 所有div容器（可能有背景）
                    'img', // 图像元素
                    '.central-preview-image', // 中央预览图
                    '[style*="background"]', // 有背景样式的元素
                    '[style*="background-color"]', // 有背景色的元素
                    '[class*="container"]', // 容器类
                    '[class*="preview"]', // 预览类
                    'section', 'main', 'article' // 主要内容容器
                ];

                for (const selector of importantSelectors) {
                    try {
                        const elements = document.querySelectorAll(selector);
                        for (let i = 0; i < elements.length; i++) {
                            const element = elements[i];
                            const rect = element.getBoundingClientRect();

                            // 跳过不可见或尺寸为0的元素
                            if (rect.width === 0 || rect.height === 0 ||
                                rect.right < 0 || rect.bottom < 0 ||
                                rect.left > window.innerWidth || rect.top > window.innerHeight) {
                                continue;
                            }

                            allElements.push({ element, rect, zIndex: 0 });
                        }
                    } catch (e) {
                        continue;
                    }
                }

                // 简单的DOM顺序排序，避免复杂的z-index计算
                allElements.sort((a, b) => {
                    return a.element.compareDocumentPosition(b.element) & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;
                });

                // 更新缓存
                cachedElements = allElements;
                cacheTime = now;
            }

            // 简化渲染，专注于颜色准确性
            for (const { element, rect } of allElements) {
                try {
                    const style = window.getComputedStyle(element);

                    // 渲染背景色（不处理透明度，保持原始颜色）
                    const bgColor = style.backgroundColor;
                    if (bgColor && bgColor !== 'transparent' && bgColor !== 'rgba(0, 0, 0, 0)') {
                        screenCtx.fillStyle = bgColor;
                        screenCtx.fillRect(rect.left, rect.top, rect.width, rect.height);
                    }

                    // 渲染图像（直接绘制，不处理透明度）
                    if (element instanceof HTMLImageElement && element.complete && element.naturalWidth > 0) {
                        try {
                            screenCtx.drawImage(element, rect.left, rect.top, rect.width, rect.height);
                        } catch (e) {
                            // 忽略图像绘制错误
                        }
                    }

                } catch (e) {
                    // 忽略单个元素的渲染错误，继续处理下一个
                    continue;
                }
            }

        } catch (error) {
            console.error('截图失败:', error);
        }
    };
}

/**
 * 获取屏幕某点的颜色
 */
export function getPixelColor(
    x: number,
    y: number,
    screenCanvas: HTMLCanvasElement,
    screenCtx: CanvasRenderingContext2D
): string {
    if (!screenCtx) return '#ffffff';

    try {
        // 强制确保坐标在画布范围内
        const safeX = Math.max(0, Math.min(Math.floor(x), screenCanvas.width - 1));
        const safeY = Math.max(0, Math.min(Math.floor(y), screenCanvas.height - 1));

        // 确保画布已准备好并且尺寸正确
        if (screenCanvas.width <= 0 || screenCanvas.height <= 0) {
            console.warn('画布尺寸无效:', screenCanvas.width, screenCanvas.height);
            return '#ffffff';
        }

        // 检查坐标位置是否有效
        if (safeX >= 0 && safeX < screenCanvas.width && safeY >= 0 && safeY < screenCanvas.height) {
            try {
                const pixelData = screenCtx.getImageData(safeX, safeY, 1, 1).data;

                // 确保像素数据有效
                if (pixelData && pixelData.length >= 3) {
                    // 转换为HEX格式
                    const hex = '#' +
                        ('0' + pixelData[0].toString(16)).slice(-2) +
                        ('0' + pixelData[1].toString(16)).slice(-2) +
                        ('0' + pixelData[2].toString(16)).slice(-2);

                    return hex;
                }
            } catch (pixelError) {
                console.error('获取像素数据失败:', pixelError);
            }
        } else {
            console.warn('坐标超出画布范围:', safeX, safeY, '画布大小:', screenCanvas.width, screenCanvas.height);
        }
    } catch (error) {
        console.error('获取像素颜色失败:', error);
    }

    // 如果获取失败，返回白色
    return '#ffffff';
}

/**
 * 判断颜色是否为浅色
 */
export function isLightColor(hexColor: string): boolean {
    try {
        // 转换HEX为RGB
        const r = parseInt(hexColor.slice(1, 3), 16);
        const g = parseInt(hexColor.slice(3, 5), 16);
        const b = parseInt(hexColor.slice(5, 7), 16);

        // 计算亮度（简化版YIQ公式）
        const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
        return yiq >= 128; // 大于128认为是浅色
    } catch (e) {
        return true; // 发生错误时默认为浅色
    }
}

/**
 * 创建放大镜绘制函数
 */
export function createMagnifierDrawFunction(
    magnifier: HTMLDivElement,
    canvas: HTMLCanvasElement,
    colorInfoElement: HTMLDivElement,
    screenCanvas: HTMLCanvasElement,
    screenCtx: CanvasRenderingContext2D,
    tempCanvas: HTMLCanvasElement,
    tempCtx: CanvasRenderingContext2D,
    getCurrentCaptureSize: () => number,
    getCurrentMagnifierSize: () => number
): MagnifierDrawFunction {
    return (x: number, y: number) => {
        // 强制使用整数坐标，以此消除准星偏移和抖动
        const pixelX = Math.floor(x);
        const pixelY = Math.floor(y);

        // 使用 requestAnimationFrame 来调度DOM更新，确保动画流畅
        requestAnimationFrame(() => {
            if (magnifier) {
                // 始终将放大镜定位在整数像素坐标上，与内容渲染保持一致
                magnifier.style.left = `${pixelX}px`;
                magnifier.style.top = `${pixelY}px`;

                // 确保放大镜可见
                if (magnifier.style.display !== 'block') {
                    magnifier.style.display = 'block';
                }
            }
        });

        const ctx = canvas.getContext('2d', { willReadFrequently: true });
        if (!ctx || !screenCtx) return;

        try {
            // 获取中心点颜色 - 使用对齐后的整数坐标
            const centerColor = getPixelColor(pixelX, pixelY, screenCanvas, screenCtx);

            // 更新颜色信息显示 - 同样使用整数坐标
            updateColorInfo(colorInfoElement, pixelX, pixelY, centerColor, getCurrentMagnifierSize());

            // 清除画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 使用当前的捕获大小
            const captureSize = getCurrentCaptureSize();

            // 确保tempCanvas尺寸正确
            if (tempCanvas.width !== captureSize || tempCanvas.height !== captureSize) {
                tempCanvas.width = captureSize;
                tempCanvas.height = captureSize;
            }

            try {
                // 计算需要捕获的区域 - 基于整数坐标，确保对齐
                const srcX = Math.max(0, pixelX - Math.floor(captureSize / 2));
                const srcY = Math.max(0, pixelY - Math.floor(captureSize / 2));

                // 确保不会超出边界
                const clampedWidth = Math.min(captureSize, screenCanvas.width - srcX);
                const clampedHeight = Math.min(captureSize, screenCanvas.height - srcY);

                // 防止尺寸为0导致出错
                if (clampedWidth <= 0 || clampedHeight <= 0) {
                    throw new Error('捕获区域超出边界');
                }

                // 从屏幕截图获取像素数据
                const imageData = screenCtx.getImageData(srcX, srcY, clampedWidth, clampedHeight);

                // 禁用图像平滑，确保像素边缘清晰
                ctx.imageSmoothingEnabled = false;
                if (tempCtx) {
                    tempCtx.imageSmoothingEnabled = false;
                    tempCtx.clearRect(0, 0, tempCanvas.width, tempCanvas.height);

                    // 处理可能的部分图像
                    if (clampedWidth < captureSize || clampedHeight < captureSize) {
                        // 先填充白色背景
                        tempCtx.fillStyle = '#ffffff';
                        tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
                    }

                    tempCtx.putImageData(imageData, 0, 0);

                    // 直接绘制到主画布，不进行缩放
                    ctx.drawImage(
                        tempCanvas,
                        0, 0, captureSize, captureSize,
                        0, 0, canvas.width, canvas.height
                    );

                    // 绘制中心像素标记
                    const exactPixelSize = canvas.width / captureSize;
                    const targetPixelPosition = Math.floor(captureSize / 2);
                    const centerPixelLeft = targetPixelPosition * exactPixelSize;
                    const centerPixelTop = targetPixelPosition * exactPixelSize;

                    // 绘制中心像素标记框
                    ctx.strokeStyle = 'rgba(0, 0, 0, 1)';
                    ctx.lineWidth = 2;
                    ctx.lineJoin = 'round';
                    ctx.strokeRect(centerPixelLeft, centerPixelTop, exactPixelSize, exactPixelSize);

                    ctx.strokeStyle = 'rgba(255, 255, 255, 1)';
                    ctx.lineWidth = 1;
                    ctx.lineJoin = 'round';
                    ctx.strokeRect(centerPixelLeft + 1, centerPixelTop + 1, exactPixelSize - 2, exactPixelSize - 2);
                }
            } catch (error) {
                console.error('放大镜绘制详细错误:', error);
                ctx.fillStyle = centerColor;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = isLightColor(centerColor) ? '#000000' : '#FFFFFF';
                ctx.font = '12px monospace';
                ctx.textAlign = 'center';
                ctx.fillText('渲染错误', canvas.width / 2, canvas.height / 2);
            }

            magnifier.dataset.currentColor = centerColor;
        } catch (error) {
            console.error('绘制放大镜内容失败:', error);
        }
    };
}

/**
 * RGB转HEX颜色格式转换
 */
export function rgbToHex(r: number, g: number, b: number): string {
    return '#' +
        ('0' + Math.round(r).toString(16)).slice(-2) +
        ('0' + Math.round(g).toString(16)).slice(-2) +
        ('0' + Math.round(b).toString(16)).slice(-2);
}

/**
 * HEX转RGB颜色格式转换
 */
export function hexToRgb(hex: string): { r: number, g: number, b: number } | null {
    try {
        const r = parseInt(hex.slice(1, 3), 16);
        const g = parseInt(hex.slice(3, 5), 16);
        const b = parseInt(hex.slice(5, 7), 16);
        return { r, g, b };
    } catch (e) {
        return null;
    }
}