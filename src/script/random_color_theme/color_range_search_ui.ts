/**
 * 颜色范围搜索UI初始化模块
 * 负责初始化颜色范围搜索的UI交互功能
 */

import { searchColorRange } from './color_range_search';
import { showSearchResults } from './search_results_display';
import { messageError, messageLoading } from '../prompt_message';
import { initPaths } from './color_database';
import { resetColorPickerState } from './color_picker_core';

/**
 * 初始化颜色范围搜索功能
 */
export function initializeColorRangeSearch(): void {
    try {
        console.log('开始初始化颜色范围搜索功能');

        // 确保在颜色搜索窗口中
        if (!document.querySelector('.color-search-window')) {
            console.log('不在颜色搜索窗口中，跳过颜色范围搜索初始化');
            return;
        }

        // 初始化路径
        initializePathsAsync();

        // 设置输入框事件
        setupInputEvents();

        // 设置按钮事件
        setupButtonEvents();

        // 添加全局点击事件委托（解决层级覆盖问题）
        addGlobalClickHandler();

        console.log('颜色范围搜索功能初始化完成');

    } catch (error) {
        console.error('颜色范围搜索功能初始化失败:', error);
    }
}

/**
 * 异步初始化路径
 */
async function initializePathsAsync(): Promise<void> {
    try {
        const success = await initPaths();
        if (!success) {
            console.warn('颜色数据库路径初始化失败，颜色范围搜索功能可能无法正常工作');
        }
    } catch (error) {
        console.error('初始化颜色数据库路径失败:', error);
    }
}

/**
 * 设置输入框事件
 */
function setupInputEvents(): void {
    const input = document.getElementById('color-range-search-input') as HTMLInputElement;
    if (!input) {
        console.error('未找到颜色范围搜索输入框');
        return;
    }

    // 回车键触发搜索
    input.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleSearch();
        }
    });

    // 移除所有输入验证和格式化，允许用户自由输入

    // 焦点事件
    input.addEventListener('focus', () => {
        input.select();
    });
}

/**
 * 设置按钮事件
 */
function setupButtonEvents(): void {
    const button = document.getElementById('color-range-search-btn') as HTMLButtonElement;
    if (!button) {
        console.error('未找到颜色范围搜索按钮');
        return;
    }

    button.addEventListener('click', handleSearch);
}

/**
 * 处理搜索操作
 */
async function handleSearch(): Promise<void> {
    // 关闭取色器（如果正在运行）
    if (window._colorPickerActive) {
        resetColorPickerState();
    }

    const input = document.getElementById('color-range-search-input') as HTMLInputElement;
    const button = document.getElementById('color-range-search-btn') as HTMLButtonElement;

    if (!input || !button) {
        messageError('搜索组件未找到');
        return;
    }

    const rawColorValue = input.value.trim();
    if (!rawColorValue) {
        messageError('请输入颜色值');
        input.focus();
        return;
    }

    // 直接使用用户输入，不进行格式验证

    try {
        // 禁用输入和按钮
        input.disabled = true;
        button.disabled = true;

        // 显示加载提示
        messageLoading('正在搜索颜色范围...');

        // 执行搜索，直接传递用户输入
        const results = await searchColorRange(rawColorValue);

        if (results && Object.keys(results).length > 0) {
            // 显示搜索结果
            showSearchResults(results, rawColorValue);
        }

    } catch (error) {
        console.error('颜色范围搜索失败:', error);
        messageError(`搜索失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
        // 恢复输入和按钮状态
        input.disabled = false;
        button.disabled = false;
    }
}

/**
 * 解析颜色输入
 * @param input 原始输入
 * @returns 解析结果 {colorValue: string, rangeValue?: number} 或 null
 */
// 已删除未使用的 ColorInputResult 接口和 parseColorInput 函数

// 已删除未使用的 cleanColorInput 函数

// 已删除未使用的 isValidColorFormat 函数

/**
 * 获取当前输入的颜色值
 * @returns 当前颜色值或null
 */
export function getCurrentColorInput(): string | null {
    const input = document.getElementById('color-range-search-input') as HTMLInputElement;
    if (!input) {
        return null;
    }

    const value = input.value.trim();
    return value || null;
}

/**
 * 设置颜色输入值
 * @param color 颜色值
 */
export function setColorInput(color: string): void {
    const input = document.getElementById('color-range-search-input') as HTMLInputElement;
    if (!input) {
        return;
    }

    input.value = color;
}

/**
 * 清空颜色输入
 */
export function clearColorInput(): void {
    const input = document.getElementById('color-range-search-input') as HTMLInputElement;
    if (input) {
        input.value = '';
        input.focus();
    }
}

/**
 * 添加全局点击事件委托，解决层级覆盖问题
 */
function addGlobalClickHandler(): void {
    document.addEventListener('click', (event: MouseEvent) => {
        // 检查点击位置是否在搜索框区域内
        const searchContainer = document.getElementById('color-range-search-container');
        if (!searchContainer) return;

        const rect = searchContainer.getBoundingClientRect();
        const x = event.clientX;
        const y = event.clientY;

        // 判断点击是否在搜索容器范围内
        if (x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom) {
            // 阻止事件冒泡
            event.stopPropagation();

            // 检查具体点击的是输入框还是按钮
            const input = document.getElementById('color-range-search-input') as HTMLInputElement;
            const button = document.getElementById('color-range-search-btn') as HTMLButtonElement;

            if (input && button) {
                const inputRect = input.getBoundingClientRect();
                const buttonRect = button.getBoundingClientRect();

                // 点击输入框区域
                if (x >= inputRect.left && x <= inputRect.right && y >= inputRect.top && y <= inputRect.bottom) {
                    input.focus();
                }
                // 点击按钮区域
                else if (x >= buttonRect.left && x <= buttonRect.right && y >= buttonRect.top && y <= buttonRect.bottom) {
                    handleSearch();
                }
            }
        }
    }, true); // 使用捕获阶段，确保在其他事件之前处理
}
