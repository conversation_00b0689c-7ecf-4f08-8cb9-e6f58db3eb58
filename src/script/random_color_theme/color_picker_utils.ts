/**
 * 颜色取色器工具函数模块
 * 负责图片取色、全局函数导出和窗口全局变量管理
 */

import { initColorDatabase } from './color_picker_core';

/**
 * 使用Canvas API从提供的图片URL获取特定点的颜色
 * @param imageUrl 图片URL
 * @param x X坐标
 * @param y Y坐标
 * @returns Promise<string> 返回HEX格式的颜色值
 */
export function getColorFromImage(imageUrl: string, x: number, y: number): Promise<string> {
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.crossOrigin = 'Anonymous'; // 处理跨域图片

        // 创建离屏Canvas，避免DOM渲染干扰
        const canvas = document.createElement('canvas');
        canvas.style.display = 'none'; // 确保不显示在页面上
        document.body.appendChild(canvas);

        // 添加超时处理，避免图片长时间无法加载导致取色失败
        const timeout = setTimeout(() => {
            console.error('图片加载超时:', imageUrl);
            img.src = ''; // 取消加载
            if (document.body.contains(canvas)) {
                document.body.removeChild(canvas);
            }
            reject(new Error('图片加载超时'));
        }, 10000); // 10秒超时

        img.onload = () => {
            try {
                clearTimeout(timeout);

                // 获取Canvas上下文
                const ctx = canvas.getContext('2d', {
                    willReadFrequently: true,
                    alpha: true // 启用alpha通道支持
                });

                if (!ctx) {
                    document.body.removeChild(canvas);
                    reject(new Error('无法创建Canvas上下文'));
                    return;
                }

                // 设置Canvas尺寸为图片实际尺寸
                canvas.width = img.naturalWidth;
                canvas.height = img.naturalHeight;

                // 清空Canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 禁用图像平滑，确保像素边缘清晰
                ctx.imageSmoothingEnabled = false;

                // 绘制图片，使用完整尺寸确保像素精确对应
                ctx.drawImage(img, 0, 0, img.naturalWidth, img.naturalHeight);

                // 确保坐标在图片范围内
                const safeX = Math.max(0, Math.min(Math.floor(x), img.naturalWidth - 1));
                const safeY = Math.max(0, Math.min(Math.floor(y), img.naturalHeight - 1));

                // 使用getImageData获取像素颜色
                const pixelData = ctx.getImageData(safeX, safeY, 1, 1).data;

                // 转换为HEX格式，包括透明度处理
                const hex = '#' +
                    ('0' + pixelData[ 0 ].toString(16)).slice(-2) +
                    ('0' + pixelData[ 1 ].toString(16)).slice(-2) +
                    ('0' + pixelData[ 2 ].toString(16)).slice(-2);

                // 移除Canvas
                document.body.removeChild(canvas);

                // 返回结果
                resolve(hex);
            } catch (error) {
                console.error('在图片取色过程中发生错误:', error);
                // 确保Canvas被移除
                if (document.body.contains(canvas)) {
                    document.body.removeChild(canvas);
                }
                reject(error);
            }
        };

        img.onerror = (error) => {
            clearTimeout(timeout);
            console.error('图片加载失败:', error);
            // 确保Canvas被移除
            if (document.body.contains(canvas)) {
                document.body.removeChild(canvas);
            }
            reject(error);
        };

        // 开始加载图片
        img.src = imageUrl;
    });
}

/**
 * 全局窗口类型声明
 */
declare global {
    interface Window {
        colorPicker: () => Promise<string | undefined>;
        getColorFromImage: typeof getColorFromImage;
        resetColorPickerState: () => void;
        activateColorPicker: () => Promise<string | undefined>;
        initColorDatabase: typeof initColorDatabase;
        _lastMouseX: number;
        _lastMouseY: number;
        _colorPickerActive: boolean; // 标记取色器是否激活
        _isMouseInWindow: boolean; // 标记鼠标是否在窗口内
        _colorPickerActivatedTime: number; // 记录取色器激活时间
        _colorPickerForceLocked: boolean; // 强制锁定状态，阻止任何预览区域事件处理
        _lastPickedColor: string | undefined; // 记录最后一次取到的颜色
        _colorPickerSourceElement: Element | null; // 记录激活取色器的预览图元素
        _colorPickerPreviewRect: { // 记录预览图的位置和尺寸
            left: number;
            top: number;
            right: number;
            bottom: number;
            width: number;
            height: number;
        } | null;
        _colorPickerCleanup: (() => void) | null;
        _dropdownMenuOpen: boolean; // 标记是否有下拉菜单展开
        platform: string; // 平台信息
    }
}

/**
 * 初始化全局窗口变量
 */
export function initializeGlobalVariables(): void {
    // 初始化全局变量
    window._colorPickerCleanup = null;
    window._colorPickerActive = false; // 初始化为false
    window._isMouseInWindow = false; // 初始化为false
    window._colorPickerActivatedTime = 0; // 初始化为0
    window._colorPickerForceLocked = false; // 初始化为false
    window._lastPickedColor = undefined; // 初始化为undefined
    window._colorPickerSourceElement = null; // 初始化为null
    window._colorPickerPreviewRect = null; // 初始化为null
    window._dropdownMenuOpen = false; // 初始化为false
}

/**
 * 导出全局函数到window对象
 */
export function exportGlobalFunctions(
    colorPicker: () => Promise<string | undefined>,
    resetColorPickerState: () => void,
    activateColorPicker: () => Promise<string | undefined>
): void {
    // 添加到window对象，使其成为全局函数
    window.colorPicker = colorPicker;
    window.getColorFromImage = getColorFromImage;
    window.resetColorPickerState = resetColorPickerState;
    window.activateColorPicker = activateColorPicker;
    window.initColorDatabase = initColorDatabase;
}