/**
 * 搜索颜色工具主入口文件
 * 已重构为模块化架构，主要功能已拆分到各个专门模块中
 */

// 导入类型声明
declare global {
    interface Window {
        截屏: (outpath?: string | undefined) => Promise<string[] | undefined>;
        reloadDataJsonAndRefresh?: () => Promise<void>;
        __TAURI__?: any;
        isPrivilegedUser?: boolean;
    }
}

// 导入并初始化所有模块
import './search_color_init';

// 重新导出主要功能供其他模块使用
export {
    loadModuleStatusFromConfig,
    saveModuleStatusToConfig,
    getUserName,
    checkPrivilegedUser,
    moduleStatusModifications,
    getModuleStatusModifications,
    getHasModifications
} from './search_color_config';

export {
    createGenerateThemeButton,
    createApplyThemeButton,
    populateGenerateModuleOptions,
    populateModuleOptions
} from './search_color_ui';

export {
    generateSelectedModules,
    saveModuleSelections,
    setupGenerateModuleEvents
} from './module_config_manager';

export {
    init,
    addScreenshot,
    deleteScreenshot,
    clearAllScreenshots,
    renderScreenshots,
    updateScreenshotAndPreviewArea,
    updateScreenshotSelection,
    getCurrentSelectedScreenshot,
    setCurrentSelectedScreenshot,
    getPath
} from './screenshot_manager';

export {
    checkAndSelectDevice,
    copySelectedImageToClipboard,
    setupImageCopyKeyboardHandler,
    openRandomThemeFolder
} from './device_file_operations';

export {
    initializeSearchColorTools,
    setupGenerateModuleEvents as setupModuleEvents,
    setupConfigAutoSave
} from './search_color_init';

// 向后兼容性：保留一些原始导出
export { init as initScreenshots } from './screenshot_manager';
export { reloadDataJsonAndRefresh } from './module_config_manager';

// 颜色范围搜索功能导出
export {
    searchColorRange,
    abortColorRangeSearch,
    ColorRangeSearchManager
} from './color_range_search';

export {
    generateColorRegex,
    executeBatchOperation,
    RegexProcessor
} from './regex_processor';

export {
    showSearchResults,
    closeSearchResults,
    SearchResultsDisplay
} from './search_results_display';

export {
    initializeColorRangeSearch,
    getCurrentColorInput,
    setColorInput,
    clearColorInput
} from './color_range_search_ui';