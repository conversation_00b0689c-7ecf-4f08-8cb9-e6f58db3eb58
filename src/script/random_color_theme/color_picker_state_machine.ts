/**
 * 取色器状态机核心模块
 * 统一管理取色器的激活、关闭和状态转换逻辑
 */



/**
 * 取色器状态枚举
 */
export enum ColorPickerState {
    IDLE = 'IDLE',                    // 空闲状态
    ACTIVATING = 'ACTIVATING',        // 正在激活
    ACTIVE = 'ACTIVE',                // 已激活
    DEACTIVATING = 'DEACTIVATING',    // 正在关闭
    ERROR = 'ERROR'                   // 错误状态
}

/**
 * 状态转换事件枚举
 */
export enum ColorPickerEvent {
    ACTIVATE = 'ACTIVATE',            // 激活事件
    ACTIVATION_SUCCESS = 'ACTIVATION_SUCCESS', // 激活成功
    ACTIVATION_FAILED = 'ACTIVATION_FAILED',   // 激活失败
    DEACTIVATE = 'DEACTIVATE',        // 关闭事件
    DEACTIVATION_SUCCESS = 'DEACTIVATION_SUCCESS', // 关闭成功
    DEACTIVATION_FAILED = 'DEACTIVATION_FAILED',   // 关闭失败
    ERROR = 'ERROR',                  // 错误事件
    RESET = 'RESET'                   // 重置事件
}

/**
 * 状态转换规则接口
 */
export interface StateTransition {
    from: ColorPickerState;
    event: ColorPickerEvent;
    to: ColorPickerState;
    action?: () => void | Promise<void>;
}

/**
 * 状态机配置接口
 */
export interface StateMachineConfig {
    initialState: ColorPickerState;
    transitions: StateTransition[];
    onStateChange?: (from: ColorPickerState, to: ColorPickerState, event: ColorPickerEvent) => void;
    onError?: (error: Error, state: ColorPickerState) => void;
}

/**
 * 取色器状态机类
 */
export class ColorPickerStateMachine {
    private currentState: ColorPickerState;
    private config: StateMachineConfig;
    private isTransitioning: boolean = false;
    private stateHistory: Array<{ state: ColorPickerState; timestamp: number; event?: ColorPickerEvent }> = [];

    constructor(config: StateMachineConfig) {
        this.config = config;
        this.currentState = config.initialState;
        this.logStateChange(this.currentState, undefined, 'INIT');
    }

    /**
     * 获取当前状态
     */
    getCurrentState(): ColorPickerState {
        return this.currentState;
    }

    /**
     * 获取状态历史
     */
    getStateHistory(): Array<{ state: ColorPickerState; timestamp: number; event?: ColorPickerEvent }> {
        return [ ...this.stateHistory ];
    }

    /**
     * 检查是否正在转换状态
     */
    isInTransition(): boolean {
        return this.isTransitioning;
    }

    /**
     * 检查是否可以执行指定事件
     */
    canExecuteEvent(event: ColorPickerEvent): boolean {
        return this.config.transitions.some(
            transition => transition.from === this.currentState && transition.event === event
        );
    }

    /**
     * 执行状态转换
     */
    async executeEvent(event: ColorPickerEvent): Promise<boolean> {
        if (this.isTransitioning) {
            console.warn(`状态机正在转换中，忽略事件: ${event}`);
            return false;
        }

        const transition = this.config.transitions.find(
            t => t.from === this.currentState && t.event === event
        );

        if (!transition) {
            console.warn(`无效的状态转换: ${this.currentState} -> ${event}`);
            return false;
        }

        try {
            this.isTransitioning = true;
            const fromState = this.currentState;
            const toState = transition.to;

            // 执行转换动作（如果有）
            if (transition.action) {
                await transition.action();
            }

            // 更新状态
            this.currentState = toState;
            this.logStateChange(toState, event);



            // 调用状态变化回调
            if (this.config.onStateChange) {
                this.config.onStateChange(fromState, toState, event);
            }

            return true;
        } catch (error) {
            console.error(`状态转换失败: ${this.currentState} -> ${event}`, error);



            // 转换到错误状态
            this.currentState = ColorPickerState.ERROR;
            this.logStateChange(ColorPickerState.ERROR, ColorPickerEvent.ERROR);

            if (this.config.onError) {
                this.config.onError(error as Error, this.currentState);
            }

            return false;
        } finally {
            this.isTransitioning = false;
        }
    }

    /**
     * 强制重置到初始状态
     */
    async reset(): Promise<void> {
        this.isTransitioning = false;
        this.currentState = this.config.initialState;
        this.logStateChange(this.currentState, ColorPickerEvent.RESET);
    }

    /**
     * 记录状态变化
     */
    private logStateChange(state: ColorPickerState, event?: ColorPickerEvent, reason?: string): void {
        this.stateHistory.push({
            state,
            timestamp: Date.now(),
            event
        });

        // 保持历史记录在合理范围内
        if (this.stateHistory.length > 50) {
            this.stateHistory = this.stateHistory.slice(-30);
        }

        console.log(`[ColorPicker State] ${state}${event ? ` (${event})` : ''}${reason ? ` - ${reason}` : ''}`);
    }
}

/**
 * 创建取色器状态机实例
 */
export function createColorPickerStateMachine(): ColorPickerStateMachine {
    const transitions: StateTransition[] = [
        // IDLE -> ACTIVATING
        {
            from: ColorPickerState.IDLE,
            event: ColorPickerEvent.ACTIVATE,
            to: ColorPickerState.ACTIVATING,
            action: async () => {
                // 设置激活状态
                window._colorPickerActive = true;
                window._colorPickerActivatedTime = Date.now();
            }
        },

        // ACTIVATING -> ACTIVE
        {
            from: ColorPickerState.ACTIVATING,
            event: ColorPickerEvent.ACTIVATION_SUCCESS,
            to: ColorPickerState.ACTIVE
        },

        // ACTIVATING -> ERROR
        {
            from: ColorPickerState.ACTIVATING,
            event: ColorPickerEvent.ACTIVATION_FAILED,
            to: ColorPickerState.ERROR
        },

        // ACTIVE -> DEACTIVATING
        {
            from: ColorPickerState.ACTIVE,
            event: ColorPickerEvent.DEACTIVATE,
            to: ColorPickerState.DEACTIVATING,
            action: async () => {
                // 开始关闭流程
                window._colorPickerActive = false;
            }
        },

        // DEACTIVATING -> IDLE
        {
            from: ColorPickerState.DEACTIVATING,
            event: ColorPickerEvent.DEACTIVATION_SUCCESS,
            to: ColorPickerState.IDLE,
            action: async () => {
                // 清理资源
                window._colorPickerCleanup = null;
                window._colorPickerSourceElement = null;
                window._colorPickerPreviewRect = null;
                window._colorPickerForceLocked = false;
            }
        },

        // DEACTIVATING -> ERROR
        {
            from: ColorPickerState.DEACTIVATING,
            event: ColorPickerEvent.DEACTIVATION_FAILED,
            to: ColorPickerState.ERROR
        },

        // ERROR -> IDLE
        {
            from: ColorPickerState.ERROR,
            event: ColorPickerEvent.RESET,
            to: ColorPickerState.IDLE,
            action: async () => {
                // 强制清理
                window._colorPickerActive = false;
                window._colorPickerCleanup = null;
                window._colorPickerSourceElement = null;
                window._colorPickerPreviewRect = null;
                window._colorPickerForceLocked = false;
            }
        },

        // 从任何状态重置到IDLE
        {
            from: ColorPickerState.IDLE,
            event: ColorPickerEvent.RESET,
            to: ColorPickerState.IDLE,
            action: async () => {
                // 确保清理
                window._colorPickerActive = false;
                window._colorPickerCleanup = null;
                window._colorPickerSourceElement = null;
                window._colorPickerPreviewRect = null;
                window._colorPickerForceLocked = false;
                // 重置锁定状态
                const { setColorPickerLocked } = await import('./color_picker_config');
                setColorPickerLocked(false);

            }
        },
        {
            from: ColorPickerState.ACTIVATING,
            event: ColorPickerEvent.RESET,
            to: ColorPickerState.IDLE,
            action: async () => {
                // 强制清理
                window._colorPickerActive = false;
                window._colorPickerCleanup = null;
                window._colorPickerSourceElement = null;
                window._colorPickerPreviewRect = null;
                window._colorPickerForceLocked = false;
                // 重置锁定状态
                const { setColorPickerLocked } = await import('./color_picker_config');
                setColorPickerLocked(false);

            }
        },
        {
            from: ColorPickerState.ACTIVE,
            event: ColorPickerEvent.RESET,
            to: ColorPickerState.IDLE,
            action: async () => {
                // 强制清理
                window._colorPickerActive = false;
                window._colorPickerCleanup = null;
                window._colorPickerSourceElement = null;
                window._colorPickerPreviewRect = null;
                window._colorPickerForceLocked = false;
                // 重置锁定状态
                const { setColorPickerLocked } = await import('./color_picker_config');
                setColorPickerLocked(false);

            }
        },
        {
            from: ColorPickerState.DEACTIVATING,
            event: ColorPickerEvent.RESET,
            to: ColorPickerState.IDLE,
            action: async () => {
                // 强制清理
                window._colorPickerActive = false;
                window._colorPickerCleanup = null;
                window._colorPickerSourceElement = null;
                window._colorPickerPreviewRect = null;
                window._colorPickerForceLocked = false;
                // 重置锁定状态
                const { setColorPickerLocked } = await import('./color_picker_config');
                setColorPickerLocked(false);

            }
        },

        // 任何状态 -> ERROR
        {
            from: ColorPickerState.IDLE,
            event: ColorPickerEvent.ERROR,
            to: ColorPickerState.ERROR
        },
        {
            from: ColorPickerState.ACTIVATING,
            event: ColorPickerEvent.ERROR,
            to: ColorPickerState.ERROR
        },
        {
            from: ColorPickerState.ACTIVE,
            event: ColorPickerEvent.ERROR,
            to: ColorPickerState.ERROR
        },
        {
            from: ColorPickerState.DEACTIVATING,
            event: ColorPickerEvent.ERROR,
            to: ColorPickerState.ERROR
        }
    ];

    const config: StateMachineConfig = {
        initialState: ColorPickerState.IDLE,
        transitions,
        onStateChange: (from, to, event) => {
            console.log(`ColorPicker State Change: ${from} -> ${to} (${event})`);
        },
        onError: (error, state) => {
            console.error(`ColorPicker State Error in ${state}:`, error);
        }
    };

    return new ColorPickerStateMachine(config);
}

/**
 * 全局状态机实例
 */
let globalStateMachine: ColorPickerStateMachine | null = null;

/**
 * 获取全局状态机实例
 */
export function getColorPickerStateMachine(): ColorPickerStateMachine {
    if (!globalStateMachine) {
        globalStateMachine = createColorPickerStateMachine();
    }
    return globalStateMachine;
}

/**
 * 重置全局状态机
 */
export function resetColorPickerStateMachine(): void {
    if (globalStateMachine) {
        globalStateMachine.reset();
    }
} 