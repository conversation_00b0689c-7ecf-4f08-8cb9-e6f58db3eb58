declare global {
    interface Window {
        cacheDir: string;
        dragFiles: string;
        parentDir: string;
    }
}

import { exists, stat } from '@tauri-apps/plugin-fs';
import { join, normalize, dirname } from '@tauri-apps/api/path';
import { messageError, messageLoading, messageSuccess } from './prompt_message';
import install_apk from './install_apk';
import 打包主题 from './pack_theme';
import { 编译或反编译点9图 } from './nine_patch';
import { 生成应用按钮 } from './apply/buttons';
import { 更新输入框 } from './update_input';
import { 缓存文件 } from './cache_file';
import { 预编译 } from './precompile';
import { unicode转中文 } from './unicode_to_chinese';
import { 字体生成图片 } from './font_to_image';
// import { 图片裁切 } from './image_crop';

// 处理拖入的文件
export async function handleBubble1Drop(draggedPaths: string[]) {
    // 标准化所有拖放的文件路径
    const normalizedPaths = await Promise.all(draggedPaths.map(path => normalize(path)));
    // 使用第一个标准化后的文件路径
    const dragFiles = normalizedPaths[ 0 ];
    // 从文件路径中提取主题名称:
    const fileName = dragFiles.split(/[/\\]/).pop() || '';
    const themeName = fileName.toLowerCase().endsWith('.mtz') ?
        fileName.slice(0, -4) :
        fileName;

    // ========================如果是mtz文件,调用解压主题包函数============================ //
    if (dragFiles.toLowerCase().endsWith('.mtz')) {
        // 过滤出所有mtz文件
        const mtzFiles = normalizedPaths.filter(path => path.toLowerCase().endsWith('.mtz'));
        // mtz文件数量
        const fileCount = mtzFiles.length;
        const errors: string[] = [];
        try {
            const module = await import('./unpack_theme');
            if (fileCount === 1) {
                // 单个文件解压和缓存
                messageLoading('正在解压主题');
                const mtzPath = await module.default(mtzFiles[ 0 ]);
                window.cacheDir = await 缓存文件(mtzPath);

                await 更新输入框(themeName);
                await 生成应用按钮(mtzPath);

                // 暴露给全局变量 - 拖入的文件路径
                window.dragFiles = mtzPath;
                // 暴露给全局变量 - 拖入文件父目录
                window.parentDir = await dirname(dragFiles);
                // 暴露给全局变量 - 缓存目录

                messageSuccess('主题加载成功', 1000);

                预编译(window.cacheDir);

            } else {
                // 多个主题只进行解压
                messageLoading(`正在解压 ${fileCount} 个主题`);
                for (const mtzFile of mtzFiles) {
                    try {
                        await module.default(mtzFile);
                    } catch (error) {
                        const fileName = mtzFile.split(/[/\\]/).pop();
                        errors.push(`${fileName}: ${error}`);
                        continue;
                    }
                }
            }

            if (errors.length > 0) {
                messageError(`${fileCount - errors.length} 个主题解压成功，${errors.length} 个失败:\n${errors.join('\n')}`);
            } else {
                messageSuccess(`${fileCount} 个主题解压成功`, 1000);
            }

        } catch (error) {
            messageError(`导入解压主题模块失败: ${error}`);
            console.error('导入解压主题包模块失败:', error);
        }
    }

    // ==========================如果是目录,调用读取目录函数========================== //

    else if ((await stat(dragFiles)).isDirectory) {
        try {
            const fileCount = normalizedPaths.length;
            // 判断拖入文件数量
            if (fileCount === 1) {
                // 如果能读取description.xml，说明是主题文件
                const descriptionPath = await join(dragFiles, 'description.xml');
                const hasDescriptionFile = await exists(descriptionPath);
                if (hasDescriptionFile) { // 目录中存在description.xml文件
                    messageLoading(`正在加载主题`);
                    await 更新输入框(themeName);
                    await 生成应用按钮(dragFiles);
                    // 暴露给全局变量 - 拖入文件目录
                    window.dragFiles = dragFiles;
                    // 暴露给全局变量 - 拖入文件父目录
                    window.parentDir = await dirname(dragFiles);
                    // 暴露给全局变量 - 缓存目录
                    window.cacheDir = await 缓存文件(dragFiles);

                    预编译(window.cacheDir);
                    messageSuccess('主题加载成功', 1000);

                } else { // 目录中不存在description.xml文件
                    messageError('不是可处理的文件类型，支持未打包主题文件、打包后的mtz、编译反编译.9.png、install_apk');
                }
            } else {
                for (const path of normalizedPaths) {
                    // 如果能读取description.xml，说明是主题文件
                    const descriptionPath = await join(dragFiles, 'description.xml');
                    const hasDescriptionFile = await exists(descriptionPath);
                    if (hasDescriptionFile) {
                        await 打包主题(path, undefined, true);
                    }
                }
            }

        } catch (error) {
        }
    }

    // ==========================如果是.9.png文件========================== //
    else if (dragFiles.toLowerCase().match(/\.9\.png$/)) {  // 使用正则表达式精确匹配.9.png后缀

        // 过滤出所有.9.png文件，使用相同的正则表达式
        const dot9Files = normalizedPaths.filter(path => path.toLowerCase().match(/\.9\.png$/));

        try {
            await messageLoading('正在反编译点9图');
            // 遍历所有.9.png文件进行反编译
            for (const dot9File of dot9Files) {
                await 编译或反编译点9图(dot9File);
            }
            await messageSuccess(`成功处理 ${dot9Files.length} 个点9图`, 1000);
        } catch (error) {
            await messageError(`处理点9图失败: ${error}`);
        }
    }

    // ==========================如果是.png文件========================== //
    // else if (dragFiles.toLowerCase().endsWith('.png') && !dragFiles.toLowerCase().endsWith('.9.png')) {

    //     // 过滤出所有普通png文件(排除.9.png)
    //     const pngFiles = normalizedPaths.filter(path =>
    //         path.toLowerCase().endsWith('.png') && !path.toLowerCase().endsWith('.9.png')
    //     );

    //     await 图片裁切(pngFiles[ 0 ]);

    // }

    // ==========================如果是apk文件,调用adb命令install_apk========================== //
    else if (dragFiles.toLowerCase().endsWith('.apk')) {
        await install_apk(normalizedPaths);
    }

    // ==========================如果是其他文件==========================
    else {

        // 定义支持的文本文件扩展名
        const textFileExtensions = [
            '.log', '.properties', '.xml', '.json', '.txt', '.ini', '.cfg',
            '.conf', '.md', '.sh', '.bat', '.cmd', '.py', '.js', '.ts', '.html', '.css', '.sql', '.csv' ];
        // 字体文件扩展名
        const fontFileExtensions = [ '.ttf', '.otf', '.ttc', '.woff', '.woff2', '.eot' ];
        // 图片文件扩展名
        // const imageFileExtensions = [ '.png', '.webp' ];

        // 检查文件扩展名是否在支持列表中
        const fileExt = dragFiles.toLowerCase().substring(dragFiles.lastIndexOf('.'));
        if (textFileExtensions.includes(fileExt)) {
            await unicode转中文(normalizedPaths);
        } else if (fontFileExtensions.includes(fileExt)) {
            await 字体生成图片(normalizedPaths[ 0 ]);
            // } else if (imageFileExtensions.includes(fileExt)) {
            //     await 图片裁切(normalizedPaths[ 0 ]);
        } else {
            messageError('不是可处理的文件类型');
        }
    }
}

