import { invoke } from '@tauri-apps/api/core';

/**
 * 反编译.9.png图片
 * @param path .9.png文件路径
 * @returns Promise<void>
 */
export async function 反编译点9图(path: string): Promise<void> {

    try {
        // 输出路径与输入路径相同,实现覆盖原图片
        await invoke('反编译点9图', {
            filePath: path,
            outputPath: path
        });
    } catch (error) {
        console.error('点9图反编译失败:', error);
    }
}

/**
 * 编译.9.png图片
 * @param path .9.png文件路径
 * @returns Promise<void>
 */
export async function 编译点9图(path: string): Promise<void> {

    try {
        // 输出路径与输入路径相同,实现覆盖原图片
        await invoke('编译点9图', {
            filePath: path,
            outputPath: path
        });
    } catch (error) {
        console.error('点9图编译失败:', error);
    }
}


/**
 * 检查图片是否为点9图并进行相应处理
 * @param path 图片文件路径
 * @returns Promise<void>
 */
export async function 编译或反编译点9图(path: string): Promise<void> {
    try {
        // 输出路径与输入路径相同,实现覆盖原图片
        await invoke('编译或反编译点9图', {
            filePath: path,
            outputPath: path
        });
    } catch (error) {
        console.error('点9图处理失败:', error);
    }
}
