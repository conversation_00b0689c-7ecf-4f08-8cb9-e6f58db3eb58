import { messageSuccess, messageError } from './prompt_message';
import { Command } from 'tauri-plugin-shellx-api';
import { platform } from '@tauri-apps/plugin-os';
import { setContainerAni } from './ui_animation';
import { invoke } from '@tauri-apps/api/core';

// 设备类型定义
interface Device {
    id: string;
    type: string;
    status: string;
    model: string;
    marketName?: string;  // 添加市场名称字段
    displayName?: string; // 添加显示名称字段
}

// 设备状态变化回调类型
type DeviceChangeCallback = (devices: Device[]) => void;

// 全局声明
declare global {
    interface Window {
        updateDeviceList: () => Promise<void>;
        restartAdbServer: () => Promise<void>;
        closeDevicePanel: () => Promise<void>;
    }
}

/**
 * 获取正确的可执行文件路径
 * @param fileName 可执行文件名
 * @returns 完整的可执行文件路径
 */
async function getExecutablePath(fileName: string): Promise<string> {
    try {
        // 开发环境下使用相对路径，生产环境下使用绝对路径
        const isDev = import.meta.env.DEV;
        const currentPlatform = await platform();


        if (isDev) {
            // 开发环境：使用Command.sidecar默认查找机制
            // 在开发环境下，Tauri会自动查找src-tauri/binary目录
            const devPath = currentPlatform === 'windows' ? `${fileName}.exe` : fileName;
            return devPath;
        } else {
            // 生产环境：根据平台使用不同的路径策略
            try {
                const appPath = await invoke('get_app_path') as string;

                let fullPath: string;

                if (currentPlatform === 'macos') {
                    // macOS平台：appPath 已经是 MacOS 目录
                    fullPath = `${appPath}/${fileName}`;
                } else if (currentPlatform === 'windows') {
                    // Windows平台：可执行文件与主程序在同一目录下
                    fullPath = `${appPath}/${fileName}.exe`;
                } else {
                    // Linux平台：可执行文件与主程序在同一目录下
                    fullPath = `${appPath}/${fileName}`;
                }

                return fullPath;
            } catch (invokeError) {
                console.warn(`[getExecutablePath] 调用get_app_path失败: ${invokeError}`);
                // 如果获取应用路径失败，使用Command.sidecar的默认机制
                const fallbackPath = currentPlatform === 'windows' ? `${fileName}.exe` : fileName;
                return fallbackPath;
            }
        }
    } catch (error) {
        console.error('获取可执行文件路径失败:', error);
        // 最终降级方案：使用Command.sidecar的默认机制
        const currentPlatform = await platform();
        const fallbackPath = currentPlatform === 'windows' ? `${fileName}.exe` : fileName;
        return fallbackPath;
    }
}

// ========================= 设备管理模块 =========================

// 缓存设备列表和禁用状态
let cachedDevices: Device[] = [];

// 使用localStorage持久化存储禁用设备列表
const DISABLED_DEVICES_KEY = 'disabled_devices';
const disabledDevices: Set<string> = new Set(
    JSON.parse(localStorage.getItem(DISABLED_DEVICES_KEY) || '[]')
);

// Master device list to track devices more robustly and handle ADB flakiness.
let masterDeviceMap: Map<string, { device: Device; missCount: number; lastSeen: number }> = new Map();
const MAX_MISS_COUNT = 2; // 减少失联阈值，更快速检测设备变化
const DEVICE_TIMEOUT_MS = 15000; // 15秒设备超时

// ADB服务健康状态标志
let adbServerHealthy = true;
// ADB命令尝试次数
const MAX_RETRY_COUNT = 2;
// ADB重启尝试次数限制
const MAX_RESTART_ATTEMPTS = 2;
// 上次重启ADB服务的时间戳
let lastAdbRestartTime = 0;
// 重启冷却期（毫秒）- 缩短到15秒以提高响应速度
const RESTART_COOLDOWN_MS = 15000; // 15秒

// 添加并发控制变量
let concurrentAdbCommandCount = 0;
const MAX_CONCURRENT_ADB_COMMANDS = 6; // 增加并发数量以提高效率

// 添加连接稳定性监控
let consecutiveFailures = 0;
const MAX_CONSECUTIVE_FAILURES = 2; // 降低阈值，更快响应问题

// ADB健康检查相关变量
let lastSuccessfulDeviceCheck = Date.now();
const ADB_HEALTH_CHECK_INTERVAL = 30000; // 30秒健康检查间隔，减少频率
const ADB_RESPONSE_TIMEOUT = 5000; // 5秒ADB响应超时，缩短等待时间
let adbHealthCheckInterval: number | null = null;
let isAdbHealthy = true;

// 错误分类常量 - 用于智能重试判断
const ERROR_CATEGORIES = {
    // 不需要重试的错误类型（预期的正常情况或永久性错误）
    NON_RETRYABLE_ERRORS: [
        'No such file or directory',        // 文件不存在（常见于清理操作）
        'Permission denied',                // 权限不足
        'Invalid argument',                 // 参数错误
        'File exists',                     // 文件已存在
        'Directory not empty',             // 目录非空
        'Operation not permitted',         // 操作不被允许
        'Read-only file system',          // 只读文件系统
        'No space left on device',       // 设备空间不足
        'Is a directory',                 // 目标是目录
        'Not a directory',                // 目标不是目录
        'Command not found',              // 命令未找到
        'Bad substitution',               // shell替换错误
        'Syntax error'                    // 语法错误
    ],

    // 需要重试的错误类型（临时性、可恢复的错误）
    RETRYABLE_ERRORS: [
        'device not found',               // 设备未找到
        'error: no devices',              // 没有设备连接
        'daemon not running',             // ADB守护进程未运行
        'device offline',                 // 设备离线
        'device unauthorized',            // 设备未授权（可能临时的）
        'protocol fault',                 // 协议错误
        'connection refused',             // 连接被拒绝
        'broken pipe',                   // 管道断开
        'timeout',                       // 超时（包含各种超时）
        'server not found',              // 服务器未找到
        'connection lost',               // 连接丢失
        'device busy'                    // 设备忙碌
    ],

    // 文件操作相关的特殊错误（需要特殊处理）
    FILE_OPERATION_ERRORS: [
        'No such file or directory',
        'File exists',
        'Directory not empty',
        'Is a directory',
        'Not a directory'
    ]
};

/**
 * 分析错误类型，判断是否需要重试
 * @param errorMessage 错误信息
 * @param command adb命令参数数组
 * @returns 错误分析结果
 */
function analyzeError(errorMessage: string, command: string[]): {
    shouldRetry: boolean;
    isFileOperationError: boolean;
    isSilentError: boolean;
    category: string;
} {
    const errorMsg = errorMessage.toLowerCase();

    // 检查是否为文件操作命令
    const isFileCommand = command.some(arg =>
        [ 'rm', 'mv', 'cp', 'mkdir', 'rmdir', 'touch' ].includes(arg)
    );

    // 检查是否为不可重试错误
    const isNonRetryable = ERROR_CATEGORIES.NON_RETRYABLE_ERRORS.some(pattern =>
        errorMsg.includes(pattern.toLowerCase())
    );

    // 检查是否为可重试错误
    const isRetryable = ERROR_CATEGORIES.RETRYABLE_ERRORS.some(pattern =>
        errorMsg.includes(pattern.toLowerCase())
    );

    // 检查是否为文件操作错误
    const isFileOperationError = ERROR_CATEGORIES.FILE_OPERATION_ERRORS.some(pattern =>
        errorMsg.includes(pattern.toLowerCase())
    );

    // 特殊处理：删除不存在的文件应该被视为成功
    const isDeleteNonExistentFile = isFileCommand &&
        command.includes('rm') &&
        errorMsg.includes('no such file or directory');

    return {
        shouldRetry: !isNonRetryable && isRetryable,
        isFileOperationError: isFileOperationError,
        isSilentError: isDeleteNonExistentFile, // 删除不存在的文件应该静默处理
        category: isNonRetryable ? 'non-retryable' :
            isRetryable ? 'retryable' : 'unknown'
    };
}

// 设备监控相关变量
let deviceMonitorInterval: number | null = null;
const DEVICE_MONITOR_INTERVAL_MS = 3000; // 设备面板打开时3秒监控间隔
let isMonitoringDevices = false;
let deviceChangeCallbacks: Set<DeviceChangeCallback> = new Set();

// 设备状态缓存，用于检测变化
let lastDeviceSnapshot: string = '';

// 添加设备获取缓存机制
let lastDevicesFetchTime = 0;
const DEVICE_CACHE_DURATION = 5000; // 5秒缓存时间
let cachedDeviceResult: Device[] = [];

/**
 * 注册设备状态变化回调
 * @param callback 回调函数
 */
export function onDeviceChange(callback: DeviceChangeCallback): () => void {
    deviceChangeCallbacks.add(callback);
    return () => deviceChangeCallbacks.delete(callback);
}

/**
 * 触发设备状态变化事件
 * @param devices 当前设备列表
 */
function triggerDeviceChange(devices: Device[]): void {
    deviceChangeCallbacks.forEach(callback => {
        try {
            callback(devices);
        } catch (error) {
            console.error('设备状态变化回调执行失败:', error);
        }
    });
}

// 移除智能监控相关函数，改为简单的按需监控

/**
 * 开始设备监控 - 简化版本，固定3秒间隔
 */
function startDeviceMonitoring(): void {
    if (isMonitoringDevices) return;

    console.log('启动设备监控，3秒间隔');
    isMonitoringDevices = true;

    deviceMonitorInterval = setInterval(async () => {
        try {
            // 如果当前有adb命令正在执行，跳过本次监控以避免冲突
            if (concurrentAdbCommandCount >= MAX_CONCURRENT_ADB_COMMANDS - 1) {
                return;
            }

            // 静默检查设备，不显示错误消息，不使用缓存机制
            const devices = await getDevicesInternal(0, true);

            // 检查设备列表是否发生变化
            const currentSnapshot = JSON.stringify(devices.map(d => ({ id: d.id, status: d.status, model: d.model })));
            if (currentSnapshot !== lastDeviceSnapshot) {
                lastDeviceSnapshot = currentSnapshot;

                // 触发设备变化事件
                triggerDeviceChange(devices);

                // 自动更新UI
                const deviceContainer = document.getElementById('device-container');
                if (deviceContainer && deviceContainer.classList.contains('device-container-on')) {
                    await updateDeviceList();
                }
            }
        } catch (error) {
            // 静默处理监控错误，避免频繁弹出错误消息
            console.warn('设备监控检查失败:', error);
        }
    }, DEVICE_MONITOR_INTERVAL_MS);
}

/**
 * 停止设备监控
 */
function stopDeviceMonitoring(): void {
    if (deviceMonitorInterval) {
        console.log('停止设备监控');
        clearInterval(deviceMonitorInterval);
        deviceMonitorInterval = null;
    }
    isMonitoringDevices = false;
}

/**
 * ADB健康检查 - 检测ADB进程是否假死
 */
async function performAdbHealthCheck(): Promise<boolean> {
    try {
        const startTime = Date.now();

        // 创建一个带超时的Promise来检测ADB响应
        const healthCheckPromise = new Promise<string>(async (resolve, reject) => {
            try {
                const adbPath = await getExecutablePath('adb');
                const command = Command.sidecar(adbPath, [ 'version' ]);
                const result = await command.execute();
                resolve(result.stdout);
            } catch (error) {
                reject(error);
            }
        });

        const timeoutPromise = new Promise<never>((_, reject) => {
            setTimeout(() => reject(new Error('ADB健康检查超时')), ADB_RESPONSE_TIMEOUT);
        });

        await Promise.race([ healthCheckPromise, timeoutPromise ]);

        const responseTime = Date.now() - startTime;
        if (responseTime > ADB_RESPONSE_TIMEOUT / 2) {
            console.warn(`ADB响应较慢: ${responseTime}ms`);
        }

        lastSuccessfulDeviceCheck = Date.now();
        isAdbHealthy = true;
        return true;
    } catch (error) {
        console.error('ADB健康检查失败:', error);
        isAdbHealthy = false;
        return false;
    }
}

/**
 * 启动ADB健康监控
 */
function startAdbHealthMonitoring(): void {
    if (adbHealthCheckInterval) return;


    adbHealthCheckInterval = setInterval(async () => {
        const currentTime = Date.now();

        // 如果当前有adb命令正在执行，跳过健康检查以避免冲突
        if (concurrentAdbCommandCount >= MAX_CONCURRENT_ADB_COMMANDS - 2) {
            return;
        }

        // 检查上次成功检查是否过久
        if (currentTime - lastSuccessfulDeviceCheck > ADB_HEALTH_CHECK_INTERVAL * 2) {
            console.warn('ADB长时间无响应，可能已假死');
            isAdbHealthy = false;
        }

        // 执行健康检查
        const isHealthy = await performAdbHealthCheck();

        if (!isHealthy) {
            console.warn('检测到ADB不健康，尝试恢复...');
            try {
                // 强制重启ADB服务
                adbServerHealthy = false;
                await restartAdbServer();
            } catch (error) {
                console.error('ADB恢复失败:', error);
            }
        }
    }, ADB_HEALTH_CHECK_INTERVAL);
}

/**
 * 停止ADB健康监控
 */
function stopAdbHealthMonitoring(): void {
    if (adbHealthCheckInterval) {
        clearInterval(adbHealthCheckInterval);
        adbHealthCheckInterval = null;
    }
}

/**
 * 设备连接性测试 - 通过ping命令验证设备真实连接状态
 * @param deviceId 设备ID
 * @returns 设备是否真实可达
 */
async function pingDevice(deviceId: string): Promise<boolean> {
    try {
        // 使用多个命令组合来验证设备连接性
        const tests = [
            [ 'get-state' ],
            [ 'shell', 'echo', 'ping_test' ],
            [ 'shell', 'getprop', 'ro.build.version.sdk' ]
        ];

        for (const testCommand of tests) {
            try {
                const result = await adb([ '-s', deviceId, ...testCommand ], false);
                if (testCommand[ 0 ] === 'get-state' && result.trim() !== 'device') {
                    return false;
                }
                if (testCommand.includes('echo') && !result.includes('ping_test')) {
                    return false;
                }
                if (testCommand.includes('getprop') && !result.trim()) {
                    return false;
                }
            } catch (error) {
                console.warn(`设备${deviceId}连接性测试失败:`, testCommand, error);
                return false;
            }
        }

        return true;
    } catch (error) {
        console.error(`设备${deviceId}ping测试失败:`, error);
        return false;
    }
}

/**
 * 保存禁用设备列表到本地存储
 */
function saveDisabledDevices(): void {
    try {
        localStorage.setItem(DISABLED_DEVICES_KEY, JSON.stringify([ ...disabledDevices ]));
    } catch (error) {
        console.error('保存禁用设备列表失败:', error);
    }
}

/**
 * 获取设备完整信息 - 包括型号、市场名称等
 * @param deviceId 设备ID
 * @returns 设备完整信息对象
 */
async function getDeviceInfo(deviceId: string): Promise<{ model: string, marketName: string, displayName: string }> {
    // 检查是否已有缓存的设备信息
    const cachedDevice = masterDeviceMap.get(deviceId);
    if (cachedDevice && cachedDevice.device.displayName && cachedDevice.device.displayName !== '未知型号') {
        return {
            model: cachedDevice.device.model,
            marketName: cachedDevice.device.marketName || '',
            displayName: cachedDevice.device.displayName
        };
    }

    // 等待并发限制
    while (concurrentAdbCommandCount >= MAX_CONCURRENT_ADB_COMMANDS) {
        await new Promise(resolve => setTimeout(resolve, 50));
    }

    concurrentAdbCommandCount++;
    try {
        // 并行获取多个设备属性
        const [ brand, model, marketName, productName ] = await Promise.all([
            adb([ '-s', deviceId, 'shell', 'getprop', 'ro.product.brand' ], false).catch(() => '未知'),
            adb([ '-s', deviceId, 'shell', 'getprop', 'ro.product.model' ], false).catch(() => '未知型号'),
            adb([ '-s', deviceId, 'shell', 'getprop', 'ro.product.marketname' ], false).catch(() => ''),
            adb([ '-s', deviceId, 'shell', 'getprop', 'ro.product.name' ], false).catch(() => '')
        ]);

        // 整理并确定最终显示的名称
        const brandName = brand.trim();
        const modelName = model.trim();
        const marketNameProp = marketName.trim();
        const productNameProp = productName.trim();
        let finalDisplayName = '';

        // 优先级：市场名称 > 品牌+型号 > 产品名称 > 基本型号
        if (marketNameProp) {
            finalDisplayName = marketNameProp;
        } else if (brandName && brandName !== '未知' && modelName) {
            finalDisplayName = `${brandName} ${modelName}`;
        } else if (productNameProp) {
            finalDisplayName = productNameProp;
        } else {
            finalDisplayName = modelName || '未知型号';
        }

        consecutiveFailures = 0; // 重置连续失败计数
        return {
            model: modelName || '未知型号',
            marketName: marketNameProp,
            displayName: finalDisplayName
        };
    } catch (error) {
        console.error(`获取设备 ${deviceId} 完整信息失败:`, error);
        consecutiveFailures++;
        return {
            model: '未知型号',
            marketName: '',
            displayName: '未知型号'
        };
    } finally {
        concurrentAdbCommandCount--;
    }
}

/**
 * 检查设备是否真实存在并可访问 - 增强版本，包含深度连接性测试
 * @param deviceId 设备ID
 * @returns 设备是否可访问
 */
async function isDeviceAccessible(deviceId: string): Promise<boolean> {
    try {
        // 首先进行基本状态检查
        const state = await adb([ '-s', deviceId, 'get-state' ], false);
        if (state.trim() !== 'device') {
            return false;
        }

        // 如果基本检查通过，进行深度连接性测试
        return await pingDevice(deviceId);
    } catch {
        return false;
    }
}

/**
 * 获取所有已连接的设备信息(包括USB和无线连接) - 带缓存机制
 * @param restartAttempts 已尝试重启的次数，用于限制递归调用
 * @param silent 是否静默模式，不显示错误消息
 * @returns 返回设备信息数组,包含id、type、status、model等信息
 */
export async function getDevices(restartAttempts: number = 0, silent: boolean = false): Promise<Device[]> {
    const currentTime = Date.now();

    // 检查缓存是否有效（5秒内只获取一次）
    if (currentTime - lastDevicesFetchTime < DEVICE_CACHE_DURATION && cachedDeviceResult.length > 0) {
        console.log('使用缓存的设备列表');
        return cachedDeviceResult;
    }

    // 获取最新设备列表
    const devices = await getDevicesInternal(restartAttempts, silent);

    // 更新缓存
    lastDevicesFetchTime = currentTime;
    cachedDeviceResult = devices;

    return devices;
}

/**
 * 内部获取设备方法 - 不使用缓存
 * @param restartAttempts 已尝试重启的次数，用于限制递归调用
 * @param silent 是否静默模式，不显示错误消息
 * @returns 返回设备信息数组,包含id、type、status、model等信息
 */
async function getDevicesInternal(restartAttempts: number = 0, silent: boolean = false): Promise<Device[]> {
    try {
        // 首先确保ADB服务健康
        if (!adbServerHealthy) {
            await ensureAdbServer();
        }

        // 执行 adb devices 命令获取设备列表
        const result = await adb([ 'devices', '-l' ], true); // 使用 -l 参数获取更详细信息

        // 解析输出结果
        const lines = result.trim().split('\n');
        lines.shift(); // 移除第一行的 "List of devices attached"

        const currentDeviceIds = new Set<string>();
        const rawDevices: { id: string, status: string }[] = [];
        const currentTime = Date.now();

        for (const line of lines.filter(line => line.trim())) {
            const parts = line.trim().split(/\s+/);
            const deviceId = parts[ 0 ];
            const status = parts[ 1 ];

            if (!deviceId || deviceId === 'List') continue;

            currentDeviceIds.add(deviceId);
            rawDevices.push({ id: deviceId, status: status });
        }

        // 并行检查所有设备的可访问性
        const deviceAccessibilityChecks = await Promise.allSettled(
            rawDevices.map(async ({ id, status }) => {
                if (status !== 'device') return { id, status, accessible: false };

                const accessible = await isDeviceAccessible(id);
                return { id, status, accessible };
            })
        );

        // 更新主列表中设备的状态
        const accessibleDevices = new Set<string>();

        deviceAccessibilityChecks.forEach((result) => {
            if (result.status === 'fulfilled' && result.value.accessible) {
                accessibleDevices.add(result.value.id);
            }
        });

        // 更新主列表中设备的失联计数和最后见到时间
        for (const [ deviceId, data ] of masterDeviceMap.entries()) {
            if (accessibleDevices.has(deviceId)) {
                // 设备仍然可访问
                data.missCount = 0;
                data.lastSeen = currentTime;
            } else {
                // 设备不可访问，增加失联计数
                data.missCount++;

                // 检查是否超时
                if (currentTime - data.lastSeen > DEVICE_TIMEOUT_MS) {
                    data.missCount = MAX_MISS_COUNT; // 直接标记为需要移除
                }
            }
        }

        // 移除失联次数超过阈值或超时的设备
        for (const [ deviceId, data ] of masterDeviceMap.entries()) {
            if (data.missCount >= MAX_MISS_COUNT) {
                masterDeviceMap.delete(deviceId);
            }
        }

        // 并行处理新设备和现有设备的型号获取
        const deviceProcessingPromises = rawDevices.map(async ({ id, status }) => {
            if (!accessibleDevices.has(id) && status !== 'unauthorized') return null;

            if (masterDeviceMap.has(id)) {
                // 已存在的设备，更新状态
                const entry = masterDeviceMap.get(id)!;
                entry.device.status = status;
                entry.missCount = 0;
                entry.lastSeen = currentTime;
                return entry.device;
            } else {
                // 发现新设备
                const isWireless = id.includes(':');

                // 并行获取设备完整信息，如果失败则使用默认值
                let deviceInfo = { model: '未知型号', marketName: '', displayName: '未知型号' };
                try {
                    if (status === 'device') {
                        deviceInfo = await getDeviceInfo(id);
                    }
                } catch (error) {
                    console.warn(`获取设备 ${id} 完整信息失败，使用默认值:`, error);
                }

                const newDevice: Device = {
                    id: id,
                    type: isWireless ? '无线连接' : 'USB连接',
                    status: status,
                    model: deviceInfo.model,
                    marketName: deviceInfo.marketName,
                    displayName: deviceInfo.displayName
                };

                masterDeviceMap.set(id, {
                    device: newDevice,
                    missCount: 0,
                    lastSeen: currentTime
                });

                return newDevice;
            }
        });

        // 等待所有设备处理完成
        await Promise.allSettled(deviceProcessingPromises);

        const devices = Array.from(masterDeviceMap.values()).map(data => data.device);

        // 验证设备状态
        const unauthorizedDevice = devices.find(device => device.status === 'unauthorized');
        if (unauthorizedDevice && !silent) {
            throw new Error('设备未授权，请在设备上允许调试');
        }

        const onlineDevices = devices.filter(device => device.status !== 'offline');
        if (devices.length > 0 && onlineDevices.length === 0 && !silent) {
            throw new Error('所有设备均处于离线状态，请检查USB连接');
        }

        // ADB服务状态正常
        adbServerHealthy = true;
        consecutiveFailures = 0; // 重置连续失败计数

        cachedDevices = onlineDevices; // 更新缓存

        // 不再自动启动监控，改为按需启动
        return cachedDevices;
    } catch (error) {
        if (!silent) {
            console.error('获取设备列表失败:', error);
        }

        // 更加保守的重启策略
        if (String(error).includes('无法识别') ||
            String(error).includes('daemon not running') ||
            (consecutiveFailures >= MAX_CONSECUTIVE_FAILURES)) {

            // 标记ADB服务不健康
            adbServerHealthy = false;

            const currentTime = Date.now();
            const timeSinceLastRestart = currentTime - lastAdbRestartTime;

            // 检查是否超过重启尝试次数或者正在冷却期
            if (restartAttempts >= MAX_RESTART_ATTEMPTS) {
                if (!silent) {
                    console.warn('已达到ADB重启最大尝试次数，放弃重试');
                }
                cachedDevices = [];
                consecutiveFailures = 0; // 重置计数
                throw new Error('ADB服务无法正常启动，请手动重启ADB服务或检查设备连接');
            } else if (timeSinceLastRestart < RESTART_COOLDOWN_MS && restartAttempts > 0) {
                if (!silent) {
                    console.warn(`ADB服务在冷却期内，剩余 ${Math.round((RESTART_COOLDOWN_MS - timeSinceLastRestart) / 1000)} 秒`);
                }
                cachedDevices = [];
                throw new Error('ADB服务正在冷却中，请稍后重试');
            }

            // 尝试自动重启ADB服务
            try {
                if (!silent) {
                }
                await restartAdbServer();
                lastAdbRestartTime = Date.now(); // 更新最后重启时间
                consecutiveFailures = 0; // 重置连续失败计数
                // 重启后再次尝试获取设备，增加重启计数
                return await getDevices(restartAttempts + 1, silent);
            } catch (restartError) {
                if (!silent) {
                    console.error('自动重启ADB服务失败:', restartError);
                }
                cachedDevices = [];
                throw new Error('重启ADB服务失败，请手动重启ADB服务');
            }
        }

        cachedDevices = [];
        throw error;
    }
}

// 标记是否正在重启ADB服务
let isRestarting = false;

/**
 * 确保 ADB 服务器正在运行 - 优化版本，避免重复启动进程
 * 注意：ADB使用客户端-服务器架构，我们只需要确保服务器运行，不需要持久化进程引用
 */
async function ensureAdbServer(): Promise<void> {
    try {
        const adbPath = await getExecutablePath('adb');

        // 首先检查ADB服务器是否已经运行
        try {
            const checkCommand = Command.sidecar(adbPath, [ 'devices' ]);
            await checkCommand.execute();
            // 如果成功，说明服务器已经运行
            adbServerHealthy = true;
            return;
        } catch {
            // 服务器未运行，需要启动
        }

        // 启动ADB服务器（这是一个一次性操作，不需要保持进程引用）
        const startCommand = Command.sidecar(adbPath, [ 'start-server' ]);
        await startCommand.execute();

        // 等待服务器启动完成
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 验证服务器是否成功启动
        const verifyCommand = Command.sidecar(adbPath, [ 'devices' ]);
        await verifyCommand.execute();

        adbServerHealthy = true;
    } catch (error) {
        console.error('启动 ADB 服务失败:', error);
        adbServerHealthy = false;
        throw error;
    }
}

/**
 * 重启ADB服务器
 * 在ADB假死时手动调用此方法可恢复连接
 */
export async function restartAdbServer(): Promise<void> {
    if (isRestarting) {
        return;
    }

    // 检查冷却期
    const currentTime = Date.now();
    const timeSinceLastRestart = currentTime - lastAdbRestartTime;
    if (timeSinceLastRestart < RESTART_COOLDOWN_MS) {
        console.warn(`ADB服务重启过于频繁，请等待${Math.round((RESTART_COOLDOWN_MS - timeSinceLastRestart) / 1000)}秒后再试`);
        return;
    }

    isRestarting = true;
    try {
        messageSuccess('正在重启ADB服务...');

        const adbPath = await getExecutablePath('adb');

        // 停止ADB服务器
        try {
            const killCommand = Command.sidecar(adbPath, [ 'kill-server' ]);
            await killCommand.execute();
        } catch (error) {
            console.error('停止ADB服务失败:', error);
        }

        // 等待服务完全停止
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 重新启动ADB服务器
        await ensureAdbServer();

        adbServerHealthy = true;
        messageSuccess('ADB服务重启成功');
    } catch (error) {
        console.error('重启ADB服务失败:', error);
        messageError('重启ADB服务失败，请手动重启电脑上的ADB服务');
        adbServerHealthy = false;
    } finally {
        isRestarting = false;
        // 更新最后重启时间
        lastAdbRestartTime = Date.now();
    }
}

/**
 * 对单个设备执行adb命令 - 增强版本，包含设备状态检查
 * @param args adb命令参数数组
 * @param withRetry 是否在失败时重试（可选）
 * @returns 返回命令执行结果
 */
export async function adb(args: string[], withRetry: boolean = false): Promise<string> {
    let retryCount = 0;
    let lastError: any = null;

    // 等待并发限制，避免ADB服务过载
    while (concurrentAdbCommandCount >= MAX_CONCURRENT_ADB_COMMANDS) {
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    concurrentAdbCommandCount++;

    try {
        // 如果是针对特定设备的命令，先检查设备状态
        const deviceId = args.includes('-s') ? args[ args.indexOf('-s') + 1 ] : null;
        if (deviceId && !args.includes('devices') && !args.includes('get-state')) {
            // 快速检查设备是否仍然可访问
            try {
                const quickCheck = await adb([ '-s', deviceId, 'get-state' ], false);
                if (quickCheck.trim() !== 'device') {
                    // 设备状态异常，触发设备列表更新
                    setTimeout(() => getDevices(0, true), 0); // 异步更新，不阻塞当前命令
                }
            } catch {
                // 设备可能已断开，触发设备列表更新
                setTimeout(() => getDevices(0, true), 0);
            }
        }

        while (retryCount <= (withRetry ? MAX_RETRY_COUNT : 0)) {
            try {
                // 确保 ADB 服务器运行
                if (args[ 0 ] === 'devices' || !adbServerHealthy) {
                    await ensureAdbServer();
                }

                // 执行实际的 adb 命令
                const adbPath = await getExecutablePath('adb');
                const command = Command.sidecar(adbPath, args);

                // 增加命令执行超时控制 - 对pull/push命令使用更长的超时时间
                const isPullPushCommand = args.includes('pull') || args.includes('push');
                const timeoutDuration = isPullPushCommand ? 120000 : 30000; // pull/push命令2分钟超时，其他命令30秒
                const timeoutPromise = new Promise<never>((_, reject) => {
                    setTimeout(() => reject(new Error('ADB命令执行超时')), timeoutDuration);
                });

                const output = await Promise.race([
                    command.execute(),
                    timeoutPromise
                ]);

                // 关键修复：检查命令退出码。只有当退出码为0时，命令才算成功。
                if (output.code !== 0) {
                    // 对于shell命令，真正的错误信息通常在stderr中
                    throw new Error(output.stderr || `命令执行失败，退出码: ${output.code}`);
                }

                // 成功执行，标记ADB服务健康并重置失败计数
                adbServerHealthy = true;
                consecutiveFailures = 0;
                return output.stdout;
            } catch (error: any) {
                lastError = error;
                const errorMsg = error.stderr || error.message || String(error);

                // 分析错误类型
                const errorAnalysis = analyzeError(errorMsg, args);

                // 对于静默错误（如删除不存在的文件），直接返回空字符串表示成功
                if (errorAnalysis.isSilentError) {
                    console.log(`文件清理操作：${args.join(' ')} - 文件不存在，视为操作成功`);
                    return '';
                }

                // 根据错误分析决定是否显示警告日志
                if (!errorAnalysis.isSilentError) {
                    const logLevel = errorAnalysis.shouldRetry ? 'warn' : 'error';
                    const retryInfo = withRetry ? ` (尝试 ${retryCount + 1}/${MAX_RETRY_COUNT + 1})` : '';
                    const categoryInfo = ` [${errorAnalysis.category}]`;

                    if (logLevel === 'warn') {
                        console.warn(`adb命令执行失败${retryInfo}${categoryInfo}: ${errorMsg}`);
                    } else {
                        console.error(`adb命令执行失败${categoryInfo}: ${errorMsg}`);
                    }
                }

                // 增加失败计数（但静默错误和文件操作错误不计入）
                if (!errorAnalysis.isSilentError && !errorAnalysis.isFileOperationError) {
                    consecutiveFailures++;
                }

                // 智能重试判断：只对分析后确认需要重试的错误进行重试
                if (withRetry && retryCount < MAX_RETRY_COUNT && errorAnalysis.shouldRetry) {
                    retryCount++;

                    // 只有在连续失败达到阈值时才重启ADB服务
                    if (consecutiveFailures >= MAX_CONSECUTIVE_FAILURES) {
                        try {
                            adbServerHealthy = false;
                            await restartAdbServer();
                            consecutiveFailures = 0; // 重置失败计数
                        } catch (restartError) {
                            console.error('自动重启ADB服务失败:', restartError);
                        }
                    }

                    // 增加延迟时间，给设备和服务更多恢复时间
                    const delay = Math.min(2000 * retryCount, 5000); // 递增延迟，最大5秒
                    await new Promise(resolve => setTimeout(resolve, delay));
                    continue;
                }

                throw error;
            }
        }

        throw lastError;
    } finally {
        concurrentAdbCommandCount--;
    }
}

/**
 * 对所有设备执行adb命令,每个型号的设备只选择一个执行 - 增强版本，实时检查设备状态
 * @param command adb命令参数数组
 * @param showDeviceCount 是否显示设备数量
 * @returns 返回每个设备的执行结果
 */
export async function adb_all(command: string[], showDeviceCount: boolean = false): Promise<{ [ key: string ]: unknown }> {
    try {
        // 总是检查设备状态以确保最新
        const currentDevices = await getDevices();

        // 如果缓存的设备列表为空或者需要显示设备数量，则强制更新
        if (showDeviceCount || cachedDevices.length === 0 || currentDevices.length !== cachedDevices.length) {
            cachedDevices = currentDevices;
        }

        // 存储每个设备的执行结果
        const results: { [ key: string ]: unknown } = {};
        // 存储执行过程中的错误
        const errors: { deviceId: string; model: string; error: unknown }[] = [];

        // 按型号分组设备
        const devicesByModel = groupDevicesByModel(cachedDevices);

        // 创建所有待执行命令的Promise数组，实现并行执行提高效率
        const commandPromises = Array.from(devicesByModel.entries()).map(
            async ([ _model, devices ]) => {
                const selectedDevice = selectDeviceForExecution(devices);
                if (!selectedDevice) return;

                try {
                    // 构建完整的命令数组
                    const deviceCommand = [ '-s', selectedDevice.id, ...command ];
                    const result = await adb(deviceCommand, true);

                    // 检查结果是否包含错误信息
                    checkCommandResult(result);

                    // 去除结果中的尾随空格和换行符
                    results[ selectedDevice.id ] = result.trim();
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : String(error);

                    // 分析错误类型，对于文件操作的预期错误进行特殊处理
                    const errorAnalysis = analyzeError(errorMessage, command);

                    // 对于静默错误（如删除不存在的文件），不记录为错误
                    if (!errorAnalysis.isSilentError) {
                        errors.push({
                            deviceId: selectedDevice.id,
                            model: selectedDevice.model,
                            error: errorMessage
                        });
                    } else {
                        // 对于静默错误，记录为成功结果
                        results[ selectedDevice.id ] = '';
                        console.log(`设备 ${selectedDevice.model} (${selectedDevice.id}) 文件清理操作成功`);
                    }
                }
            }
        );

        // 等待所有命令执行完成
        await Promise.all(commandPromises);

        // 处理错误
        handleCommandErrors(errors);

        return results;
    } catch (error) {
        console.error('adb_all执行失败:', error);
        throw error;
    }
}

/**
 * 按显示名称分组设备
 * @param devices 设备列表
 * @returns 按显示名称分组的设备Map
 */
function groupDevicesByModel(devices: Device[]): Map<string, Device[]> {
    const devicesByModel = new Map<string, Device[]>();

    devices.forEach(device => {
        const groupKey = device.displayName || device.model;
        if (!devicesByModel.has(groupKey)) {
            devicesByModel.set(groupKey, []);
        }
        devicesByModel.get(groupKey)?.push(device);
    });

    return devicesByModel;
}

/**
 * 从设备列表中选择一个设备执行命令
 * @param devices 设备列表
 * @returns 选中的设备或null
 */
function selectDeviceForExecution(devices: Device[]): Device | null {
    const usbDevices = devices.filter(d => d.type === 'USB连接');
    const wirelessDevices = devices.filter(d => d.type === '无线连接');

    // 优先选择未被禁用的USB设备，其次选择未被禁用的无线设备
    return usbDevices.find(d => !disabledDevices.has(d.id)) ||
        wirelessDevices.find(d => !disabledDevices.has(d.id)) ||
        null;
}

/**
 * 检查命令执行结果是否包含错误
 * @param result 命令执行结果
 */
function checkCommandResult(result: string): void {
    if (result.includes('error:') ||
        result.includes('failed') ||
        result.includes('Permission denied')) {
        throw new Error(result);
    }
}

/**
 * 处理命令执行过程中的错误
 * @param errors 错误列表
 */
function handleCommandErrors(errors: { deviceId: string; model: string; error: unknown }[]): void {
    if (errors.length > 0) {
        const errorMessage = errors.map(e =>
            `设备 ${e.model} (${e.deviceId}) 执行失败: ${e.error}`
        ).join('\n');
        throw new Error(errorMessage);
    }
}

/**
 * 增强的设备连接健康检查
 * @returns 返回设备连接健康状态报告
 */
export async function checkDeviceHealth(): Promise<{
    totalDevices: number;
    healthyDevices: number;
    issues: string[];
    recommendations: string[];
}> {
    const issues: string[] = [];
    const recommendations: string[] = [];

    try {
        const devices = await getDevices();
        const healthyDevices = devices.filter(d => d.status === 'device').length;

        // 检查ADB服务健康状态
        if (!adbServerHealthy || !isAdbHealthy) {
            issues.push('ADB服务状态异常');
            recommendations.push('尝试重启ADB服务');
        }

        // 检查ADB响应时间
        const timeSinceLastCheck = Date.now() - lastSuccessfulDeviceCheck;
        if (timeSinceLastCheck > ADB_HEALTH_CHECK_INTERVAL * 1.5) {
            issues.push(`ADB响应延迟过高: ${Math.round(timeSinceLastCheck / 1000)}秒未响应`);
            recommendations.push('ADB可能存在性能问题，建议重启');
        }

        // 检查设备连接状态
        const offlineDevices = devices.filter(d => d.status === 'offline');
        if (offlineDevices.length > 0) {
            issues.push(`发现${offlineDevices.length}个离线设备`);
            recommendations.push('检查USB连接或重新连接设备');
        }

        const unauthorizedDevices = devices.filter(d => d.status === 'unauthorized');
        if (unauthorizedDevices.length > 0) {
            issues.push(`发现${unauthorizedDevices.length}个未授权设备`);
            recommendations.push('在设备上确认USB调试授权');
        }

        // 检查连续失败次数
        if (consecutiveFailures > 0) {
            issues.push(`检测到${consecutiveFailures}次连续通信失败`);
            if (consecutiveFailures >= MAX_CONSECUTIVE_FAILURES) {
                recommendations.push('连接不稳定，建议重启ADB服务或检查设备');
            }
        }

        return {
            totalDevices: devices.length,
            healthyDevices,
            issues,
            recommendations
        };
    } catch (error) {
        issues.push(`设备检查失败: ${error}`);
        recommendations.push('检查ADB服务状态和设备连接');

        return {
            totalDevices: 0,
            healthyDevices: 0,
            issues,
            recommendations
        };
    }
}

/**
 * 显示设备选择对话框 - 通用版本，适用于所有需要设备选择的场景
 * @param devices 可用设备列表
 * @param title 对话框标题
 * @returns 用户选择的设备，如果取消则返回null
 */
async function showDeviceSelectionDialog(devices: any[], title: string): Promise<any | null> {
    return new Promise(async (resolve) => {
        // 1. 使用已有的设备信息，如果没有displayName则动态获取
        const detailedDevicesPromises = devices.map(async (device) => {
            try {
                // 如果设备已有displayName，直接使用
                if (device.displayName && device.displayName !== '未知型号') {
                    return { ...device, finalDisplayName: device.displayName };
                }

                // 否则动态获取设备信息
                const deviceInfo = await getDeviceInfo(device.id);
                return {
                    ...device,
                    finalDisplayName: deviceInfo.displayName,
                    model: deviceInfo.model,
                    marketName: deviceInfo.marketName,
                    displayName: deviceInfo.displayName
                };

            } catch (error) {
                console.warn(`获取设备 ${device.id} 详细信息失败:`, error);
                // 如果失败，则使用基本型号作为备用
                return { ...device, finalDisplayName: device.displayName || device.model || '未知设备' };
            }
        });

        // 等待所有设备信息都获取完毕
        const detailedDevices = await Promise.all(detailedDevicesPromises);

        // 2. 信息获取完毕后，再创建并显示对话框
        const modal = document.createElement('div');
        modal.className = 'special-overlay';

        const dialog = document.createElement('div');
        dialog.className = 'special-button-container';
        dialog.onclick = (e) => e.stopPropagation();

        const titleElement = document.createElement('div');
        titleElement.textContent = title;
        titleElement.className = 'overlay-title';
        dialog.appendChild(titleElement);

        detailedDevices.forEach((device) => {
            const deviceItem = document.createElement('div');
            deviceItem.className = 'device-item';

            // 创建左侧信息容器（设备名 + ID）
            const leftInfoContainer = document.createElement('div');
            leftInfoContainer.className = 'device-main-info';

            // 设备名称（显示完整名称）
            const deviceNameSpan = document.createElement('span');
            deviceNameSpan.className = 'device-name-simple';
            const displayName = device.finalDisplayName;

            // 设备ID
            const deviceIdSuffix = device.id.slice(-4);

            // 组合显示：设备名 ID
            deviceNameSpan.textContent = `${displayName} ${deviceIdSuffix}`;
            leftInfoContainer.appendChild(deviceNameSpan);

            // 创建右侧信息容器（设备类型）
            const rightInfoContainer = document.createElement('div');
            rightInfoContainer.className = 'device-secondary-info';

            const typeTag = document.createElement('span');
            typeTag.className = 'device-env-tag';
            typeTag.textContent = device.type || 'USB连接';
            rightInfoContainer.appendChild(typeTag);

            // 组装设备项目
            deviceItem.appendChild(leftInfoContainer);
            deviceItem.appendChild(rightInfoContainer);

            // 添加点击事件
            deviceItem.addEventListener('click', () => {
                cleanup();
                resolve(device);
            });

            dialog.appendChild(deviceItem);
        });

        const cancelButton = document.createElement('button');
        cancelButton.textContent = '取消';
        cancelButton.className = 'overlay-button close-button';
        cancelButton.onclick = () => {
            cleanup();
            resolve(null);
        };
        dialog.appendChild(cancelButton);

        // 清理函数
        const cleanup = () => {
            document.removeEventListener('keydown', handleKeyDown);
            if (modal.parentNode === document.body) {
                document.body.removeChild(modal);
            }
        };

        // 处理 ESC 键
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Escape') {
                cleanup();
                resolve(null);
            }
        };

        modal.appendChild(dialog);
        document.body.appendChild(modal);
        document.addEventListener('keydown', handleKeyDown);
    });
}

/**
 * 检查设备并选择设备（如果有多个设备）- 通用版本
 * @param title 弹窗标题
 * @returns 选中的设备对象，如果失败或取消则返回null
 */
export async function checkAndSelectDeviceGeneral(title: string = '选择设备'): Promise<any | null> {
    try {
        const devices = await getDevices();
        const onlineDevices = devices.filter(device => device.status === 'device');

        if (onlineDevices.length === 0) {
            throw new Error('没有设备连接，请连接设备后重试');
        }

        if (onlineDevices.length === 1) {
            const device = onlineDevices[ 0 ];
            const displayName = device.displayName || device.model;
            console.log(`使用设备: ${displayName} (${device.id})`);
            return device;
        }

        const selectedDevice = await showDeviceSelectionDialog(onlineDevices, title);
        if (!selectedDevice) {
            return null;
        }
        return selectedDevice;

    } catch (error) {
        throw new Error(`设备检查失败: ${error}`);
    }
}

/**
 * 检查已连接设备数量是否符合预期（保持向后兼容性）
 * @param expectedCount 期望的设备数量
 * @returns 返回检查结果,true表示符合预期,false表示不符合
 */
export async function 检查连接设备数量(expectedCount: number): Promise<boolean> {
    try {
        const deviceResults = await adb_all([ 'devices', '-l' ], true);

        // 获取所有设备的model
        const models = new Set();
        for (const deviceId in deviceResults) {
            const deviceInfo = deviceResults[ deviceId ] as string;
            // 从设备信息中提取model
            const modelMatch = deviceInfo.match(/model:(.*?)\s/);
            if (modelMatch) {
                models.add(modelMatch[ 1 ]);
            }
        }

        // 检查设备数量是否符合预期
        const deviceCount = Object.keys(deviceResults).length;
        if (deviceCount === expectedCount) {
            return true;
        } else {
            const { messageError } = await import('./prompt_message');
            messageError(`设备数量不符合预期，当前连接 ${deviceCount} 台设备，期望连接 ${expectedCount} 台设备`);
            return false;
        }
    } catch (error) {
        const { messageError } = await import('./prompt_message');
        messageError(`请检查设备连接状态`);
        return false;
    }
}

// ========================= UI交互模块 =========================

/**
 * 更新设备列表UI并绑定点击事件
 */
async function updateDeviceList() {
    console.log('updateDeviceList 函数被调用');
    try {
        // 清除设备缓存，强制获取最新信息
        lastDevicesFetchTime = 0;
        cachedDeviceResult = [];
        masterDeviceMap.clear(); // 清除主设备映射以确保重新获取完整信息

        const devices = await getDevices();
        const deviceListElement = document.getElementById('device-list');
        if (!deviceListElement) return;

        deviceListElement.innerHTML = createDeviceListHTML(devices);
        attachDeviceItemClickHandlers(deviceListElement);

        messageSuccess('设备列表更新成功');

        // 更新ADB状态指示器
        updateAdbStatusIndicator();
    } catch (error) {
        const deviceListElement = document.getElementById('device-list');
        if (deviceListElement) {
            deviceListElement.innerHTML = '';
        }
        console.error('更新设备列表失败:', error);

        // 更新ADB状态指示器显示错误状态
        updateAdbStatusIndicator(false);
    }
}

/**
 * 更新ADB服务状态指示器
 * @param isHealthy 是否健康（可选）
 */
function updateAdbStatusIndicator(isHealthy?: boolean) {
    const status = isHealthy !== undefined ? isHealthy : adbServerHealthy;
    const adbStatusIndicator = document.getElementById('adb-status-indicator');

    if (adbStatusIndicator) {
        adbStatusIndicator.className = status ? 'adb-status-healthy' : 'adb-status-error';
        adbStatusIndicator.title = status ? 'ADB服务状态正常' : 'ADB服务异常，点击修复';
    }
}

/**
 * 创建设备列表HTML
 * @param devices 设备列表
 * @returns 设备列表HTML字符串
 */
function createDeviceListHTML(devices: Device[]): string {
    return devices.map(device => {
        const deviceDiv = document.createElement('div');
        // 根据持久化的禁用状态设置初始class
        deviceDiv.className = disabledDevices.has(device.id) ? 'device-item-s' : 'device-item-n';
        // 优先显示displayName，如果没有则显示model
        const displayName = device.displayName || device.model;
        const deviceIdSuffix = device.id.slice(-4);
        deviceDiv.innerHTML = `${displayName} ${deviceIdSuffix} ${device.type}`;
        deviceDiv.setAttribute('data-device-id', device.id);
        return deviceDiv.outerHTML;
    }).join('');
}

/**
 * 为设备列表项添加点击事件处理
 * @param deviceListElement 设备列表元素
 */
function attachDeviceItemClickHandlers(deviceListElement: HTMLElement): void {
    deviceListElement.querySelectorAll('div[class*="device-item"]').forEach(element => {
        element.addEventListener('click', function (this: HTMLDivElement) {
            const deviceId = this.getAttribute('data-device-id');
            if (!deviceId) return;

            // 切换当前设备的状态
            toggleDeviceStatus(this, deviceId);
        });
    });
}

/**
 * 切换设备状态
 * @param element 设备DOM元素
 * @param deviceId 设备ID
 */
function toggleDeviceStatus(element: HTMLDivElement, deviceId: string): void {
    if (element.classList.contains('device-item-n')) {
        element.classList.remove('device-item-n');
        element.classList.add('device-item-s');
        disabledDevices.add(deviceId);
    } else {
        element.classList.remove('device-item-s');
        element.classList.add('device-item-n');
        disabledDevices.delete(deviceId);
    }

    // 持久化保存禁用状态
    saveDisabledDevices();
}


// ========================= 初始化 =========================

// 初始化设备列表UI和事件监听
(function initDeviceUI() {
    const container = document.getElementById('device-container');
    if (!container) {
        return;
    }

    let isUpdating = false; // 防止重复更新

    // 监听点击事件切换类名
    container.addEventListener('click', async (event) => {
        // 检查点击的目标是否是关闭或更新按钮
        const target = event.target as HTMLElement;
        if (target.classList.contains('device-close-container') ||
            target.classList.contains('device-update-container') ||
            target.closest('.device-close-button-container') ||
            target.closest('.device-update-button-container')) {
            // 如果点击的是按钮，不处理容器的点击事件
            return;
        }

        // 如果已经在展开状态,直接返回
        if (container.classList.contains('device-container-on')) {
            return;
        }

        console.log('打开设备面板，启动监控');
        container.classList.add('device-container-on');

        // 设置container缩放
        setContainerAni(0.9, 0);

        // 启动设备监控（3秒间隔）
        startDeviceMonitoring();

        // 首次进入时更新一次
        if (!isUpdating) {
            isUpdating = true;
            try {
                await updateDeviceList();
            } finally {
                isUpdating = false;
            }
        }
    });

    // 鼠标移出设备列表时关闭
    container.addEventListener('mouseleave', async () => {
        // 只有在完全展开时才关闭
        if (container.classList.contains('device-container-on')) {
            console.log('关闭设备面板，停止监控');

            // 停止设备监控
            stopDeviceMonitoring();

            // 关闭面板时获取一次设备状态（清除缓存以强制获取）
            try {
                lastDevicesFetchTime = 0; // 清除缓存
                await getDevices(0, true); // 静默获取一次最新状态
            } catch (error) {
                console.warn('关闭面板时获取设备状态失败:', error);
            }

            // 恢复container缩放
            setContainerAni(1, 1);

            container.classList.remove('device-container-on');
            container.classList.add('device-container');
        }
    });

    // 为关闭和更新按钮添加事件监听器
    const closeButton = container.querySelector('.device-close-container');
    const updateButton = container.querySelector('.device-update-container');

    if (closeButton) {
        closeButton.addEventListener('click', async (event) => {
            event.stopPropagation();
            event.preventDefault();
            console.log('关闭按钮被点击');
            await closeDevicePanel();
        });
    }

    if (updateButton) {
        updateButton.addEventListener('click', async (event) => {
            event.stopPropagation();
            event.preventDefault();
            console.log('更新按钮被点击');
            await updateDeviceList();
        });
    }
})();

/**
 * 关闭设备管理面板
 */
async function closeDevicePanel(): Promise<void> {
    console.log('closeDevicePanel 函数被调用');
    // 使用ID选择器，更可靠
    const container = document.getElementById('device-container');

    if (container) {
        // 无论当前状态如何，都执行关闭操作
        if (container.classList.contains('device-container-on')) {
            console.log('手动关闭设备面板，停止监控');

            // 停止设备监控
            stopDeviceMonitoring();

            // 关闭面板时获取一次设备状态（清除缓存以强制获取）
            try {
                lastDevicesFetchTime = 0; // 清除缓存
                await getDevices(0, true); // 静默获取一次最新状态
            } catch (error) {
                console.warn('关闭面板时获取设备状态失败:', error);
            }

            // 恢复container缩放
            setContainerAni(1, 1);

            // 移除展开状态的类
            container.classList.remove('device-container-on');

            // 确保有基础类
            if (!container.classList.contains('device-container')) {
                container.classList.add('device-container');
            }
        } else {
            // 即使不在展开状态，也确保监控停止和状态同步
            stopDeviceMonitoring();
            setContainerAni(1, 1);

            // 确保类名正确
            container.classList.remove('device-container-on');
            if (!container.classList.contains('device-container')) {
                container.classList.add('device-container');
            }
        }
    } else {
        console.error('未找到设备容器元素 #device-container');
    }
}

// 导出到全局对象
window.updateDeviceList = updateDeviceList;
window.restartAdbServer = restartAdbServer;
window.closeDevicePanel = closeDevicePanel;

// 页面可见性变化时清除缓存以获取最新设备状态
document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
        // 页面可见时，清除缓存以获取最新设备状态
        lastDevicesFetchTime = 0;
        if (isMonitoringDevices) {
            getDevices(0, true).catch(console.warn);
        }
    }
});

// 在应用退出时清理 ADB 服务器和监控
window.addEventListener('beforeunload', async () => {
    // 停止所有监控
    stopDeviceMonitoring();
    stopAdbHealthMonitoring();

    try {
        const adbPath = await getExecutablePath('adb');
        const killCommand = Command.sidecar(adbPath, [ 'kill-server' ]);
        await killCommand.execute();
    } catch (error) {
        console.error('清理ADB服务失败:', error);
    }
});

// 添加全局错误处理，捕获未处理的Promise拒绝
window.addEventListener('unhandledrejection', (event) => {
    if (event.reason && String(event.reason).includes('adb')) {
        console.warn('捕获到ADB相关的未处理错误:', event.reason);
        // 对于ADB相关错误，尝试重置连接状态
        if (consecutiveFailures < MAX_CONSECUTIVE_FAILURES) {
            consecutiveFailures++;
        }
    }
});

// 导出设备监控控制函数供外部调用
export {
    startDeviceMonitoring,
    stopDeviceMonitoring,
    startAdbHealthMonitoring,
    stopAdbHealthMonitoring,
    pingDevice
};
