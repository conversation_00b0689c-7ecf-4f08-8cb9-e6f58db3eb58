// ContentProviderBinderTransformer.ts
// 内容提供者绑定器转换器：确保<ContentProviderBinder>标签的columns属性包含所有子<Variable>标签的column属性值
// import { XMLSerializer } from 'xmldom';
// import { format_xml } from './format_xml';
/**
 * 转换所有 <ContentProviderBinder> 标签，确保它们的 columns 属性包含所有子 <Variable> 标签的 column 属性值。
 * 如果 columns 属性不存在，则创建它。如果已存在，则添加新的列，避免重复。
 * 当 variableElements 为空时，不添加或更新 columns 属性。
 * @param root - manifest.xml 的根 Element 节点
 */
export const cpb_binder_command = (root: Element): void => {
    // 查找所有 <ContentProviderBinder> 元素
    const contentProviderBinders = Array.from(root.getElementsByTagName('ContentProviderBinder')) as Element[];

    if (contentProviderBinders.length === 0) {
        return;
    }

    // const serializer = new XMLSerializer();

    contentProviderBinders.forEach(binder => {
        // 获取 ContentProviderBinder 的 name 属性
        const binderName = binder.getAttribute('name');
        if (!binderName) {
            return;
        }

        // 查找或创建 ExternalCommands 节点
        let externalCommands = root.getElementsByTagName('ExternalCommands')[ 0 ] as Element;
        if (!externalCommands) {
            externalCommands = root.ownerDocument.createElement('ExternalCommands');
            root.appendChild(externalCommands);
        }

        // 查找所有 Trigger 节点
        let initTrigger = Array.from(externalCommands.getElementsByTagName('Trigger')).find(trigger =>
            trigger.getAttribute('action') === 'init,resume'
        ) as Element;

        // 如果没有找到 init,resume 的 Trigger，创建一个
        if (!initTrigger) {
            initTrigger = root.ownerDocument.createElement('Trigger');
            initTrigger.setAttribute('action', 'init,resume');
            externalCommands.appendChild(initTrigger);
        }

        // 检查是否已存在对应的 BinderCommand
        const existingCommand = Array.from(initTrigger.getElementsByTagName('BinderCommand')).find(cmd =>
            cmd.getAttribute('name') === binderName &&
            cmd.getAttribute('command') === 'refresh'
        );

        // 如果不存在，创建新的 BinderCommand
        if (!existingCommand) {
            const binderCommand = root.ownerDocument.createElement('BinderCommand');
            binderCommand.setAttribute('name', binderName);
            binderCommand.setAttribute('command', 'refresh');
            initTrigger.appendChild(binderCommand);
        }
    });
};
