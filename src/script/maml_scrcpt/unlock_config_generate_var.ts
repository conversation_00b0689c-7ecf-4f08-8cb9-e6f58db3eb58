// unlock_config_generate_var.ts
// 锁屏配置文件生成Var标签
import { readTextFile } from '@tauri-apps/plugin-fs';
import { DOMParser } from 'xmldom';

/**
 * 生成并添加<Var>节点到manifest.xml的根元素
 * @param configPath - 配置文件路径
 * @param rootElement - manifest.xml的根元素节点
 */
export async function unlock_config_generate_var(configPath: string, rootElement: Element): Promise<void> {
    /**
     * 处理单个XML文件，查找指定元素并创建<Var>节点
     * @param filePath - XML文件路径
     */
    async function processXmlFile(filePath: string): Promise<void> {
        try {
            const xmlContent = await readTextFile(filePath);
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(xmlContent, 'application/xml');
            const root = xmlDoc.documentElement;
            if (!root) {
                console.warn(`警告：文件${filePath}没有根元素`);
                return;
            }

            const elementsToProcess = [ 'CheckBox', 'StringInput', 'NumberInput', 'StringChoice', 'NumberChoice' ];

            elementsToProcess.forEach(tag => {
                const nodes = Array.from(root.getElementsByTagName(tag)) as Element[];
                nodes.forEach(node => {
                    const id = node.getAttribute('id');
                    let expression: string | null = null;
                    let type: string | null = null;

                    let item: Element | null;
                    switch (tag) {
                        case 'CheckBox':
                            expression = node.getAttribute('default');
                            type = 'number';
                            break;
                        case 'StringInput':
                            expression = node.getAttribute('default');
                            type = 'string';
                            break;
                        case 'NumberInput':
                            expression = node.getAttribute('default');
                            type = 'number';
                            break;
                        case 'StringChoice':
                            item = node.getElementsByTagName('Item')[ 0 ];
                            expression = item ? item.getAttribute('value') : null;
                            type = 'string';
                            break;
                        case 'NumberChoice':
                            item = node.getElementsByTagName('Item')[ 0 ];
                            expression = item ? item.getAttribute('value') : null;
                            type = 'number';
                            break;
                        default:
                            break;
                    }

                    if (id && expression && type) {
                        // 如果类型是 string，给 expression 添加单引号
                        if (type === 'string') {
                            expression = `'${expression}'`;
                        }
                        createVarNode(id, expression, type);
                    } else {
                        console.warn(`警告：在文件${filePath}中，${tag}元素缺少必要属性`);
                    }
                });
            });
        } catch (error) {
            console.error(`处理文件 ${filePath} 时发生错误:`, error);
        }
    }

    /**
     * 创建<Var>节点并添加到根元素下
     * @param name - Var的名称
     * @param expression - Var的表达式
     * @param type - Var的类型
     */
    function createVarNode(name: string, expression: string, type: string): void {
        // 检查是否已存在同名的 Var
        const existingVars = rootElement.getElementsByTagName('Var');
        for (let i = 0; i < existingVars.length; i++) {
            const varName = existingVars[ i ].getAttribute('name');
            if (varName === name) {
                // console.warn(`Var name="${name}" 已存在，跳过创建`);
                return;
            }
        }

        const varElement = rootElement.ownerDocument.createElement('Var');
        varElement.setAttribute('name', name);
        varElement.setAttribute('expression', expression);
        varElement.setAttribute('type', type);
        varElement.setAttribute('const', 'true');

        // 将<Var>节点添加到根元素下
        rootElement.appendChild(varElement);

        // console.log(`%c已添加默认声明\n%c<Var name="${name}" expression="${expression}" type="${type}" const="true" />`, 'color:red', 'color:#0055ff');
    }

    try {
        // 直接处理XML文件，不再尝试作为目录处理
        if (configPath.endsWith('.xml')) {
            await processXmlFile(configPath);
        } else {
            console.warn(`警告：文件 ${configPath} 不是XML文件，已跳过处理`);
        }
    } catch (error) {
        console.error(`处理配置文件时发生错误:`, error);
    }
}