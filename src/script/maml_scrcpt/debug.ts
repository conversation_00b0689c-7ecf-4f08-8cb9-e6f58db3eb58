// import { XMLSerializer } from 'xmldom';
// import { format_xml } from './format_xml';
import { isPrivilegedUser } from '../user_name';
/**
 * 将Debug标签转换为Group标签，并合并相同位置的Debug标签
 * @param root - 根元素
 */
export function debug(root: Element): void {
    // 检查是否存在RmDebug标签
    const rmDebugElements = Array.from(root.getElementsByTagName('RmDebug'));
    if (rmDebugElements.length > 0) {
        // 如果存在RmDebug标签，删除所有.log属性
        const allElements = root.getElementsByTagName('*');
        for (const element of Array.from(allElements)) {
            const attributes = Array.from(element.attributes);
            for (const attr of attributes) {
                if (attr.name.endsWith('.log')) {
                    // 提取基本属性名（去掉.log后缀）
                    const baseName = attr.name.replace('.log', '');
                    // 移除.log后缀属性，但保留属性值到基本属性名
                    element.removeAttribute(attr.name);
                    element.setAttribute(baseName, attr.value);
                }
            }
        }

        // 删除所有Debug标签
        const debugElements = Array.from(root.getElementsByTagName('Debug'));
        debugElements.forEach(element => {
            element.parentNode?.removeChild(element);
        });

        // 删除RmDebug标签本身
        rmDebugElements.forEach(element => {
            element.parentNode?.removeChild(element);
        });

        // 提前返回，不执行后续Debug处理逻辑
        return;
    }

    // 处理.log属性
    processLogAttributes(root);

    // 查找所有 Debug 标签
    let debugElements = Array.from(root.getElementsByTagName('Debug')) as Element[];

    // 如果没有Debug标签，直接返回
    if (!debugElements) {
        return;
    }

    // 创建一个Map来存储相同位置的Debug标签
    const positionMap = new Map<string, Element[]>();

    // 按位置分组Debug标签
    debugElements.forEach(debugElement => {
        const x = debugElement.getAttribute('x') || '0';
        const y = debugElement.getAttribute('y') || '0';
        const key = `${x},${y}`;

        if (!positionMap.has(key)) {
            positionMap.set(key, []);
        }
        positionMap.get(key)?.push(debugElement);
    });

    // 处理每组Debug标签
    positionMap.forEach((elements, _position) => {
        // 如果该位置只有一个Debug标签，直接处理
        if (elements.length === 1) {
            processDebugElement(elements[ 0 ], root);
            return;
        }

        // 合并多个Debug标签的textExp
        const mergedTextExp = elements
            .map(element => element.getAttribute('textExp') || '')
            .filter(exp => exp !== '')
            .join(',');

        // 使用第一个元素作为基础，但使用合并后的textExp
        const baseElement = elements[ 0 ];
        baseElement.setAttribute('textExp', mergedTextExp);

        // 处理合并后的Debug标签
        processDebugElement(baseElement, root);

        // 移除其他重复的Debug标签
        elements.slice(1).forEach(element => {
            element.parentNode?.removeChild(element);
        });
    });
}

// 将原来的Debug标签处理逻辑抽取为单独的函数
function processDebugElement(debugElement: Element, root: Element): void {

    // 创建 Group 元素
    const group = root.ownerDocument.createElement('Group');
    group.setAttribute('name', 'debugGroup');
    group.setAttribute('visibility', '1');

    // 获取Debug标签的x和y属性
    const x = debugElement.getAttribute('x') || '0';
    const y = debugElement.getAttribute('y') || '0';
    group.setAttribute('x', x);
    group.setAttribute('y', y);

    // 辅助函数：正确分割表达式，处理括号内的逗号
    function splitExpressions(text: string): string[] {
        const results: string[] = [];
        let current = '';
        let depth = 0;

        for (let i = 0; i < text.length; i++) {
            const char = text[ i ];

            if (char === '(' || char === '[') {
                depth++;
                current += char;
            } else if (char === ')' || char === ']') {
                depth--;
                current += char;
            } else if (char === ',' && depth === 0) {
                results.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }

        if (current.trim()) {
            results.push(current.trim());
        }

        return results;
    }

    // 获取textExp并解析表达式
    const textExp = debugElement.getAttribute('textExp') || '';
    const expressions = splitExpressions(textExp);

    // 确定通用属性
    const commonAttributes: Record<string, string> = {};

    // 复制Debug标签的属性(除了x、y和textExp)
    Array.from(debugElement.attributes).forEach(attr => {
        if (![ 'x', 'y', 'textExp' ].includes(attr.name)) {
            commonAttributes[ attr.name ] = attr.value;
        }
    });

    // 设置或覆盖固定属性
    commonAttributes[ 'w' ] = `#view_width-${Number(x) + 10}`;
    commonAttributes[ 'marqueeSpeed' ] = '100';

    // 特权用户颜色设置
    if (!commonAttributes[ 'color' ]) {
        commonAttributes[ 'color' ] = isPrivilegedUser ? '#ff0000,#ff6903' : '#ff0000';
    }

    // 获取文字大小用于计算间距
    const size = commonAttributes[ 'size' ] || '30';

    // 为每个表达式创建对应的 Text 元素
    expressions.forEach((exp, index) => {
        const text = root.ownerDocument.createElement('Text');

        // 应用所有通用属性
        Object.entries(commonAttributes).forEach(([ name, value ]) => {
            text.setAttribute(name, value);
        });

        // 设置y值，使用文字大小*1.5作为间距
        text.setAttribute('y', String(index * Number(size) * 1.5));

        // 设置textExp，删除所有引号
        const expDescription = exp.replace(/['"]/g, '');
        text.setAttribute('textExp', `'${expDescription}  ='+${exp}`);

        group.appendChild(text);
    });

    // 修改前的XML
    // const serializer = new XMLSerializer();
    // const beforeXML = serializer.serializeToString(debugElement);

    // 替换原Debug标签
    debugElement.parentNode?.replaceChild(group, debugElement);

    // 修改后的XML
    // const afterXML = serializer.serializeToString(group);
}

/**
 * 处理带有.log后缀的属性，并为这些属性生成调试表达式
 * @param root - XML文档的根元素
 */
export function processLogAttributes(root: Element): void {
    // 首先检查文档中是否存在任何带有.log后缀的属性
    const allElements = root.getElementsByTagName('*');
    let hasLogAttribute = false;
    for (const element of Array.from(allElements)) {
        const attributes = Array.from(element.attributes);
        if (attributes.some(attr => attr.name.endsWith('.log'))) {
            hasLogAttribute = true;
            break;
        }
    }

    // 如果没有.log属性，无需进行处理，直接返回
    if (!hasLogAttribute) {
        return;
    }

    // 获取文档中的Debug标签（用于显示调试信息）
    let debugElement = root.getElementsByTagName('Debug')[ 0 ] as Element;

    // 如果存在.log属性但没有Debug标签，则创建一个新的Debug标签
    if (hasLogAttribute && !debugElement) {
        debugElement = root.ownerDocument.createElement('Debug');
        // 设置Debug标签的默认属性
        debugElement.setAttribute('x', '50');            // 水平位置
        debugElement.setAttribute('y', '50');            // 垂直位置
        // 特权用户颜色设置
        if (!debugElement.getAttribute('color')) {
            debugElement.setAttribute('color', isPrivilegedUser ? '#ff0000,#ff6903' : '#ff0000');
        }
        debugElement.setAttribute('size', '30');         // 文本大小
        debugElement.setAttribute('fontFamily', 'mipro-bold'); // 字体
        debugElement.setAttribute('textExp', '');        // 初始化为空的表达式列表
        root.appendChild(debugElement);                  // 将Debug标签添加到文档
    }

    // 用于收集所有需要添加到Debug标签的表达式
    const expressions = new Set<string>();

    // 定义正则表达式用于识别不同类型的表达式
    const operatorRegex = /[+\-*/%()<>=!&|]/;           // 识别包含运算符的表达式
    const functionNameRegex = /^[a-zA-Z]+\(/;           // 识别函数调用
    const variableRegex = /^[#@$]{1,2}[a-zA-Z0-9_]+$/;  // 识别变量引用
    const stringLiteralRegex = /^['"][^'"]*['"]$/;      // 识别字符串字面量

    /**
     * 检查字符串中的括号是否成对匹配
     * @param str - 需要检查的字符串
     * @returns 如果括号匹配则返回true，否则返回false
     */
    function isBalancedParentheses(str: string): boolean {
        let count = 0;
        for (const char of str) {
            if (char === '(') count++;
            if (char === ')') count--;
            if (count < 0) return false;  // 右括号过多
        }
        return count === 0;  // 括号数量应该完全匹配
    }

    /**
     * 处理表达式，确保正确格式化以用于调试输出
     * @param value - 原始表达式
     * @returns 处理后的表达式
     */
    function processExpression(value: string): string {
        // 处理函数调用（包括嵌套函数）
        if (functionNameRegex.test(value) && isBalancedParentheses(value)) {
            return value;  // 函数调用保持原样
        }

        // 处理包含+号的表达式（通常是字符串拼接）
        if (value.includes('+')) {
            const parts = value.split('+').map(part => part.trim());
            // 检查每个部分是否需要用num()函数包裹
            const processedParts = parts.map(part => {
                if (stringLiteralRegex.test(part)) {
                    return part;  // 字符串字面量保持不变
                } else if (variableRegex.test(part)) {
                    return part;  // 变量引用保持不变
                } else if (functionNameRegex.test(part) && isBalancedParentheses(part)) {
                    return part;  // 函数调用保持不变
                } else if (operatorRegex.test(part)) {
                    return `num(${part})`;  // 包含运算符的表达式用num()包裹以确保正确计算
                }
                return part;
            });
            return processedParts.join('+');
        }

        // 处理不包含+号的单一表达式
        if (variableRegex.test(value)) {
            return value;  // 纯变量引用，如#var、@str、$json等
        } else if (stringLiteralRegex.test(value)) {
            return value;  // 纯字符串字面量，如'text'
        } else if (operatorRegex.test(value)) {
            return `num(${value})`;  // 包含运算符的表达式用num()包裹
        }
        return value;  // 其他情况原样返回
    }

    // 遍历所有元素处理.log属性
    for (const element of Array.from(allElements)) {
        const attributes = Array.from(element.attributes);

        for (const attr of attributes) {
            if (attr.name.endsWith('.log')) {
                // 提取基本属性名（去掉.log后缀）
                const baseName = attr.name.replace('.log', '');
                let expToAdd = '';

                // 特殊处理name或return属性
                if (baseName === 'name' || baseName === 'return' || baseName === 'target') {
                    const name = attr.value;
                    const type = element.getAttribute('type');

                    // 根据元素的type属性确定正确的变量前缀
                    if (type) {
                        switch (type.toLowerCase()) {
                            case 'max':
                            case 'min':
                            case 'number':
                            case 'float':
                            case 'int':
                            case 'long':
                                expToAdd = `#${name}`;  // 数值类型使用#前缀
                                break;
                            case 'jsono':
                            case 'jsonO':
                                expToAdd = `$${name}`;  // JSON对象使用$前缀
                                break;
                            case 'jsona':
                            case 'jsonA':
                                expToAdd = `$$${name}`;  // JSON数组使用$$前缀
                                break;
                            case 'string':
                            case 'boolean':
                                expToAdd = `@${name}`;  // 字符串和布尔类型使用@前缀
                                break;
                            default:
                                expToAdd = `#${name}`;  // 默认使用#前缀
                                break;
                        }
                    } else {
                        expToAdd = `#${name}`;  // 如果没有type属性,默认使用#前缀
                    }

                    // 为Text和Image标签添加特殊属性
                    if (element.tagName === 'Text') {
                        expressions.add(`#${name}.text_width`);   // 文本宽度
                        expressions.add(`#${name}.text_height`);  // 文本高度
                    } else if (element.tagName === 'Image') {
                        expressions.add(`#${name}.bmp_width`);    // 图像宽度
                        expressions.add(`#${name}.bmp_height`);   // 图像高度
                    }
                }
                // 特殊处理countName属性
                else if (baseName === 'countName') {
                    // 为countName属性添加#前缀
                    expToAdd = `#${attr.value}`;
                }
                else {
                    // 处理其他类型的属性
                    const value = attr.value;
                    expToAdd = processExpression(value);
                }

                // 如果生成了有效的表达式，添加到表达式集合中
                if (expToAdd) {
                    expressions.add(expToAdd);
                }

                // 移除.log后缀，恢复原始属性
                element.removeAttribute(attr.name);
                element.setAttribute(baseName, attr.value);
            }
        }
    }

    // 更新Debug标签的textExp属性，合并已有表达式和新添加的表达式
    const existingExps = debugElement.getAttribute('textExp') || '';
    const allExps = new Set([
        ...existingExps.split(',').map(exp => exp.trim()),  // 已有的表达式
        ...expressions  // 新添加的表达式
    ].filter(exp => exp !== ''));  // 过滤掉空表达式

    // 将所有表达式以逗号分隔的形式设置回Debug标签
    debugElement.setAttribute('textExp', Array.from(allExps).join(','));
}