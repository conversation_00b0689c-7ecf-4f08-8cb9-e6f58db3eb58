// CirPathTextTransformer.ts
// 圆形路径文字转换器：将<CirPathText>标签转换为<Var>和<CanvasDrawer>标签
// import { XMLSerializer } from 'xmldom';
// import { format_xml } from './format_xml';
/**
 * 生成唯一的 name 属性
 * @param existingNames - 已存在的 name 集合
 * @param baseName - 基础名称
 * @returns 唯一的名称
 */
const generateUniqueName = (existingNames: Set<string>, baseName: string = 'generatedName'): string => {
    let uniqueName = baseName;
    let counter = 1;
    while (existingNames.has(uniqueName)) {
        uniqueName = `${baseName}_${counter}`;
        counter++;
    }
    existingNames.add(uniqueName);
    return uniqueName;
};

/**
 * 将颜色字符串转换为带有透明度的格式
 * 支持 "#ffffff", "#ffffffff", "argb(255,255,255,255)"
 * @param color - 原始颜色字符串
 * @returns 带有透明度的颜色字符串（如 "0xFFFFFFFF"）
 */
const formatColor = (color: string): string => {
    color = color.trim().toLowerCase();
    if (color.startsWith('#')) {
        if (color.length === 7) {
            // #RRGGBB
            return `0xFF${color.substring(1).toUpperCase()}`;
        } else if (color.length === 9) {
            // #AARRGGBB
            return `0x${color.substring(1).toUpperCase()}`;
        }
    } else if (color.startsWith('argb(') && color.endsWith(')')) {
        const parts = color.substring(5, color.length - 1).split(',').map(part => part.trim());
        if (parts.length === 4) {
            const [ a, r, g, b ] = parts;
            const alpha = parseInt(a).toString(16).padStart(2, '0').toUpperCase();
            const red = parseInt(r).toString(16).padStart(2, '0').toUpperCase();
            const green = parseInt(g).toString(16).padStart(2, '0').toUpperCase();
            const blue = parseInt(b).toString(16).padStart(2, '0').toUpperCase();
            return `0x${alpha}${red}${green}${blue}`;
        }
    }
    console.warn(`颜色格式不正确: ${color}. 使用默认颜色0xFF000000.`);
    return '0xFF000000'; // 默认黑色
};

/**
 * 解析 color 属性，返回带有透明度的颜色数组
 * 支持单一颜色和渐变色
 * @param colorAttr - color 属性值
 * @returns 带有透明度的颜色数组
 */
const parseColors = (colorAttr: string): string[] => {
    return colorAttr.split(',').map(color => formatColor(color.trim()));
};

/**
 * 解析 position 属性，返回位置数组
 * 支持单一位置和多个位置
 * @param positionAttr - position 属性值
 * @returns 位置数组
 */
const parsePositions = (positionAttr: string, colorCount: number): string[] => {
    const positions = positionAttr.split(',').map(pos => pos.trim());

    if (positions.length === colorCount) {
        return positions;
    }

    // 生成均匀分布的位置
    return Array.from({ length: colorCount }, (_, idx) => (idx / (colorCount - 1)).toFixed(2));
};

// 定义MethodCommand的接口
interface IMethodCommand {
    target?: string;
    targetType: string;
    className?: string;
    method?: string;
    returnName?: string;
    returnType?: string;
    paramTypes?: string;
    params?: string;
}

/**
 * 转换所有 <CirPathText> 标签，将其替换为 <Var> 和 <CanvasDrawer> 标签。
 * 具体需求如下：
 * 1. 遍历所有 CirPathText 标签，注意嵌套
 * 2. 生成 Var 标签，如果尚未存在 Var.name=CANVAS_SCALE
 * 3. 检查并生成唯一的 name 属性
 * 4. 转换 color 属性为 int 类型
 * 5. 根据 color 数量生成相应的 Var 标签
 * 6. 设置默认值 rotation、space、skew 为 0
 * 7. 替换 CirPathText 标签为指定结构
 * @param root - manifest.xml 的根 Element 节点
 */
export const cir_path_text = (root: Element): void => {
    // 1. 查找所有 <CirPathText> 元素（包括嵌套的）
    const cirPathTextElements = Array.from(root.getElementsByTagName('CirPathText')) as Element[];

    if (cirPathTextElements.length === 0) {
        return;
    }


    // 2. 检查是否已经存在 <Var name="CANVAS_SCALE">
    const existingCanvasScaleVar = Array.from(root.getElementsByTagName('Var')).find(varElem => varElem.getAttribute('name') === 'CANVAS_SCALE');

    if (!existingCanvasScaleVar) {
        // 创建 <Var name="CANVAS_SCALE" expression="int(#raw_screen_width/1.08)/1000" />
        const canvasScaleVar = root.ownerDocument.createElement('Var');
        canvasScaleVar.setAttribute('name', 'CANVAS_SCALE');
        canvasScaleVar.setAttribute('expression', 'int(#raw_screen_width/1.08)/1000');
        canvasScaleVar.setAttribute('type', 'number'); // 假设type为number

        // 插入到根节点的开始位置
        root.insertBefore(canvasScaleVar, root.firstChild);

    }

    // 3. 收集所有现有的name属性以确保生成唯一名称
    const existingNames = new Set<string>();
    Array.from(root.getElementsByTagName('*')).forEach(elem => {
        const nameAttr = elem.getAttribute('name');
        if (nameAttr) {
            existingNames.add(nameAttr);
        }
    });

    // 初始化序号计数器
    let sequenceNumber = 1;

    // 定义要排除的属性
    // const excludeAttrs = new Set([ 'color', 'size', 'textExp', 'radius', 'rotation', 'space', 'skew', 'x', 'y', 'position' ]);

    // 遍历每个 <CirPathText> 元素并进行转换
    cirPathTextElements.forEach(element => {
        // 3. 获取或生成 name 属性
        let name = element.getAttribute('name');
        if (!name) {
            name = generateUniqueName(existingNames, `generatedName_${sequenceNumber}`);
            element.setAttribute('name', name);
        }

        // 6. 获取或设置默认属性值 rotation、space、skew
        const rotation = element.hasAttribute('rotation') ? element.getAttribute('rotation')! : '0';
        const space = element.hasAttribute('space') ? element.getAttribute('space')! : '0';
        const skew = element.hasAttribute('skew') ? element.getAttribute('skew')! : '0';

        // 如果缺少这些属性，则设置默认值
        if (!element.hasAttribute('rotation')) {
            element.setAttribute('rotation', rotation);
        }
        if (!element.hasAttribute('space')) {
            element.setAttribute('space', space);
        }
        if (!element.hasAttribute('skew')) {
            element.setAttribute('skew', skew);
        }

        // 获取属性值
        const x = element.getAttribute('x') || '0';
        const y = element.getAttribute('y') || '0';
        const colorAttr = element.getAttribute('color') || '#000000';
        const size = element.getAttribute('size') || '40';
        const textExp = element.getAttribute('textExp') || "''"; // 默认空字符串
        const radius = element.getAttribute('radius') || '200';
        const positionAttr = element.getAttribute('position') || ''; // 位置属性

        // 4. 解析 color 属性，支持单一颜色和渐变色
        const colors = parseColors(colorAttr);
        const colorCount = colors.length;
        const isGradient = colorCount > 1;

        // 5. 根据 color 数量生成相应的 Var 标签
        let finalPositions: string[] = [];
        if (isGradient) {
            if (positionAttr) {
                finalPositions = parsePositions(positionAttr, colorCount);
            } else {
                finalPositions = Array.from({ length: colorCount }, (_, idx) => (idx / (colorCount - 1)).toFixed(2));
            }
        }

        // 创建 <Var name="${CirPathText.name}_radius" expression="${radius}*#CANVAS_SCALE" type="number" const="true" />
        const radiusVar = root.ownerDocument.createElement('Var');
        radiusVar.setAttribute('name', `${name}_radius`);
        radiusVar.setAttribute('expression', `${radius}*#CANVAS_SCALE`);
        radiusVar.setAttribute('type', 'number');
        radiusVar.setAttribute('const', 'true');

        // 如果是渐变色，创建 position 和 colors 变量
        let positionVar: Element | null = null;
        let colorsVar: Element | null = null;
        if (isGradient) {
            positionVar = root.ownerDocument.createElement('Var');
            positionVar.setAttribute('name', `${name}_position`);
            positionVar.setAttribute('type', 'float[]');
            positionVar.setAttribute('const', 'true');
            positionVar.setAttribute('values', finalPositions.join(','));

            colorsVar = root.ownerDocument.createElement('Var');
            colorsVar.setAttribute('name', `${name}_colors`);
            colorsVar.setAttribute('type', 'int[]');
            colorsVar.setAttribute('const', 'true');
            colorsVar.setAttribute('values', colors.join(','));
        }

        // 创建 <CanvasDrawer> 元素
        const canvasDrawer = root.ownerDocument.createElement('CanvasDrawer');
        canvasDrawer.setAttribute('name', 'canvas');
        canvasDrawer.setAttribute('x', x);
        canvasDrawer.setAttribute('y', y);

        // 创建 <Triggers> 元素
        const triggers = root.ownerDocument.createElement('Triggers');

        // 创建 init,resume trigger
        const initTrigger = root.ownerDocument.createElement('Trigger');
        initTrigger.setAttribute('action', 'init,resume');

        // 创建 draw trigger
        const drawTrigger = root.ownerDocument.createElement('Trigger');
        drawTrigger.setAttribute('action', 'draw');

        // 将 methodCommands 分成两组
        const initCommands: IMethodCommand[] = [
            { targetType: 'ctor', className: 'android.graphics.Paint', returnName: 'paint', returnType: 'object' },
            { targetType: 'ctor', className: 'android.graphics.Path', returnName: 'path', returnType: 'object' },
            { targetType: 'ctor', className: 'android.graphics.RectF', method: 'ctor', returnName: 'rectF', returnType: 'object', paramTypes: 'float,float,float,float', params: `0,0,#${name}_radius*2,#${name}_radius*2` },
            { targetType: 'var', className: 'com.miui.maml.util.ReflectionHelper', method: 'getEnumConstant', paramTypes: 'String,String', params: `'android.graphics.Path$Direction','CW'`, returnName: 'dire', returnType: 'object' },
            { target: 'path', targetType: 'var', method: 'addRoundRect', paramTypes: 'android.graphics.RectF,float,float,android.graphics.Path$Direction', params: `'rectF',#${name}_radius*2,#${name}_radius*2,'dire'` },
            { targetType: 'var', className: 'com.miui.maml.util.ReflectionHelper', method: 'getEnumConstant', paramTypes: 'String,String', params: `'android.graphics.Paint$Align','CENTER'`, returnName: 'align', returnType: 'object' },
            { target: 'paint', targetType: 'var', method: 'setTextAlign', paramTypes: 'android.graphics.Paint$Align', params: `'align'` },
            { target: 'paint', targetType: 'var', method: 'setLetterSpacing', paramTypes: 'float', params: `${space}+0.2` }
        ];

        // 添加颜色相关的命令到 initCommands
        if (isGradient) {
            initCommands.push(
                { targetType: 'var', className: 'com.miui.maml.util.ReflectionHelper', method: 'getEnumConstant', paramTypes: 'String,String', params: `'android.graphics.Shader$TileMode','CLAMP'`, returnName: 'tileMode', returnType: 'object' },
                { targetType: 'ctor', className: 'android.graphics.LinearGradient', method: 'ctor', returnName: 'linearGradient', returnType: 'object', paramTypes: 'float,float,float,float,int[],float[],android.graphics.Shader$TileMode', params: `0,0,#${name}_radius*2,#${name}_radius*2,'${name}_colors','${name}_position','tileMode'` },
                { target: 'paint', targetType: 'var', method: 'setShader', paramTypes: 'android.graphics.Shader', params: `'linearGradient'` }
            );
        } else {
            initCommands.push(
                { target: 'paint', targetType: 'var', method: 'setColor', paramTypes: 'int', params: `${colors[ 0 ]}` }
            );
        }

        initCommands.push(
            { target: 'paint', targetType: 'var', method: 'setTextSize', paramTypes: 'float', params: `${size}` },
            { target: 'paint', targetType: 'var', method: 'setTextSkewX', paramTypes: 'float', params: `${skew}` }
        );

        const drawCommands: IMethodCommand[] = [
            { target: '__objCanvas', targetType: 'var', method: 'rotate', paramTypes: 'float,float,float', params: `${rotation}, #${name}_radius, #${name}_radius` },
            { target: '__objCanvas', targetType: 'var', method: 'drawTextOnPath', paramTypes: 'String,android.graphics.Path,float,float,android.graphics.Paint', params: `${textExp},'path',0,${size},'paint'` }
        ];

        // 创建并附加 init/resume 的 MethodCommand 元素
        initCommands.forEach(cmd => {
            const methodCommand = root.ownerDocument.createElement('MethodCommand');
            if (cmd.target) {
                methodCommand.setAttribute('target', cmd.target);
            }
            if (cmd.targetType) {
                methodCommand.setAttribute('targetType', cmd.targetType);
            }
            if (cmd.className) {
                methodCommand.setAttribute('class', cmd.className);
            }
            if (cmd.method) {
                methodCommand.setAttribute('method', cmd.method);
            }
            if (cmd.returnName) {
                methodCommand.setAttribute('return', cmd.returnName);
            }
            if (cmd.returnType) {
                methodCommand.setAttribute('returnType', cmd.returnType);
            }
            if (cmd.paramTypes) {
                methodCommand.setAttribute('paramTypes', cmd.paramTypes);
            }
            if (cmd.params) {
                methodCommand.setAttribute('params', cmd.params);
            }
            initTrigger.appendChild(methodCommand);
        });

        // 创建并附加 draw 的 MethodCommand 元素
        drawCommands.forEach(cmd => {
            const methodCommand = root.ownerDocument.createElement('MethodCommand');
            if (cmd.target) {
                methodCommand.setAttribute('target', cmd.target);
            }
            if (cmd.targetType) {
                methodCommand.setAttribute('targetType', cmd.targetType);
            }
            if (cmd.className) {
                methodCommand.setAttribute('class', cmd.className);
            }
            if (cmd.method) {
                methodCommand.setAttribute('method', cmd.method);
            }
            if (cmd.returnName) {
                methodCommand.setAttribute('return', cmd.returnName);
            }
            if (cmd.returnType) {
                methodCommand.setAttribute('returnType', cmd.returnType);
            }
            if (cmd.paramTypes) {
                methodCommand.setAttribute('paramTypes', cmd.paramTypes);
            }
            if (cmd.params) {
                methodCommand.setAttribute('params', cmd.params);
            }
            drawTrigger.appendChild(methodCommand);
        });

        // 组装 <Triggers>
        triggers.appendChild(initTrigger);
        triggers.appendChild(drawTrigger);

        // 组装 <CanvasDrawer>
        canvasDrawer.appendChild(triggers);

        // 创建一个文档片段，将新元素插入到原标签的位置
        const fragment = root.ownerDocument.createDocumentFragment();
        fragment.appendChild(radiusVar);
        if (isGradient && colorsVar && positionVar) {
            fragment.appendChild(positionVar);
            fragment.appendChild(colorsVar);
        }
        fragment.appendChild(canvasDrawer);

        // 获取原标签的父节点
        const parent = element.parentNode;
        if (!parent) {
            console.warn(`元素 ${name} 没有父节点，无法替换。`);
            return;
        }

        // 修改前
        // const serializer = new XMLSerializer();
        // const beforeXML = serializer.serializeToString(element);
        // console.log(`\n\n%c修改前的<CirPathText> 片段：\n${format_xml(beforeXML)}`, 'color:#bf2c9f;');

        // 修改后的日志增加 Var 标签信息
        const varTags = [];
        varTags.push(`<Var name="${name}_radius" expression="${radius}*#CANVAS_SCALE" type="number" const="true" />`);
        if (isGradient) {
            varTags.push(`<Var name="${name}_position" type="float[]" const="true" values="${finalPositions.join(',')}" />`);
            varTags.push(`<Var name="${name}_colors" type="int[]" const="true" values="${colors.join(',')}" />`);
        }


        // const afterXML = serializer.serializeToString(canvasDrawer);
        // console.log(`%c修改后的 <CanvasDrawer> 标签：\n${format_xml(afterXML)}\n\n${format_xml(varTags.join('\n'))}`, 'color: #0055ff');
        // 替换原标签
        parent.replaceChild(fragment, element);

        // 增加序号计数器
        sequenceNumber++;
    });
};
