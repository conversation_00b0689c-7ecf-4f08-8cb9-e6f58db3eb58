/**
 * 将多个ExternalCommands标签合并为一个ExternalCommands标签
 * @param rootElement - 根元素
 */
export function merge_external_commands(rootElement: Element): void {
    try {
        // 获取所有ExternalCommands标签
        const externalCommandsList = rootElement.getElementsByTagName('ExternalCommands');

        // 如果没有或只有一个ExternalCommands标签，直接返回
        if (externalCommandsList.length <= 1) {
            return;
        }

        // 使用第一个ExternalCommands作为主标签
        const mainExternalCommands = externalCommandsList[ 0 ];

        // 从第二个开始遍历所有其他ExternalCommands标签
        for (let i = externalCommandsList.length - 1; i > 0; i--) {
            const currentCommands = externalCommandsList[ i ];

            // 将当前标签的所有子节点移动到主标签中
            while (currentCommands.firstChild) {
                mainExternalCommands.appendChild(currentCommands.firstChild);
            }

            // 移除当前的ExternalCommands标签
            currentCommands.parentNode?.removeChild(currentCommands);
        }
    } catch (error) {
        // 打印错误但不中断程序
        console.error('合并ExternalCommands标签时发生错误:', error);
    }
}
