// var_types.ts

// 根据需要添加更多类型
export enum VarType {
    STRING = 'string',
    STRINGARR = 'string[]',
    NUMBER = 'number',
    NUMBERARR = 'number[]',
    FLOAT = 'float',
    FLOATARR = 'float[]',
}

// 为新增类型定义前缀
export const varTypePrefixes: Record<VarType, string> = {
    [ VarType.STRING ]: '@',
    [ VarType.STRINGARR ]: '@',
    [ VarType.NUMBER ]: '#',
    [ VarType.NUMBERARR ]: '#',
    [ VarType.FLOAT ]: '#',
    [ VarType.FLOATARR ]: '#',
};
