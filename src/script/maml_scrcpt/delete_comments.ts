/**
* 异步版本的删除XML注释函数
* @param root - manifest.xml 的根 Element 节点
* @returns Promise，解析为被删除的注释节点数量
*/
export const delete_comments = async (root: Element): Promise<number> => {
    if (!root) return 0;

    let deletedCount = 0;
    const nodeStack: Node[] = [ root ];
    const BATCH_SIZE = 1000; // 每批处理的节点数

    while (nodeStack.length > 0) {
        // 每处理一批节点后让出主线程
        if (deletedCount % BATCH_SIZE === 0 && deletedCount > 0) {
            await new Promise(resolve => setTimeout(resolve, 0));
        }

        const currentNode = nodeStack.pop();

        // 添加空值检查
        if (!currentNode || !currentNode.childNodes) continue;

        const nodesToRemove: Node[] = [];
        const childrenToProcess: Node[] = [];

        for (let i = 0; i < currentNode.childNodes.length; i++) {
            const child = currentNode.childNodes[ i ];
            if (child.nodeType === 8) {
                nodesToRemove.push(child);
            } else if (child.hasChildNodes()) {
                childrenToProcess.push(child);
            }
        }

        for (const nodeToRemove of nodesToRemove) {
            currentNode.removeChild(nodeToRemove);
            deletedCount++;
        }

        nodeStack.push(...childrenToProcess);
    }

    return deletedCount;
};

