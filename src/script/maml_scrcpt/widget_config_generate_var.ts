// widget_config_generate_var.ts
// 小部件配置生成Var标签
import { readTextFile } from '@tauri-apps/plugin-fs';
import { DOMParser } from 'xmldom';

/**
 * 处理 var_config.xml 文件，生成并直接添加 <Var> 节点到 manifest.xml 的根元素下第一行的位置。
 * 同时打印出生成的 Var 信息。
 * @param varConfigPath - var_config.xml 文件路径
 * @param root - manifest.xml 的根 Element 节点
 */
export async function widget_config_generate_var(varConfigPath: string, rootElement: Element): Promise<void> {
    try {
        // 读取 var_config.xml
        const varConfigXml = await readTextFile(varConfigPath);

        const parser = new DOMParser();
        const varConfigDoc = parser.parseFromString(varConfigXml, 'application/xml');

        const varConfigRoot = varConfigDoc.documentElement;
        if (!varConfigRoot) {
            console.warn(`%c警告：var_config.xml 的根元素不存在`, 'color:#d9534f');
            return;
        }

        // 收集 manifest.xml 中已存在的 Var 名称
        const existingVarElements = Array.from(rootElement.getElementsByTagName('Var')) as Element[];
        const existingVarNames = new Set(existingVarElements.map(v => v.getAttribute('name') || ''));

        // 用于记录生成的 Var 信息
        const generatedVars: { name: string; expression: string; type: string }[] = [];

        /**
         * 添加 Var 元素到 manifest.xml 根元素的第一行位置
         * 同时记录并打印 Var 信息
         * @param name - Var 名称
         * @param expression - Var 表达式
         * @param type - Var 类型
         */
        const addVarIfNotExists = (name: string, expression: string, type: string) => {
            // 非空检查
            if (!name || typeof name !== 'string') {
                console.warn(`%c警告：addVarIfNotExists 被调用时缺少有效的 name 属性`, 'color:#d9534f');
                return;
            }

            // 检查是否已存在
            if (!existingVarNames.has(name)) {
                const varElement = rootElement.ownerDocument.createElement('Var');
                varElement.setAttribute('name', name);
                varElement.setAttribute('expression', expression);
                varElement.setAttribute('type', type);
                varElement.setAttribute('const', 'true'); // 将变量设为常量
                // 插入到 manifest.xml 的根元素下第一行的位置
                if (rootElement.firstChild) {
                    rootElement.insertBefore(varElement, rootElement.firstChild);
                } else {
                    rootElement.appendChild(varElement);
                }
                // 记录生成的 Var 信息
                generatedVars.push({ name, expression, type });
                // 添加到已存在的 Var 名称集合中，防止重复
                existingVarNames.add(name);
            }
        };

        /**
         * 处理特定标签的函数
         * @param node - 处理的 Element 节点
         * @param addVarIfNotExists - 添加 Var 的函数
         */
        const processTag = (
            node: Element,
            addVarIfNotExists: (name: string, expression: string, type: string) => void
        ) => {
            const tagName = node.tagName;

            switch (tagName) {
                case 'Text':
                    processTextNode(node, addVarIfNotExists);
                    break;
                case 'Color':
                    processColorNode(node, addVarIfNotExists);
                    break;
                case 'ImageSelect':
                    processImageSelectNode(node, addVarIfNotExists);
                    break;
                case 'ColorGroup':
                    processColorGroupNode(node, addVarIfNotExists);
                    break;
                case 'FontSize':
                    processFontSizeNode(node, addVarIfNotExists);
                    break;
                case 'Align':
                    processAlignNode(node, addVarIfNotExists);
                    break;
                case 'OnOff':
                    processOnOffNode(node, addVarIfNotExists);
                    break;
                case 'SetDate':
                    processSetDateNode(node, addVarIfNotExists);
                    break;
                default:
                    // 不处理其他标签，无需发出警告
                    break;
            }
        };

        /**
         * 处理顶层节点，仅处理相关标签，不深入子节点。
         * @param node - 当前处理的 Element 节点
         */
        const processNode = (node: Element) => {
            const relevantTags = [ 'FontSize', 'Text', 'Align', 'Color', 'ImageSelect', 'ColorGroup', 'SetDate', 'OnOff' ];

            if (relevantTags.includes(node.tagName)) {
                processTag(node, addVarIfNotExists);
            }
        };

        // 获取 var_config.xml 的根节点的所有直接子元素
        const children = Array.from(varConfigRoot.childNodes).filter(node => node.nodeType === 1) as Element[];

        for (const childElement of children) {
            const childTagName = childElement.tagName;
            const relevantTags = [ 'FontSize', 'Text', 'Align', 'Color', 'ImageSelect', 'ColorGroup', 'SetDate', 'OnOff' ];

            if (relevantTags.includes(childTagName)) {
                processNode(childElement);
            }
        }

        // 打印汇总信息
        // if (generatedVars.length > 0) {
        //     generatedVars.forEach(varInfo => {
        //         console.log(`%c添加默认声明：\n %c<Var name="${varInfo.name}" expression="${varInfo.expression}" type="${varInfo.type}" const="true" />`, 'color:#ff0000', 'color:#0055ff');
        //     });
        // } else {
        //     console.log('没有生成新的 Var 元素。');
        // }
    } catch (error) {
        console.error('处理 var_config.xml 时发生错误:', error);
    }
}

// 处理 Text 节点，提取名称和值，并添加到 Var
const processTextNode = (
    node: Element,
    addVarIfNotExists: (name: string, expression: string, type: string) => void
) => {
    const name = getNodeName(node);
    const value = getItemValue(node);
    if (value) {
        addVarIfNotExists(name, `'${value}'`, 'string');
    } else {
        console.warn(`%c警告：Text 节点 "${name}" 缺少有效的值`, 'color:#f0ad4e');
    }
};

// 处理 Color 节点，提取名称和值，并添加到 Var
const processColorNode = (
    node: Element,
    addVarIfNotExists: (name: string, expression: string, type: string) => void
) => {
    const name = getNodeName(node);
    const value = getItemValue(node);
    if (value) {
        addVarIfNotExists(name, `'${value}'`, 'string');
    } else {
        console.warn(`%c警告：Color 节点 "${name}" 缺少有效的值`, 'color:#f0ad4e');
    }
};

// 处理 ImageSelect 节点，提取名称和值，并添加到 Var
const processImageSelectNode = (
    node: Element,
    addVarIfNotExists: (name: string, expression: string, type: string) => void
) => {
    const name = getNodeName(node);
    const value = getItemValue(node);
    if (value) {
        addVarIfNotExists(name, `'${value}'`, 'string');
    } else {
        console.warn(`%c警告：ImageSelect 节点 "${name}" 缺少有效的值`, 'color:#f0ad4e');
    }
};

// 处理 ColorGroup 节点，提取名称和值，并添加到 Var
const processColorGroupNode = (
    node: Element,
    addVarIfNotExists: (name: string, expression: string, type: string) => void
) => {
    const name = getNodeName(node); // 获取 ColorGroup 的名称
    const vars = node.getElementsByTagName('Var'); // 获取 ColorGroup 下的所有 Var 子节点

    if (vars.length > 0) {
        for (let i = 0; i < vars.length; i++) {
            const varNode = vars[ i ];
            const varName = varNode.getAttribute('name') || '';
            const values = varNode.getAttribute('values') || '';
            const valueArray = values.split(',');

            if (valueArray.length > 0) {
                const expression = `'${valueArray[ 0 ]}'`; // 取第一个颜色值
                addVarIfNotExists(varName, expression, 'string');
            } else {
                console.warn(`%c警告：ColorGroup 节点 "${name}" 的 Var "${varName}" 缺少有效的值`, 'color:#f0ad4e');
            }
        }
    } else {
        console.warn(`%c警告：ColorGroup 节点 "${name}" 没有 Var 子节点`, 'color:#f0ad4e');
    }
};

// 处理 FontSize 节点，提取名称和值，并添加到 Var
const processFontSizeNode = (
    node: Element,
    addVarIfNotExists: (name: string, expression: string, type: string) => void
) => {
    const name = getNodeName(node);
    const defaultValue = getNodeDefaultValue(node);
    if (defaultValue) {
        addVarIfNotExists(name, defaultValue, 'number');
    } else {
        console.warn(`%c警告：FontSize 节点 "${name}" 缺少默认值`, 'color:#f0ad4e');
    }
};

// 处理 Align 节点，提取名称和值，并添加到 Var
const processAlignNode = (
    node: Element,
    addVarIfNotExists: (name: string, expression: string, type: string) => void
) => {
    const name = getNodeName(node);
    const defaultValue = getNodeDefaultValue(node);
    if (defaultValue) {
        addVarIfNotExists(name, defaultValue, 'number');
    } else {
        console.warn(`%c警告：Align 节点 "${name}" 缺少默认值`, 'color:#f0ad4e');
    }
};

// 处理 OnOff 节点，提取名称和值，并添加到 Var
const processOnOffNode = (
    node: Element,
    addVarIfNotExists: (name: string, expression: string, type: string) => void
) => {
    const name = getNodeName(node);
    const defaultValue = getNodeDefaultValue(node);
    if (defaultValue) {
        addVarIfNotExists(name, defaultValue, 'number');
    } else {
        console.warn(`%c警告：OnOff 节点 "${name}" 缺少默认值`, 'color:#f0ad4e');
    }
};

// 处理 SetDate 节点，提取名称和值，并添加到 Var
const processSetDateNode = (
    node: Element,
    addVarIfNotExists: (name: string, expression: string, type: string) => void
) => {
    const name = getNodeName(node);
    const defaultValue = getNodeDefaultValue(node);
    if (defaultValue) {
        // 将日期字符串转换为毫秒时间戳
        const date = new Date(defaultValue);
        if (!isNaN(date.getTime())) {
            const value = date.getTime().toString(); // 转换为毫秒时间戳
            addVarIfNotExists(name, value, 'number');
        } else {
            console.warn(`%c警告：SetDate 节点 "${name}" 的默认值 "${defaultValue}" 不是有效的日期`, 'color:#f0ad4e');
            addVarIfNotExists(name, defaultValue, 'number'); // 如果无法转换，保留原值
        }
    } else {
        console.warn(`%c警告：SetDate 节点 "${name}" 缺少默认值`, 'color:#f0ad4e');
    }
};

// 获取节点的 name 属性
const getNodeName = (node: Element): string => {
    return node.getAttribute('name') || '';
};

// 获取节点的 default 属性值
const getNodeDefaultValue = (node: Element): string | undefined => {
    return node.getAttribute('default') || undefined;
};

// 获取 item 类型节点中的值
const getItemValue = (node: Element): string | undefined => {
    const item = node.getElementsByTagName('item')[ 0 ];
    return item && item.textContent ? item.textContent.trim() : undefined;
};