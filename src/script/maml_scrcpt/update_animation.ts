// update_animation.ts
// 文字和日期时间有ani属性时添加动画
// import { XMLSerializer } from 'xmldom';
// import { format_xml } from './format_xml';

/**
 * 生成唯一的名称
 * @param existingNames - 已存在的名称集合
 * @returns 生成的唯一名称
 */
const generateUniqueName = (existingNames: Set<string>): string => {
    let uniqueName: string;
    do {
        uniqueName = `${Math.random().toString(36).substr(2, 9)}`;
    } while (existingNames.has(uniqueName));
    existingNames.add(uniqueName);
    return uniqueName;
};

/**
 * 检查FramerateController是否满足条件
 * @param controller - FramerateController元素
 * @returns 是否满足条件
 */
const isValidController = (controller: Element): boolean => {
    const controlPoints = Array.from(controller.getElementsByTagName('ControlPoint'));
    return controlPoints.some(point => {
        const frameRate = parseInt(point.getAttribute('frameRate') || '0', 10);
        const time = parseInt(point.getAttribute('time') || '0', 10);
        return frameRate >= 120 && time >= 1000;
    });
};

/**
 * 查找或创建合适的FramerateController
 * @param root - 根节点
 * @param existingNames - 已存在的名称集合
 * @returns FramerateController的名称
 */
const findOrCreateFramerateController = (root: Element, existingNames: Set<string>): string => {
    // 查找所有FramerateController标签
    const controllers = Array.from(root.getElementsByTagName('FramerateController'));

    // 检查是否有满足条件的FramerateController
    for (const controller of controllers) {
        if (isValidController(controller)) {
            return controller.getAttribute('name') || '';
        }
    }

    // 没有找到满足条件的，创建一个新的
    const controllerName = generateUniqueName(existingNames);
    const framerateController = root.ownerDocument.createElement('FramerateController');
    framerateController.setAttribute('name', controllerName);
    framerateController.setAttribute('initPause', 'true');
    framerateController.setAttribute('loop', 'false');

    const controlPoints = [
        { frameRate: '120', time: '0' },
        { frameRate: '120', time: '1000' },
        { frameRate: '0', time: '1001' }
    ];

    controlPoints.forEach(point => {
        const controlPoint = root.ownerDocument.createElement('ControlPoint');
        controlPoint.setAttribute('frameRate', point.frameRate);
        controlPoint.setAttribute('time', point.time);
        framerateController.appendChild(controlPoint);
    });

    root.appendChild(framerateController);
    return controllerName;
};

/**
 * 转换所有符合条件的 <Text> 和 <DateTime> 标签，将其替换为 <Var> 和新的标签。
 * 条件：
 *  - 必须有 ani 属性
 *  - 对于 <Text> 标签，必须有 textExp 属性
 *  - 对于 <DateTime> 标签，必须有 formatExp 属性
 * @param root - manifest.xml 的根 Element 节点
 */
export const update_animation = (root: Element): void => {
    // 查找所有 <Text> 和 <DateTime> 元素
    const textElements = Array.from(root.getElementsByTagName('Text')) as Element[];
    const dateTimeElements = Array.from(root.getElementsByTagName('DateTime')) as Element[];

    const allElements = [ ...textElements, ...dateTimeElements ];

    if (allElements.length === 0) {
        return;
    }

    // 收集所有现有的 name 属性值，以确保生成唯一名称
    const existingNames = new Set<string>();
    Array.from(root.getElementsByTagName('*')).forEach(elem => {
        const nameAttr = elem.getAttribute('name');
        if (nameAttr) {
            existingNames.add(nameAttr);
        }
    });

    // 检查是否需要创建共用的FramerateController
    const elementsWithAni = allElements.filter(element => element.getAttribute('ani'));
    if (elementsWithAni.length === 0) {
        return;
    }

    // 共用的FramerateController名称
    let sharedControllerName: string | null = null;

    // 遍历每个符合条件的元素并进行转换
    allElements.forEach((element) => {
        const tagName = element.tagName;
        let name = element.getAttribute('name');
        const ani = element.getAttribute('ani');

        // 检查 ani 属性
        if (!ani) {
            return;
        }

        // 检查是否存在对应的Function标签
        const hasFunction = Array.from(root.getElementsByTagName('Function')).some(
            func => func.getAttribute('name') === ani
        );

        // 如果没有Function，使用共用的FramerateController
        let controllerName = ani;
        if (!hasFunction) {
            // 如果还没有共用的controller，查找或创建一个
            if (!sharedControllerName) {
                sharedControllerName = findOrCreateFramerateController(root, existingNames);
            }
            controllerName = sharedControllerName;
        }

        // 如果没有 name 属性，生成一个唯一的 name
        if (!name) {
            name = generateUniqueName(existingNames);
            element.setAttribute('name', name);
        }

        let expressionValue: string | null = null;
        let expressionAttribute: string | null = null;

        if (tagName === 'Text') {
            expressionValue = element.getAttribute('textExp');
            expressionAttribute = 'textExp';
        } else if (tagName === 'DateTime') {
            expressionValue = element.getAttribute('formatExp');
            expressionAttribute = 'formatExp';
        }

        if (!expressionValue) {
            console.warn(`<${tagName}> 标签缺少 ${expressionAttribute} 属性，跳过转换。`);
            return;
        }

        // 创建新的字符串变量
        const newStringVar = root.ownerDocument.createElement('Var');
        const newStringVarName = `${name}_new_string`;
        newStringVar.setAttribute('name', newStringVarName);
        newStringVar.setAttribute('expression', tagName === 'DateTime' ? `formatDate(${expressionValue},#time_sys)` : expressionValue);
        newStringVar.setAttribute('type', 'string');

        // 创建旧的字符串变量（用于存储）
        const oldStringVar = root.ownerDocument.createElement('Var');
        const oldStringVarName = `${name}_old_string`;
        oldStringVar.setAttribute('name', oldStringVarName);
        oldStringVar.setAttribute('expression', `@${newStringVarName}`);
        oldStringVar.setAttribute('type', 'string');
        oldStringVar.setAttribute('const', 'true');

        // 创建动画控制变量
        const aniVar = root.ownerDocument.createElement('Var');
        const aniVarName = `${name}_ani`;
        aniVar.setAttribute('name', aniVarName);
        aniVar.setAttribute('type', 'number');

        // 创建出场动画
        const outAnimation = root.ownerDocument.createElement('VariableAnimation');
        outAnimation.setAttribute('loop', 'false');
        outAnimation.setAttribute('tag', 'out');
        outAnimation.setAttribute('initPause', 'true');

        const outItems = [
            { value: '0', time: '0', easeType: 'PhysicBased(0.85,0.714)' },
            { value: '1', time: '400' }
        ];

        outItems.forEach(item => {
            const itemElement = root.ownerDocument.createElement('AniFrame');
            itemElement.setAttribute('value', item.value);
            itemElement.setAttribute('time', item.time);
            if (item.easeType) {
                itemElement.setAttribute('easeType', item.easeType);
            }
            outAnimation.appendChild(itemElement);
        });

        // 添加出场动画的触发器
        const outTriggers = root.ownerDocument.createElement('Triggers');
        const outTrigger = root.ownerDocument.createElement('Trigger');
        outTrigger.setAttribute('action', 'end');
        outTrigger.setAttribute('condition', `#${aniVarName} == 1`);

        const varCommand = root.ownerDocument.createElement('VariableCommand');
        varCommand.setAttribute('name', oldStringVarName);
        varCommand.setAttribute('expression', `@${newStringVarName}`);
        varCommand.setAttribute('type', 'string');

        const aniCommand = root.ownerDocument.createElement('AnimationCommand');
        aniCommand.setAttribute('target', aniVarName);
        aniCommand.setAttribute('command', 'play');
        aniCommand.setAttribute('tags', 'in');

        outTrigger.appendChild(varCommand);
        outTrigger.appendChild(aniCommand);
        outTriggers.appendChild(outTrigger);
        outAnimation.appendChild(outTriggers);

        // 创建进场动画
        const inAnimation = root.ownerDocument.createElement('VariableAnimation');
        inAnimation.setAttribute('loop', 'false');
        inAnimation.setAttribute('tag', 'in');
        inAnimation.setAttribute('initPause', 'true');

        const inItems = [
            { value: '1', time: '0', easeType: 'PhysicBased(0.85,0.714)' },
            { value: '0', time: '400' }
        ];

        inItems.forEach(item => {
            const itemElement = root.ownerDocument.createElement('AniFrame');
            itemElement.setAttribute('value', item.value);
            itemElement.setAttribute('time', item.time);
            if (item.easeType) {
                itemElement.setAttribute('easeType', item.easeType);
            }
            inAnimation.appendChild(itemElement);
        });

        // 将动画添加到动画控制变量中
        aniVar.appendChild(outAnimation);
        aniVar.appendChild(inAnimation);

        // 创建新的标签
        const newElement = root.ownerDocument.createElement(tagName);
        newElement.setAttribute('name', name);

        if (tagName === 'Text') {
            newElement.setAttribute('textExp', `@${oldStringVarName}`);
        } else if (tagName === 'DateTime') {
            newElement.setAttribute('formatExp', `@${oldStringVarName}`);
        }

        // 继承其他属性
        for (let i = 0; i < element.attributes.length; i++) {
            const attr = element.attributes[ i ];
            const attrName = attr.name;
            const attrValue = attr.value;

            if (
                attrName !== 'name' &&
                attrName !== 'ani' &&
                !((tagName === 'Text' && attrName === 'textExp') ||
                    (tagName === 'DateTime' && attrName === 'formatExp'))
            ) {
                newElement.setAttribute(attrName, attrValue);
            }
        }

        // 设置 pivotX 和 pivotY
        if (!newElement.hasAttribute('pivotX')) {
            newElement.setAttribute('pivotX', '40');
        }
        if (!newElement.hasAttribute('pivotY')) {
            newElement.setAttribute('pivotY', `#${name}.text_height/3`);
        }

        // 设置动画相关的属性
        newElement.setAttribute('alpha', `255*(1-#${aniVarName})`);
        newElement.setAttribute('scale', `1-#${aniVarName}*0.3`);

        // 创建触发器变量
        const triggerVar = root.ownerDocument.createElement('Var');
        triggerVar.setAttribute('name', `${name}_trigger`);
        triggerVar.setAttribute('expression', `@${newStringVarName}`);
        triggerVar.setAttribute('type', 'string');

        const trigger = root.ownerDocument.createElement('Trigger');

        // 添加帧率控制器命令
        if (!hasFunction) {
            const rateCommand = root.ownerDocument.createElement('AnimationCommand');
            rateCommand.setAttribute('target', controllerName);
            rateCommand.setAttribute('command', 'play');
            trigger.appendChild(rateCommand);
        }

        const triggerAniCommand = root.ownerDocument.createElement('AnimationCommand');
        triggerAniCommand.setAttribute('target', aniVarName);
        triggerAniCommand.setAttribute('command', 'play');
        triggerAniCommand.setAttribute('tags', 'out');
        triggerAniCommand.setAttribute('condition', `#${aniVarName} == 0`);
        trigger.appendChild(triggerAniCommand);
        triggerVar.appendChild(trigger);

        // 创建一个文档片段，将新元素插入到原标签的位置
        const fragment = root.ownerDocument.createDocumentFragment();
        fragment.appendChild(newStringVar);
        fragment.appendChild(oldStringVar);
        fragment.appendChild(aniVar);
        fragment.appendChild(triggerVar);
        fragment.appendChild(newElement);

        // 获取原标签的父节点
        const parent = element.parentNode;
        if (!parent) {
            console.warn(`<${tagName}> 标签没有父节点，无法替换。`);
            return;
        }

        // 替换原标签
        parent.replaceChild(fragment, element);
    });
};

