import * as fs from '@tauri-apps/plugin-fs';
import * as path from '@tauri-apps/api/path';
import * as opentype from 'opentype.js';



/**
 * 将SVG路径数据转换为字形轮廓
 * @param svgContent SVG内容
 * @returns 字形轮廓数据
 */
function svgToPath(svgContent: string): opentype.Path {
    const parser = new DOMParser();
    const doc = parser.parseFromString(svgContent, 'image/svg+xml');
    const paths = doc.getElementsByTagName('path');

    // 计算边界框
    let minX = Infinity, minY = Infinity;
    let maxX = -Infinity, maxY = -Infinity;

    // 遍历所有路径以找到实际的边界框
    Array.from(paths).forEach(pathElement => {
        const d = pathElement.getAttribute('d');
        if (!d) return;

        const commands = d.match(/([a-zA-Z])([^a-zA-Z]*)/g) || [];
        let currentX = 0, currentY = 0;

        commands.forEach(cmd => {
            const type = cmd[ 0 ];
            const args = cmd.slice(1).trim().split(/[\s,]+/).map(Number);
            const isRelative = type >= 'a' && type <= 'z';

            switch (type.toUpperCase()) {
                case 'M':
                case 'L':
                    if (isRelative) {
                        currentX += args[ 0 ];
                        currentY += args[ 1 ];
                    } else {
                        currentX = args[ 0 ];
                        currentY = args[ 1 ];
                    }
                    minX = Math.min(minX, currentX);
                    minY = Math.min(minY, currentY);
                    maxX = Math.max(maxX, currentX);
                    maxY = Math.max(maxY, currentY);
                    break;

                case 'H':
                    if (isRelative) {
                        currentX += args[ 0 ];
                    } else {
                        currentX = args[ 0 ];
                    }
                    minX = Math.min(minX, currentX);
                    maxX = Math.max(maxX, currentX);
                    break;

                case 'V':
                    if (isRelative) {
                        currentY += args[ 0 ];
                    } else {
                        currentY = args[ 0 ];
                    }
                    minY = Math.min(minY, currentY);
                    maxY = Math.max(maxY, currentY);
                    break;

                case 'C':
                    if (isRelative) {
                        minX = Math.min(minX, currentX + args[ 0 ], currentX + args[ 2 ], currentX + args[ 4 ]);
                        minY = Math.min(minY, currentY + args[ 1 ], currentY + args[ 3 ], currentY + args[ 5 ]);
                        maxX = Math.max(maxX, currentX + args[ 0 ], currentX + args[ 2 ], currentX + args[ 4 ]);
                        maxY = Math.max(maxY, currentY + args[ 1 ], currentY + args[ 3 ], currentY + args[ 5 ]);
                        currentX += args[ 4 ];
                        currentY += args[ 5 ];
                    } else {
                        minX = Math.min(minX, args[ 0 ], args[ 2 ], args[ 4 ]);
                        minY = Math.min(minY, args[ 1 ], args[ 3 ], args[ 5 ]);
                        maxX = Math.max(maxX, args[ 0 ], args[ 2 ], args[ 4 ]);
                        maxY = Math.max(maxY, args[ 1 ], args[ 3 ], args[ 5 ]);
                        currentX = args[ 4 ];
                        currentY = args[ 5 ];
                    }
                    break;

                case 'Q':
                    if (isRelative) {
                        minX = Math.min(minX, currentX + args[ 0 ], currentX + args[ 2 ]);
                        minY = Math.min(minY, currentY + args[ 1 ], currentY + args[ 3 ]);
                        maxX = Math.max(maxX, currentX + args[ 0 ], currentX + args[ 2 ]);
                        maxY = Math.max(maxY, currentY + args[ 1 ], currentY + args[ 3 ]);
                        currentX += args[ 2 ];
                        currentY += args[ 3 ];
                    } else {
                        minX = Math.min(minX, args[ 0 ], args[ 2 ]);
                        minY = Math.min(minY, args[ 1 ], args[ 3 ]);
                        maxX = Math.max(maxX, args[ 0 ], args[ 2 ]);
                        maxY = Math.max(maxY, args[ 1 ], args[ 3 ]);
                        currentX = args[ 2 ];
                        currentY = args[ 3 ];
                    }
                    break;
            }
        });
    });

    // 计算实际尺寸
    const width = maxX - minX;
    const height = maxY - minY;

    // 字体的安全区域（考虑边距）
    const topMargin = 128; // 顶部边距
    const bottomMargin = 128; // 底部边距
    const availableHeight = 896 - (-128) - (topMargin + bottomMargin); // 实际可用高度
    const availableWidth = 1024; // 完整宽度可用

    // 计算缩放比例，保持宽高比，并确保不超过可用空间
    const scale = Math.min(
        availableWidth / width,
        availableHeight / height
    ) * 1;

    // 计算水平居中的偏移量
    const offsetX = (availableWidth - width * scale) / 2 - minX * scale;

    // 计算垂直方向的偏移量，确保在上下边距内居中
    const scaledHeight = height * scale;
    const centerY = 896 - topMargin - (availableHeight - scaledHeight) / 2;
    const offsetY = centerY + minY * scale;

    // 创建新的路径
    const glyphPath = new opentype.Path();

    // 处理每个path元素
    Array.from(paths).forEach(pathElement => {
        const d = pathElement.getAttribute('d');
        if (!d) return;

        // 解析SVG路径命令
        const commands = d.match(/([a-zA-Z])([^a-zA-Z]*)/g) || [];
        let currentX = 0, currentY = 0;
        let startX = 0, startY = 0;

        commands.forEach(cmd => {
            const type = cmd[ 0 ];
            const args = cmd.slice(1).trim().split(/[\s,]+/).map(Number);
            const isRelative = type >= 'a' && type <= 'z';

            switch (type.toUpperCase()) {
                case 'M':
                    if (isRelative) {
                        currentX += args[ 0 ];
                        currentY += args[ 1 ];
                    } else {
                        currentX = args[ 0 ];
                        currentY = args[ 1 ];
                    }
                    startX = currentX;
                    startY = currentY;
                    glyphPath.moveTo(
                        currentX * scale + offsetX,
                        offsetY - currentY * scale
                    );
                    break;

                case 'L':
                    if (isRelative) {
                        currentX += args[ 0 ];
                        currentY += args[ 1 ];
                    } else {
                        currentX = args[ 0 ];
                        currentY = args[ 1 ];
                    }
                    glyphPath.lineTo(
                        currentX * scale + offsetX,
                        offsetY - currentY * scale
                    );
                    break;

                case 'H':
                    if (isRelative) {
                        currentX += args[ 0 ];
                    } else {
                        currentX = args[ 0 ];
                    }
                    glyphPath.lineTo(
                        currentX * scale + offsetX,
                        offsetY - currentY * scale
                    );
                    break;

                case 'V':
                    if (isRelative) {
                        currentY += args[ 0 ];
                    } else {
                        currentY = args[ 0 ];
                    }
                    glyphPath.lineTo(
                        currentX * scale + offsetX,
                        offsetY - currentY * scale
                    );
                    break;

                case 'C':
                    if (isRelative) {
                        glyphPath.curveTo(
                            (currentX + args[ 0 ]) * scale + offsetX,
                            offsetY - (currentY + args[ 1 ]) * scale,
                            (currentX + args[ 2 ]) * scale + offsetX,
                            offsetY - (currentY + args[ 3 ]) * scale,
                            (currentX + args[ 4 ]) * scale + offsetX,
                            offsetY - (currentY + args[ 5 ]) * scale
                        );
                        currentX += args[ 4 ];
                        currentY += args[ 5 ];
                    } else {
                        glyphPath.curveTo(
                            args[ 0 ] * scale + offsetX,
                            offsetY - args[ 1 ] * scale,
                            args[ 2 ] * scale + offsetX,
                            offsetY - args[ 3 ] * scale,
                            args[ 4 ] * scale + offsetX,
                            offsetY - args[ 5 ] * scale
                        );
                        currentX = args[ 4 ];
                        currentY = args[ 5 ];
                    }
                    break;

                case 'Q':
                    if (isRelative) {
                        glyphPath.quadTo(
                            (currentX + args[ 0 ]) * scale + offsetX,
                            offsetY - (currentY + args[ 1 ]) * scale,
                            (currentX + args[ 2 ]) * scale + offsetX,
                            offsetY - (currentY + args[ 3 ]) * scale
                        );
                        currentX += args[ 2 ];
                        currentY += args[ 3 ];
                    } else {
                        glyphPath.quadTo(
                            args[ 0 ] * scale + offsetX,
                            offsetY - args[ 1 ] * scale,
                            args[ 2 ] * scale + offsetX,
                            offsetY - args[ 3 ] * scale
                        );
                        currentX = args[ 2 ];
                        currentY = args[ 3 ];
                    }
                    break;

                case 'Z':
                    currentX = startX;
                    currentY = startY;
                    glyphPath.close();
                    break;
            }
        });
    });

    return glyphPath;
}

/**
 * 将SVG文件转换为TTF字体文件并修改XML中的SvgText标签
 * @param rootElement XML文档的根元素
 * @param etcPath 字体文件保存路径
 */
export async function svg_to_ttf(rootElement: Element, etcPath: string): Promise<void> {
    try {
        // 获取所有SvgText标签
        const svgTextElements = rootElement.getElementsByTagName('SvgText');
        const elements = Array.from(svgTextElements);

        // 检查是否存在SvgText标签
        if (elements.length === 0) {
            return;
        }

        // 创建新的字体
        const notdefGlyph = new opentype.Glyph({
            name: '.notdef',
            unicode: 0,
            advanceWidth: 650,
            path: new opentype.Path()
        });

        const glyphs = [ notdefGlyph ];
        let unicodeStart = 0xE001; // 从E001开始，避开保留区域

        // 处理每个SVG文件
        for (const element of elements) {
            try {
                // 获取svg文件路径
                const svgPath = element.getAttribute('textExp') ||
                    element.getAttribute('text') || '';

                if (!svgPath) {
                    console.error('未找到svg文件路径');
                    continue;
                }

                // 处理路径（去掉引号）
                const cleanPath = svgPath.replace(/['"]/g, '');
                const fileName = await path.basename(cleanPath, '.svg');
                const fullSvgPath = await path.join(etcPath, cleanPath);

                // 检查文件是否存在
                if (!await fs.exists(fullSvgPath)) {
                    console.error(`文件 ${fullSvgPath} 不存在`);
                    continue;
                }

                // 读取SVG文件
                const svgContent = await fs.readTextFile(fullSvgPath);

                // 转换SVG路径数据
                const glyphPath = svgToPath(svgContent);

                // 创建字形
                const glyph = new opentype.Glyph({
                    name: fileName,
                    unicode: unicodeStart,
                    advanceWidth: 1024,
                    path: glyphPath
                });

                glyphs.push(glyph);

                // 更新XML标签
                const textElement = rootElement.ownerDocument!.createElement('Text');

                // 复制所有属性
                for (const attr of Array.from(element.attributes)) {
                    if (attr.name !== 'textExp' && attr.name !== 'text') {
                        textElement.setAttribute(attr.name, attr.value);
                    }
                }

                // 设置新属性
                const unicode = String.fromCharCode(unicodeStart);
                if (element.hasAttribute('textExp')) {
                    textElement.setAttribute('textExp', `'${unicode}'`);
                } else if (element.hasAttribute('text')) {
                    textElement.setAttribute('text', unicode);
                }
                textElement.setAttribute('fontPath', 'etc/custom-icons.ttf');

                // 替换原始元素
                element.parentNode?.replaceChild(textElement, element);

                // console.log(`SVG文件 ${fullSvgPath} 已成功转换为字体`);

                unicodeStart++;
            } catch (error) {
                console.error('处理单个SVG文件时出错:', error);
                continue;
            }
        }

        // 创建字体
        const font = new opentype.Font({
            familyName: 'CustomIcons',
            styleName: 'Regular',
            unitsPerEm: 1024,
            ascender: 896,
            descender: -128,
            glyphs: glyphs
        });

        // 生成TTF数据
        const ttfBuffer = new Uint8Array(font.toArrayBuffer());

        // 保存TTF文件
        const ttfPath = await path.join(etcPath, 'custom-icons.ttf');
        await fs.writeFile(ttfPath, ttfBuffer);

    } catch (error) {
        console.error('SVG转TTF过程中出错:', error);
        throw error;
    }
}
