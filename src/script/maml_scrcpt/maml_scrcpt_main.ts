import { join, dirname } from '@tauri-apps/api/path';
import { readTextFile, writeTextFile } from '@tauri-apps/plugin-fs';
import { DOMParser, XMLSerializer } from 'xmldom';
import { expand_expression } from './expand_expression';
import { widget_config_generate_var } from './widget_config_generate_var';
import { manifest_generate_var } from './manifest_generate_var';
import { var_commands } from './var_commands';
import { cpb_default_columns } from './cpb_default_columns';
import { cpb_binder_command } from './cpb_binder_command';
import { update_animation } from './update_animation';
import { cir_path_text } from './cir_path_text';
import { gradient_style } from './gradient_style';
import { command_move_action } from './command_move_action';
// import { unlock_config_generate_var } from './unlock_config_generate_var';
import { delete_comments } from './delete_comments';
import { debug } from './debug';
import { delete_empty_properties } from './delete_empty_properties';
import { avg } from './avg';
import { svg_to_ttf } from './svg_to_ttf';
import { scale_group } from './scale_group';
import { merge_external_commands } from './merge_external_commands';
import { merge_variable_binders } from './merge_variable_binders';
import { linear_progress_bar } from './linear_progress_bar';
import { delete_unused_variables } from './delete_unused_variables';
import { arc_progress_bar } from './arc_progress_bar';
// 定义步骤类型，添加优先级属性
type Step = {
    name: string;
    fn: () => Promise<void>;
    priority: number; // 优先级，数字越小优先级越高
};

// 类型保护函数，用于过滤掉 false 值
function isStep(step: Step | false): step is Step {
    return step !== false;
}

// 复用 DOMParser 和 XMLSerializer 实例
const parser = new DOMParser({
    // 配置 DOMParser 以保留实体字符
    locator: {},
    errorHandler: {
        warning: () => { },
        error: () => { },
        fatalError: () => { }
    }
});
const serializer = new XMLSerializer();

// 定义实体映射
const entityMap = {
    '&#10;': '__ENTITY_10__',
    '&#xA;': '__ENTITY_XA__',
    '&#13;': '__ENTITY_13__',
    '&#xD;': '__ENTITY_XD__',
    '&#34;': '__ENTITY_34__',
    '&#x22;': '__ENTITY_X22__',
    '&#38;': '__ENTITY_38__',
    '&#x26;': '__ENTITY_X26__',
    '&#39;': '__ENTITY_39__',
    '&#x27;': '__ENTITY_X27__',
    '&#60;': '__ENTITY_60__',
    '&#x3C;': '__ENTITY_X3C__',
    '&#62;': '__ENTITY_62__',
    '&#x3E;': '__ENTITY_X3E__',
    '&quot;': '__ENTITY_QUOT__',
    '&amp;': '__ENTITY_AMP__',
    '&apos;': '__ENTITY_APOS__',
    '&lt;': '__ENTITY_LT__',
    '&gt;': '__ENTITY_GT__'

};

// 将函数移到外部
async function executeStepsByPriority(steps: Array<Step | false>, errors: string[]): Promise<void> {
    // 过滤掉 false 值并按优先级排序
    const validSteps = steps.filter(isStep).sort((a, b) => a.priority - b.priority);

    // 按优先级分组
    const priorityGroups = validSteps.reduce((groups, step) => {
        const priority = step.priority;
        if (!groups[ priority ]) {
            groups[ priority ] = [];
        }
        groups[ priority ].push(step);
        return groups;
    }, {} as Record<number, Step[]>);

    // 按优先级顺序执行每组步骤
    for (const priority of Object.keys(priorityGroups).map(Number).sort((a, b) => a - b)) {
        const stepsInGroup = priorityGroups[ priority ];
        // console.log(`执行优先级 ${priority} 的步骤组...`);

        const results = await Promise.allSettled(stepsInGroup.map(async step => {
            try {
                // console.log(`开始执行: ${step.name}`);
                await step.fn();
                // console.log(`完成执行: ${step.name}`);
                return { name: step.name, success: true };
            } catch (error) {
                const errorMessage = `执行 ${step.name} 时发生错误: ${error instanceof Error ? error.message : '未知错误'}`;
                console.error(errorMessage);
                errors.push(errorMessage);
                return { name: step.name, success: false };
            }
        }));

        // 只记录失败的执行结果
        results.forEach(result => {
            if (result.status === 'fulfilled' && !result.value.success) {
                console.error(`步骤 ${result.value.name} 执行失败`);
            } else if (result.status === 'rejected') {
                console.error(`步骤执行rejected: ${result.reason}`);
                errors.push(`未预期的错误: ${result.reason}`);
            }
        });
    }
}

// 处理 manifest 文件
async function maml_scrcpt_main(
    manifestFilePath: string,
    useVarConfig: boolean,
    _useUnlockConfig: boolean,
    username: string = '' // 添加用户名参数，默认为空字符串
): Promise<{ success: boolean; errors: string[] }> {
    const errors: string[] = [];
    // 指定用户可用
    const isPrivilegedUser = username === 'zhangchuanqiang';

    try {
        // 读取并替换实体
        let manifestXml = await readTextFile(manifestFilePath);

        // 将实体替换为临时标记
        Object.entries(entityMap).forEach(([ entity, placeholder ]) => {
            manifestXml = manifestXml.replace(new RegExp(entity, 'g'), placeholder);
        });

        // 解析 XML 为 DOM
        const manifestDoc = parser.parseFromString(manifestXml, 'application/xml');

        // 并行获取manifest父目录名
        const manifestDir = await dirname(manifestFilePath);

        // 并行获取项目根目录名
        const projectRootDir = await dirname(manifestDir);

        // 定义 var_config.xml 和 config.xml 的路径
        const varConfigPath = await join(manifestDir, 'var_config.xml');
        // const lockConfigPath = await join(manifestDir, 'config.xml');
        const etcPath = await join(projectRootDir, 'etc');

        // console.log('manifestDir************************************', manifestDir);
        // console.log('varConfigPath************************************', varConfigPath);
        // console.log('lockConfigPath************************************', lockConfigPath);
        // console.log('etcPath************************************', etcPath);

        // 获取根节点
        const rootElement = manifestDoc.documentElement;

        if (!rootElement) {
            console.error("无法获取根节点，终止处理。");
            return { success: false, errors };
        }

        // 定义转换步骤，添加优先级
        const steps: Array<Step | false> = [

            isPrivilegedUser && {
                name: 'delete_comments',
                priority: 1,
                fn: async () => {
                    // 删除XML文档中的所有注释节点
                    delete_comments(rootElement);
                }
            },
            {
                name: 'delete_empty_properties',
                priority: 1,
                fn: async () => {
                    // 删除XML文档中的空节点和空属性
                    await delete_empty_properties(rootElement);
                }
            },
            // useUnlockConfig && {
            //     name: 'unlock_config_generate_var',
            //     priority: 1,
            //     fn: async () => {
            //         // 处理解锁配置文件，生成相关变量
            //         unlock_config_generate_var(lockConfigPath, rootElement);
            //     }
            // },
            {
                name: 'manifest_generate_var',
                priority: 1,
                fn: async () => {
                    // 从manifest文件生成全局变量
                    manifest_generate_var(rootElement);
                }
            },
            useVarConfig && {
                name: 'widget_config_generate_var',
                priority: 1,
                fn: async () => {
                    // 从var_config.xml生成组件变量配置
                    widget_config_generate_var(varConfigPath, rootElement);
                }
            },

            {
                name: 'command_move_action',
                priority: 1,
                fn: async () => {
                    // 指令移动到指定动作中
                    command_move_action(rootElement);
                }
            },
            {
                name: 'scale_group',
                priority: 1,
                fn: async () => {
                    // 将scale_group标签转换为Group标签
                    scale_group(rootElement);
                }
            },
            {
                name: 'merge_external_commands',
                priority: 1,
                fn: async () => {
                    // 将多个 ExternalCommands 标签合并为一个 ExternalCommands 标签
                    merge_external_commands(rootElement);
                }
            },
            isPrivilegedUser && {
                name: 'merge_variable_binders',
                priority: 1,
                fn: async () => {
                    // 将多个 VariableBinders 标签合并为一个 VariableBinders 标签
                    merge_variable_binders(rootElement);
                }
            },
            isPrivilegedUser && {
                name: 'linear_progress_bar',
                priority: 2,
                fn: async () => {
                    // 将LinearProgressBar标签转换为线性进度条
                    linear_progress_bar(rootElement);
                }
            },
            isPrivilegedUser && {
                name: 'arc_progress_bar',
                priority: 2,
                fn: async () => {
                    // 将ArcProgressBar标签转换为弧形进度条
                    arc_progress_bar(rootElement);
                }
            },
            {
                name: 'expand_expression',
                priority: 2,
                fn: async () => {
                    // 处理扩展属性，展开特定的XML属性
                    expand_expression(rootElement);
                }
            },
            {
                name: 'debug',
                priority: 3,
                fn: async () => {
                    // 添加调试相关的信息和处理
                    debug(rootElement);
                }
            },
            {
                name: 'var_commands',
                priority: 4,
                fn: async () => {
                    // 转换变量相关的命令
                    var_commands(rootElement);
                }
            },
            {
                name: 'svg_to_ttf',
                priority: 5,
                fn: async () => {
                    // 将SVG图标转换为TTF字体文件
                    await svg_to_ttf(rootElement, etcPath);
                }
            },
            isPrivilegedUser && {
                name: 'cir_path_text',
                priority: 5,
                fn: async () => {
                    // 转换圆形路径上的文本
                    await cir_path_text(rootElement);
                }
            },
            isPrivilegedUser && {
                name: 'avg',
                priority: 5,
                fn: async () => {
                    // 平均分布组
                    avg(rootElement);
                }
            },
            isPrivilegedUser && {
                name: 'update_animation',
                priority: 6,
                fn: async () => {
                    // 为文本和日期时间添加动画效果
                    await update_animation(rootElement);
                }
            },
            isPrivilegedUser && {
                name: 'gradient_style',
                priority: 6,
                fn: async () => {
                    // 转换渐变文本和日期时间显示
                    await gradient_style(rootElement);
                }
            },
            {
                name: 'cpb_default_columns',
                priority: 6,
                fn: async () => {
                    // 转换内容提供者的绑定关系
                    cpb_default_columns(rootElement);
                }
            },
            {
                name: 'cpb_binder_command',
                priority: 6,
                fn: async () => {
                    //ContentProviderBinder接口初始化和曝光时更新
                    cpb_binder_command(rootElement);
                }
            },
            {
                name: 'delete_unused_variables',
                priority: 7,
                fn: async () => {
                    // 删除未使用变量
                    delete_unused_variables(rootElement);
                }
            },
        ];

        try {
            // 执行所有步骤
            await executeStepsByPriority(steps, errors);

            // 序列化和保存操作
            let updatedXml = serializer.serializeToString(manifestDoc);
            updatedXml = updatedXml.replace(/>\s+</g, '><');

            Object.entries(entityMap).forEach(([ entity, placeholder ]) => {
                updatedXml = updatedXml.replace(new RegExp(placeholder, 'g'), entity);
            });

            await writeTextFile(manifestFilePath, updatedXml);
        } catch (error) {
            const errorMessage = `保存文件时发生错误: ${error instanceof Error ? error.message : '未知错误'}`;
            console.error(errorMessage);
            errors.push(errorMessage);
        }

        return { success: errors.length === 0, errors };

    } catch (error) {
        const errorMessage = `处理 manifest.xml 时发生错误: ${error instanceof Error ? error.message : '未知错误'}`;
        console.error(errorMessage);
        errors.push(errorMessage);
        return { success: false, errors };
    }
}

export { maml_scrcpt_main };
