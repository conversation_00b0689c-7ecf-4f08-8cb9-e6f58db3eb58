/**
 * 删除空属性
 * @param rootElement - 根元素
 */
export function delete_empty_properties(rootElement: Element): void {
    if (!rootElement || !rootElement.nodeType) {
        console.error('传入的rootElement无效');
        return;
    }

    // 获取所有元素并转换为数组，避免实时更新的NodeList
    const allElements = [ rootElement, ...Array.from(rootElement.getElementsByTagName('*')) ];

    // 批量处理所有元素
    allElements.forEach(element => {
        if (!element) return;

        const attributes = element.attributes;
        if (!attributes || attributes.length === 0) return;

        // 收集需要删除的属性
        const attributesToRemove: string[] = [];

        // 检查所有属性
        for (let i = attributes.length - 1; i >= 0; i--) {
            const attr = attributes[ i ];
            // 只删除属性值为空字符串的情况
            if (attr.value === '') {
                attributesToRemove.push(attr.name);
            }
        }

        // 批量删除属性
        attributesToRemove.forEach(name => {
            element.removeAttribute(name);
        });
    });
}
