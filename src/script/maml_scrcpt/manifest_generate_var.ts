// manifest_generate_var.ts
// 生成变量标签：为<VariableCommand>标签生成<Var>标签
// import { DOMParser, XMLSerializer } from 'xmldom';

/**
 * 检查字符串是否符合数组格式（即 xxx[]）
 * @param type - 要检查的类型字符串
 * @returns 如果是数组格式，返回 true；否则，返回 false
 */
const isArrayType = (type: string): boolean => {
    return /\[\]$/.test(type);
};

// /**
//  * 生成唯一的 name 属性
//  * @param existingNames - 已存在的 name 集合
//  * @param baseName - 基础名称
//  * @returns 唯一的名称
//  */
// const generateUniqueName = (existingNames: Set<string>, baseName: string = 'generatedName'): string => {
//     let uniqueName = baseName;
//     let counter = 1;
//     while (existingNames.has(uniqueName)) {
//         uniqueName = `${baseName}_${counter}`;
//         counter++;
//     }
//     existingNames.add(uniqueName);
//     return uniqueName;
// };

/**
 * 检查一个元素是否被指定标签包裹
 * @param element - 要检查的元素
 * @param tagName - 包裹元素的标签名称
 * @returns 如果被指定标签包裹，返回包裹的父元素；否则，返回 null
 */
const getParentByTagName = (element: Element, tagName: string): Element | null => {
    let parent = element.parentNode;
    while (parent && parent.nodeType === 1) { // ELEMENT_NODE
        if ((parent as Element).tagName === tagName) {
            return parent as Element;
        }
        parent = parent.parentNode;
    }
    return null;
};

/**
 * 递归遍历所有子元素，查找符合条件的 <VariableCommand> 标签并生成相应的 <Var> 标签
 * @param element - 当前遍历的元素
 * @param root - manifest.xml 的根 Element 节点
 * @param existingNames - 已存在的 name 集合
 * @param existingVarNames - 已存在的 <Var.name> 集合
 * @param collectedVars - 收集生成的 <Var> 标签
 */
const traverseAndTransform = (
    element: Element,
    root: Element,
    existingNames: Set<string>,
    existingVarNames: Set<string>,
    collectedVars: Element[]
): void => {
    // 遍历当前元素的子节点
    const children = element.childNodes;
    for (let i = 0; i < children.length; i++) {
        const child = children[ i ];
        if (child.nodeType === 1) { // ELEMENT_NODE
            const childElement = child as Element;
            const tagName = childElement.tagName;

            // 如果是 <VariableCommand>，进行处理
            if (tagName === 'VariableCommand') {
                const typeAttr = childElement.getAttribute('type') || '';
                if (isArrayType(typeAttr)) {
                    const varName = childElement.getAttribute('name');
                    const varType = typeAttr;

                    if (!varName) {
                        continue;
                    }

                    if (existingVarNames.has(varName)) {
                        continue;
                    }

                    // 检查是否被 <LoopCommand> 包裹
                    const loopCommandParent = getParentByTagName(childElement, 'LoopCommand');

                    let size: string;

                    if (loopCommandParent) {
                        const countAttr = loopCommandParent.getAttribute('count');
                        if (countAttr && /^\d+$/.test(countAttr)) {
                            size = countAttr;
                        } else {
                            size = '99';
                        }
                    } else {
                        // 未被 <LoopCommand> 包裹，检查是否有 size 属性
                        const sizeAttr = childElement.getAttribute('size');
                        if (sizeAttr) {
                            size = sizeAttr;
                        } else {
                            size = '99';
                        }
                    }



                    // 创建 <Var> 元素
                    const varElement = root.ownerDocument.createElement('Var');
                    varElement.setAttribute('name', varName);
                    varElement.setAttribute('size', size);
                    varElement.setAttribute('type', varType);
                    varElement.setAttribute('const', 'true');

                    // 收集生成的 <Var> 标签
                    collectedVars.push(varElement);
                    existingVarNames.add(varName);

                    // console.log(`%c添加默认声明：\n %c<Var name="${varName}" size="${size}" type="${varType}" const="true" />`, 'color:#ff0000', 'color:#0055ff');
                }
            }

            // 递归处理子元素
            traverseAndTransform(childElement, root, existingNames, existingVarNames, collectedVars);
        }
    }
};

/**
 * 转换所有符合条件的 <VariableCommand> 标签，生成对应的 <Var> 标签并插入到根元素的第一行
 * @param root - manifest.xml 的根 Element 节点
 */
export const manifest_generate_var = (root: Element): void => {
    const collectedVars: Element[] = [];

    // 收集所有现有的 name 属性，以确保生成唯一名称
    const existingNames = new Set<string>();
    const existingVarNames = new Set<string>();
    Array.from(root.getElementsByTagName('*')).forEach((elem) => {
        const nameAttr = elem.getAttribute('name');
        if (nameAttr) {
            existingNames.add(nameAttr);
            if (elem.tagName === 'Var') {
                existingVarNames.add(nameAttr);
            }
        }
    });

    // 开始递归遍历并转换
    traverseAndTransform(root, root, existingNames, existingVarNames, collectedVars);

    if (collectedVars.length === 0) {
        return;
    }

    // 插入生成的 <Var> 标签到根元素的第一行
    const fragment = root.ownerDocument.createDocumentFragment();
    collectedVars.forEach(varElem => {
        fragment.appendChild(varElem);
    });

    if (root.firstChild) {
        root.insertBefore(fragment, root.firstChild);
    } else {
        root.appendChild(fragment);
    }
};