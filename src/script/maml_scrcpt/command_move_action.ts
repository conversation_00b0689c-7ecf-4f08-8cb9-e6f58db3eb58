// command_move_action.ts
// 命令钩子转换器：将带有hook属性的标签移动到<ExternalCommands>标签中
// import { XMLSerializer } from 'xmldom';
// import { format_xml } from './format_xml';

export function command_move_action(root: Element): void {

    // 步骤1：收集所有带有'hook'属性的元素
    const hooks: Element[] = [];
    function traverse(node: Element): void {
        if (node.hasAttribute('hook')) {
            hooks.push(node);
        }
        for (let i = 0; i < node.childNodes.length; i++) {
            const child = node.childNodes[ i ];
            if (child.nodeType === 1) { // 1表示元素节点类型
                traverse(child as Element);
            }
        }
    }
    traverse(root);

    // 如果没有hook元素,直接返回
    if (hooks.length === 0) {
        return;
    }

    // 步骤2：确保ExternalCommands存在，并在根元素的第一行开始创建
    let externalCommands = root.getElementsByTagName('ExternalCommands')[ 0 ];
    if (!externalCommands) {
        externalCommands = root.ownerDocument.createElement('ExternalCommands');
        root.insertBefore(externalCommands, root.firstChild); // 插入到根元素的第一行
    }

    // 记录修改前的ExternalCommands
    // const serializer = new XMLSerializer();
    // const beforeXml = serializer.serializeToString(externalCommands);

    // 步骤3：处理每个hook元素
    hooks.forEach(hookElement => {
        const hookValue = hookElement.getAttribute('hook')!;
        // 查找或创建带有action="${hookValue}"的Trigger
        let trigger = null;
        for (let i = 0; i < externalCommands.childNodes.length; i++) {
            const child = externalCommands.childNodes[ i ];
            if (child.nodeType === 1 && (child as Element).tagName === 'Trigger' && (child as Element).getAttribute('action') === hookValue) {
                trigger = child as Element;
                break;
            }
        }
        if (!trigger) {
            trigger = root.ownerDocument.createElement('Trigger');
            trigger.setAttribute('action', hookValue);
            externalCommands.appendChild(trigger);
        }
        // 去除原标签的hook属性
        hookElement.removeAttribute('hook');
        // 将hookElement移动到trigger中
        hookElement.parentNode!.removeChild(hookElement);
        trigger.appendChild(hookElement);
    });

    // 记录修改后的ExternalCommands
    // const afterXml = serializer.serializeToString(externalCommands);

    // 只有当ExternalCommands发生变化时才打印
    // if (beforeXml !== afterXml) {
    //     console.log(`%c修改后的 <ExternalCommands> 片段：\n%c${format_xml(afterXml)}\n\n`, 'color: #ff0000', 'color: #0055ff');
    // }
}
