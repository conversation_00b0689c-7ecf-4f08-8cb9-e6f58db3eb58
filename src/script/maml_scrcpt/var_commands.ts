// VarCommandTransformer.ts
// 变量命令转换器：将<VarCommand>标签转换为计算最大值或最小值的命令
// import { XMLSerializer } from 'xmldom';
import { VarType, varTypePrefixes } from './var_types';
// import { format_xml } from './format_xml';
/**
 * 根据 <Var> 标签的 type 属性，返回变量前缀。
 * @param root - manifest.xml 的根 Element 节点
 * @param varName - <Var> 标签的 name 属性值
 * @returns '#' 或 '@'
 */
const getVarPrefix = (root: Element, varName: string): string => {
    const varElements = Array.from(root.getElementsByTagName('Var')) as Element[];
    for (const varElement of varElements) {
        if (varElement.getAttribute('name') === varName) {
            const type = varElement.getAttribute('type') as VarType;
            if (type && varTypePrefixes[ type ]) {
                return varTypePrefixes[ type ];
            } else if (type) {
                console.warn(`未知的 type="${type}"，默认使用 '#' 作为前缀。`);
                return '#';
            }
        }
    }
    // 如果未找到对应的 <Var> 标签，默认使用 '#'
    console.warn(`未找到 name="${varName}" 的 <Var> 标签，默认使用 '#' 作为前缀。`);
    return '#';
};

/**
 * 转换 <VarCommand> 标签为新的标签结构。
 * 该功能应在展开属性之后执行。
 * @param root - manifest.xml 的根 Element 节点
 */
export const var_commands = (root: Element): void => {
    // 查找所有 <VarCommand> 元素
    const varCommands = Array.from(root.getElementsByTagName('VarCommand')) as Element[];

    if (varCommands.length === 0) {
        return;
    }

    // 遍历每个 <VarCommand> 元素并进行转换
    varCommands.forEach(varCommand => {
        const name = varCommand.getAttribute('date');
        const returnAttr = varCommand.getAttribute('return');
        const type = varCommand.getAttribute('type');

        if (!name || !returnAttr || !type) {
            console.warn(`<VarCommand> 标签缺少必要的属性 (date, return, type)，跳过该标签。`);
            return;
        }

        // 获取变量前缀
        const prefix = getVarPrefix(root, name);

        // 创建 <VariableCommand> 元素用于初始化
        const variableCommandInit = root.ownerDocument.createElement('VariableCommand');
        variableCommandInit.setAttribute('name', returnAttr);
        variableCommandInit.setAttribute('expression', `${prefix}${name}[0]`);
        variableCommandInit.setAttribute('type', 'number');

        // 创建 <LoopCommand> 元素
        const loopCommand = root.ownerDocument.createElement('LoopCommand');
        loopCommand.setAttribute('count', `#${name}.length`);
        loopCommand.setAttribute('indexName', 'i');

        // 创建 <MultiCommand> 元素
        const multiCommand = root.ownerDocument.createElement('MultiCommand');

        // 根据 type 属性设置 condition 和 expression
        if (type === 'max' || type === 'min') {
            // 根据 type 生成不同的条件格式
            const conditionOperator = type === 'max' ? '}' : '{';
            multiCommand.setAttribute('condition', `${prefix}${name}[#i] ${conditionOperator} #${returnAttr}`);

            // 设置 expression 为 #name[#i] 或 @name[@i]
            const expression = `${prefix}${name}[#i]`;
            const innerVariableCommand = root.ownerDocument.createElement('VariableCommand');
            innerVariableCommand.setAttribute('name', returnAttr);
            innerVariableCommand.setAttribute('expression', expression);
            innerVariableCommand.setAttribute('type', 'number');
            multiCommand.appendChild(innerVariableCommand);
        } else {
            console.warn(`未知的 type 属性值: ${type}，跳过该 <VarCommand> 标签。`);
            return;
        }

        loopCommand.appendChild(multiCommand);

        // 创建一个文档片段，将新元素插入到 <VarCommand> 标签的位置
        const fragment = root.ownerDocument.createDocumentFragment();
        fragment.appendChild(variableCommandInit);
        fragment.appendChild(loopCommand);

        // 获取 <VarCommand> 的父节点
        const parent = varCommand.parentNode;
        if (!parent) {
            console.warn("<VarCommand> 标签没有父节点，无法替换。");
            return;
        }
        // 修改前：序列化并打印修改前的 XML
        // const serializer = new XMLSerializer();
        // const beforeXML = serializer.serializeToString(varCommand);
        // console.log(`\n\n%c修改前的 XML 片段：\n${format_xml(beforeXML)}`, 'color:#bf2c9f;');

        // 修改后：序列化并打印修改后的 XML
        // const afterXML = serializer.serializeToString(fragment);
        // console.log(`%c修改后的 XML 片段：\n${format_xml(afterXML)}\n\n`, 'color: #0055ff');
        // 替换 <VarCommand> 标签
        parent.replaceChild(fragment, varCommand);
    });
};
