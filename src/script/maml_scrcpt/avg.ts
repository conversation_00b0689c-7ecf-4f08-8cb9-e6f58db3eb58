/**
 * 将Avg标签转换为Group标签，实现创建网格布局
 * @param rootElement - 根元素
 */
export function avg(rootElement: Element): void {
    if (!rootElement || !rootElement.nodeType) {
        console.error('传入的rootElement无效');
        return;
    }

    // 查找所有Avg标签
    const avgElements = rootElement.getElementsByTagName('Avg');

    if (avgElements.length === 0) {
        return;
    }

    // 存储所有Avg标签的name和对应的变量名映射
    const avgNameMap: Record<string, { widthVar: string, heightVar: string }> = {};

    // 将NodeList转换为数组并遍历
    Array.from(avgElements).forEach((avgElement) => {
        // 获取name属性
        const avgName = avgElement.getAttribute('name');

        if (!avgName) {
            console.error('Avg标签缺少name属性');
            return;
        }

        // 直接使用name属性值作为变量名
        const widthVarName = `${avgName}.w`;
        const heightVarName = `${avgName}.h`;

        // 存储原始name和对应的宽高变量名
        avgNameMap[ avgName ] = {
            widthVar: widthVarName,
            heightVar: heightVarName
        };

        // 获取Avg标签的属性
        const padding = avgElement.getAttribute('padding') || '0';
        const gap = avgElement.getAttribute('gap') || '0';
        const w = avgElement.getAttribute('w') || '0';
        const h = avgElement.getAttribute('h') || '0';
        const row = parseInt(avgElement.getAttribute('row') || '1');
        const column = parseInt(avgElement.getAttribute('column') || '1');

        // 计算总数
        const totalCount = row * column;

        // 创建新的父元素来存放转换后的内容
        const container = rootElement.ownerDocument.createElement('Group');

        // 复制除了特定属性之外的所有属性到新的Group标签
        const excludedAttributes = [ 'padding', 'gap', 'row', 'column', 'indexName' ];
        Array.from(avgElement.attributes).forEach(attr => {
            if (!excludedAttributes.includes(attr.name)) {
                container.setAttribute(attr.name, attr.value);
            }
        });

        // 创建width变量
        const widthVar = rootElement.ownerDocument.createElement('Var');
        widthVar.setAttribute('name', widthVarName);
        widthVar.setAttribute('expression', `(${w}-${padding}*2-${gap}*(${row}-1))/${row}`);
        widthVar.setAttribute('type', 'number');
        widthVar.setAttribute('const', 'false');

        // 创建height变量
        const heightVar = rootElement.ownerDocument.createElement('Var');
        heightVar.setAttribute('name', heightVarName);
        heightVar.setAttribute('expression', `(${h}-${padding}*2-${gap}*(${column}-1))/${column}`);
        heightVar.setAttribute('type', 'number');
        heightVar.setAttribute('const', 'false');

        // 创建Array元素
        const arrayElement = rootElement.ownerDocument.createElement('Array');
        arrayElement.setAttribute('count', totalCount.toString());
        // 获取原始Avg标签的indexName属性，如果没有则默认使用'i'
        const indexName = avgElement.getAttribute('indexName') || 'i';
        arrayElement.setAttribute('indexName', indexName);

        // 获取原始的Rectangle和Text元素
        const originalRect = avgElement.getElementsByTagName('Rectangle')[ 0 ];
        const originalText = avgElement.getElementsByTagName('Text')[ 0 ];

        // 创建Group模板，使用继承的indexName
        const groupTemplate = rootElement.ownerDocument.createElement('Group');
        groupTemplate.setAttribute('x', `${padding}+(#${indexName}%${row})*(#${widthVarName}+${gap})`);
        groupTemplate.setAttribute('y', `${padding}+int(#${indexName}/${row})*(#${heightVarName}+${gap})`);

        // 复制原始元素到Group中
        if (originalRect) {
            const newRect = rootElement.ownerDocument.createElement('Rectangle');
            // 复制所有属性
            Array.from(originalRect.attributes).forEach(attr => {
                newRect.setAttribute(attr.name, attr.value);
            });
            groupTemplate.appendChild(newRect);
        }

        if (originalText) {
            const newText = rootElement.ownerDocument.createElement('Text');
            // 复制所有属性
            Array.from(originalText.attributes).forEach(attr => {
                newText.setAttribute(attr.name, attr.value);
            });
            groupTemplate.appendChild(newText);
        }

        // 组装新的结构
        arrayElement.appendChild(groupTemplate);
        container.appendChild(widthVar);
        container.appendChild(heightVar);
        container.appendChild(arrayElement);

        // 替换原始的Avg标签
        avgElement.parentNode?.replaceChild(container, avgElement);
    });

    // 不需要替换XML中的变量引用，因为我们直接使用了原始的变量名
}
