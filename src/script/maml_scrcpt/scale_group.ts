// import { XMLSerializer } from 'xmldom';
// import { format_xml } from './format_xml';

/**
 * 将scale_group标签转换为Group标签,中心点编号(1-9)
 * @param rootElement - 文档中的根元素节点，类型为 Element
 */
export function scale_group(rootElement: Element): void {
    // 查找所有 scaleGroup 元素
    const scaleGroupElements = Array.from(rootElement.getElementsByTagName('ScaleGroup')) as Element[];

    // 添加明确的日志提示
    if (scaleGroupElements.length === 0) {
        return;
    }

    // 遍历每个 ScaleGroup 元素
    scaleGroupElements.forEach(element => {
        // 获取必要的属性
        const width = element.getAttribute('w');
        const height = element.getAttribute('h');
        const centerPoint = element.getAttribute('centerPoint');
        const type = element.getAttribute('type');

        // 检查必要属性是否存在
        if (!width || !height || !centerPoint || !type) {
            // console.warn('ScaleGroup 标签缺少必要的属性 (w, h, centerPoint, type)，跳过转换');
            return;
        }

        // 创建新的 Group 元素
        const group = rootElement.ownerDocument.createElement('Group');

        // 设置基本属性
        group.setAttribute('w', width);
        group.setAttribute('h', height);

        // 根据 type 设置 scale
        const scaleExpression = `${type}(#view_width/${width},#view_height/${height})`;
        group.setAttribute('scale', scaleExpression);

        // 根据 centerPoint 获取配置并设置属性
        const config = getConfigByCenterPoint(parseInt(centerPoint), width, height);
        group.setAttribute('align', config.align);
        group.setAttribute('alignV', config.alignV);
        group.setAttribute('x', config.x);
        group.setAttribute('y', config.y);

        // 保留原有的 pivotX 和 pivotY，如果不存在则使用默认值
        const pivotX = element.getAttribute('pivotX') || config.pivotX;
        const pivotY = element.getAttribute('pivotY') || config.pivotY;
        group.setAttribute('pivotX', pivotX);
        group.setAttribute('pivotY', pivotY);

        // 继承其他属性，排除要忽略的属性
        const ignoreAttributes = [
            'w', 'h', 'centerPoint', 'type',
            'scale', 'align', 'alignV', 'x', 'y'
        ];

        Array.from(element.attributes).forEach(attr => {
            if (!ignoreAttributes.includes(attr.name)) {
                group.setAttribute(attr.name, attr.value);
            }
        });

        // 移动子节点
        while (element.firstChild) {
            group.appendChild(element.firstChild);
        }

        // 修改前的XML
        // const serializer = new XMLSerializer();
        // const beforeXML = serializer.serializeToString(element);
        // // console.log(`\n\n%c修改前的<ScaleGroup> 片段：\n${format_xml(beforeXML)}`, 'color:#bf2c9f;');

        // 替换原始节点
        element.parentNode?.replaceChild(group, element);

        // 修改后的XML
        // const afterXML = serializer.serializeToString(group);
        // console.log(`%c修改后的 <Group> 片段：\n${format_xml(afterXML)}\n\n`, 'color: #0055ff');
    });
}

interface CenterPointConfig {
    x: string;
    y: string;
    align: string;
    alignV: string;
    pivotX: string;
    pivotY: string;
}

/**
 * 根据中心点获取配置
 * @param centerPoint - 中心点编号(1-9)
 * @param width - 宽度
 * @param height - 高度
 * @returns 配置对象
 */
function getConfigByCenterPoint(centerPoint: number, width: string, height: string): CenterPointConfig {
    const configs: { [ key: number ]: CenterPointConfig } = {
        1: { // 左上
            x: '0',
            y: '0',
            align: 'left',
            alignV: 'top',
            pivotX: '0',
            pivotY: '0'
        },
        2: { // 上中
            x: '#view_width/2',
            y: '0',
            align: 'center',
            alignV: 'top',
            pivotX: `${width}/2`,
            pivotY: '0'
        },
        3: { // 右上
            x: '#view_width',
            y: '0',
            align: 'right',
            alignV: 'top',
            pivotX: width,
            pivotY: '0'
        },
        4: { // 左中
            x: '0',
            y: '#view_height/2',
            align: 'left',
            alignV: 'center',
            pivotX: '0',
            pivotY: `${height}/2`
        },
        5: { // 中间
            x: '#view_width/2',
            y: '#view_height/2',
            align: 'center',
            alignV: 'center',
            pivotX: `${width}/2`,
            pivotY: `${height}/2`
        },
        6: { // 右中
            x: '#view_width',
            y: '#view_height/2',
            align: 'right',
            alignV: 'center',
            pivotX: width,
            pivotY: `${height}/2`
        },
        7: { // 左下
            x: '0',
            y: '#view_height',
            align: 'left',
            alignV: 'bottom',
            pivotX: '0',
            pivotY: height
        },
        8: { // 下中
            x: '#view_width/2',
            y: '#view_height',
            align: 'center',
            alignV: 'bottom',
            pivotX: `${width}/2`,
            pivotY: height
        },
        9: { // 右下
            x: '#view_width',
            y: '#view_height',
            align: 'right',
            alignV: 'bottom',
            pivotX: width,
            pivotY: height
        }
    };

    return configs[ centerPoint ] || configs[ 5 ]; // 默认返回中间配置
}
