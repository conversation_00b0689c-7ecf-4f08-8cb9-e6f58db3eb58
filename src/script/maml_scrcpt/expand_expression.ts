// 处理表达式属性：将expression、ifCondition、condition、values、textExp属性展开
export const expand_expression = (node: Element) => {
    const expandExpression = (expression: string): string => {
        // 检查是否被单引号包裹
        if (expression.startsWith("'") && expression.endsWith("'")) {
            return expression;
        }

        const regex = /([@#])([A-Za-z0-9_.]+)\[(\d+),(.*?),(\d+)\]/g;
        return expression.replace(regex, (_, prefix, param, startIndex, operator, endIndex) => {
            let expanded = '';
            // 处理空运算符的情况
            const actualOperator = operator.trim() || ',';
            for (let i = Number(startIndex); i <= Number(endIndex); i++) {
                if (i > Number(startIndex)) {
                    expanded += ` ${actualOperator} ${prefix}${param}[${i}]`;
                } else {
                    expanded += `${prefix}${param}[${i}]`;
                }
            }
            // 修改前：序列化并打印修改前的 XML
            // console.log(`\n\n%c修改前的 XML 片段：\n${expression}`, 'color:#bf2c9f;');
            // 修改后：序列化并打印修改后的 XML
            // console.log(`%c修改后的 XML 片段：\n${expanded}\n\n`, 'color: #0055ff');
            return expanded;
        });
    };

    const traverse = (currentNode: Element) => {
        // 处理属性
        const attributes = currentNode.attributes;


        // 检查是否有需要展开的属性
        for (let i = 0; i < attributes.length; i++) {
            const attr = attributes[ i ];
            if ([ 'expression', 'ifCondition', 'condition', 'values', 'textExp' ].includes(attr.name)) {

                const originalValue = attr.value;
                const expanded = expandExpression(originalValue);
                if (expanded !== originalValue) {
                    currentNode.setAttribute(attr.name, expanded);
                }
            }
        }


        // 递归处理子节点
        const children = currentNode.childNodes;
        for (let i = 0; i < children.length; i++) {
            const child = children[ i ];
            if (child.nodeType === 1) { // ELEMENT_NODE
                traverse(child as Element);
            }
        }
    };

    traverse(node);
};
