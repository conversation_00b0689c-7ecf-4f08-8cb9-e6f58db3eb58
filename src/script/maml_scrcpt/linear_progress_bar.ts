/**
 * 将LinearProgressBar标签转换为进度条标签
 * @param rootElement XML文档中的根元素节点
 */
export function linear_progress_bar(rootElement: Element): void {
    try {
        // 获取所有LinearProgressBar标签并转换为数组
        const bars = Array.from(rootElement.getElementsByTagName('LinearProgressBar'));

        if (bars.length === 0) {
            return;
        }

        // 使用第一个bar的document
        const doc = bars[ 0 ].ownerDocument;
        const firstParent = bars[ 0 ].parentNode;
        if (!firstParent) {
            console.error('找不到第一个进度条的父节点');
            return;
        }

        // 创建共享元素的名称
        const sharedNames = {
            function: 'fra120_shared',
            config: 'config_progress_shared',
        };

        // 检查是否已存在共享元素，如果不存在则创建
        const functionExists = checkIfElementExists(rootElement, 'Function', sharedNames.function);
        const configExists = checkIfElementExists(rootElement, 'FolmeConfig', sharedNames.config);
        const fraExists = checkIfElementExists(rootElement, 'FramerateController', 'fra');

        // 如果需要共享元素，则创建它们
        if (!functionExists || !configExists || !fraExists) {
            // 创建共享元素（只有Function和FolmeConfig）
            const sharedElements = createSharedElements(doc, sharedNames, {
                createFunction: !functionExists,
                createConfig: !configExists,
                createFra: !fraExists
            });

            // 将共享元素插入到第一个进度条的父元素之前
            firstParent.insertBefore(sharedElements, bars[ 0 ]);
        }

        // 处理每个LinearProgressBar标签
        bars.forEach((bar) => {
            try {
                // 获取父节点
                const parentNode = bar.parentNode;
                if (!parentNode) {
                    console.error('找不到父节点');
                    return;
                }

                // 生成随机字符串用于命名
                const randomString = bar.getAttribute('id') || Math.random().toString(36).substring(2, 8);

                // 获取所有属性
                const attrs = {
                    x: bar.getAttribute('x') || '0',
                    y: bar.getAttribute('y') || '0',
                    w: bar.getAttribute('w') || '0',
                    h: bar.getAttribute('h') || '0',
                    progress: bar.getAttribute('progress') || '0',
                    fillColor: (bar.getAttribute('fillColor') || '#ffffff,#277af7').split(','),
                    strokeColor: (bar.getAttribute('strokeColor') || '#00000000,#00000000').split(','),
                    weight: (bar.getAttribute('weight') || '0,0').split(','),
                    alpha: (bar.getAttribute('alpha') || '255,255').split(','),
                    strokeAlign: bar.getAttribute('strokeAlign') || 'inner',
                    padding: bar.getAttribute('padding') || '0',
                    cornerRadius: (bar.getAttribute('cornerRadius') || '0,0').split(','),
                    direction: bar.getAttribute('direction') || 'toRight',
                    align: bar.getAttribute('align') || 'left',
                    alignV: bar.getAttribute('alignV') || 'top'
                };

                // 计算对齐后的坐标
                const alignedCoords = calculateAlignedCoordinates(attrs);
                const { alignedX, alignedY } = alignedCoords;

                // 获取FillShaders和StrokeShaders标签
                const fillShaders = Array.from(bar.getElementsByTagName('FillShaders'));
                const strokeShaders = Array.from(bar.getElementsByTagName('StrokeShaders'));

                // 使用文档片段收集所有新元素
                const fragment = bar.ownerDocument.createDocumentFragment();

                // 创建FolmeState元素（每个进度条自己的）
                const folmeState = bar.ownerDocument.createElement('FolmeState');
                folmeState.setAttribute('name', `progress_${randomString}`);

                // 根据direction设置不同的属性
                switch (attrs.direction) {
                    case 'toRight':
                    case 'toLeft':
                        folmeState.setAttribute('w', `max((${attrs.w} - ${attrs.padding} * 2) * ${attrs.progress},0.001)`);
                        break;
                    case 'toDown':
                    case 'toUp':
                        folmeState.setAttribute('h', `max((${attrs.h} - ${attrs.padding} * 2) * ${attrs.progress},0.001)`);
                        break;
                }
                fragment.appendChild(folmeState);

                // 创建Var元素（绑定进度值到各自的FolmeState）
                const varElement = doc.createElement('Var');
                varElement.setAttribute('name', `var_progress_${randomString}`);
                varElement.setAttribute('expression', attrs.progress);
                varElement.setAttribute('type', 'string');

                const trigger = doc.createElement('Trigger');
                const folmeCommand = doc.createElement('FolmeCommand');
                folmeCommand.setAttribute('target', `progressGroup_${randomString}`);
                folmeCommand.setAttribute('states', `'progress_${randomString}'`);
                folmeCommand.setAttribute('command', 'to');
                folmeCommand.setAttribute('config', `'${sharedNames.config}'`);  // 使用共享的FolmeConfig

                trigger.appendChild(folmeCommand);
                varElement.appendChild(trigger);
                fragment.appendChild(varElement);

                // 创建背景Rectangle
                const backgroundRect = createBackgroundRectangle(
                    bar.ownerDocument, alignedX, alignedY, attrs.w, attrs.h,
                    attrs.fillColor[ 0 ], attrs.strokeColor[ 0 ], attrs.alpha[ 0 ],
                    attrs.cornerRadius[ 0 ], attrs.weight[ 0 ], attrs.strokeAlign
                );

                // 处理背景的FillShaders和StrokeShaders
                fillShaders.forEach(shader => {
                    if (shader.getAttribute('tag') === '0') {
                        try {
                            const shaderClone = shader.cloneNode(true) as Element;
                            shaderClone.removeAttribute('tag');
                            backgroundRect.appendChild(shaderClone);
                        } catch (error) {
                            console.error('克隆背景Shader节点时发生错误:', error);
                        }
                    }
                });
                strokeShaders.forEach(shader => {
                    if (shader.getAttribute('tag') === '0') {
                        try {
                            const shaderClone = shader.cloneNode(true) as Element;
                            shaderClone.removeAttribute('tag');
                            backgroundRect.appendChild(shaderClone);
                        } catch (error) {
                            console.error('克隆背景Shader节点时发生错误:', error);
                        }
                    }
                });
                fragment.appendChild(backgroundRect);

                // 创建进度条Group
                const progressGroup = bar.ownerDocument.createElement('Group');
                progressGroup.setAttribute('name', `progressGroup_${randomString}`);
                progressGroup.setAttribute('clip', 'true');

                // 根据direction设置Group属性
                switch (attrs.direction) {
                    case 'toRight':
                        progressGroup.setAttribute('x', `${alignedX}+${attrs.padding}`);
                        progressGroup.setAttribute('y', `${alignedY}+${attrs.padding}`);
                        progressGroup.setAttribute('w', '0.001');
                        progressGroup.setAttribute('h', `${attrs.h}-${attrs.padding}*2`);
                        break;
                    case 'toLeft':
                        progressGroup.setAttribute('x', `${alignedX} + ${attrs.w} - ${attrs.padding}`);
                        progressGroup.setAttribute('y', `${alignedY}+${attrs.padding}`);
                        progressGroup.setAttribute('w', '0.001');
                        progressGroup.setAttribute('h', `${attrs.h}-${attrs.padding}*2`);
                        progressGroup.setAttribute('scaleX', '-1');
                        break;
                    case 'toDown':
                        progressGroup.setAttribute('x', `${alignedX} + ${attrs.padding}`);
                        progressGroup.setAttribute('y', `${alignedY}+${attrs.padding}`);
                        progressGroup.setAttribute('w', `${attrs.w}-${attrs.padding}*2`);
                        progressGroup.setAttribute('h', '0.001');
                        break;
                    case 'toUp':
                        progressGroup.setAttribute('x', `${alignedX} + ${attrs.padding}`);
                        progressGroup.setAttribute('y', `${alignedY} + ${attrs.h} - ${attrs.padding}`);
                        progressGroup.setAttribute('w', `${attrs.w}-${attrs.padding}*2`);
                        progressGroup.setAttribute('h', '0.001');
                        progressGroup.setAttribute('scaleY', '-1');
                        break;
                }

                // 创建进度条Rectangle
                const progressRect = bar.ownerDocument.createElement('Rectangle');
                progressRect.setAttribute('w', `${attrs.w}-${attrs.padding}*2`);
                progressRect.setAttribute('h', `${attrs.h}-${attrs.padding}*2`);
                progressRect.setAttribute('fillColor', attrs.fillColor[ 1 ]);
                progressRect.setAttribute('strokeColor', attrs.strokeColor[ 1 ]);
                progressRect.setAttribute('alpha', attrs.alpha[ 1 ]);
                progressRect.setAttribute('weight', attrs.weight[ 1 ]);
                progressRect.setAttribute('strokeAlign', attrs.strokeAlign);

                // 设置圆角
                if (attrs.cornerRadius.length > 1) {
                    progressRect.setAttribute('cornerRadiusExp', attrs.cornerRadius[ 1 ]);
                } else {
                    const cornerRadiusExp = attrs.direction === 'toDown' || attrs.direction === 'toUp'
                        ? `max(${attrs.cornerRadius[ 0 ]} - ${attrs.padding},(${attrs.w}-${attrs.padding}*2)/8)`
                        : `max(${attrs.cornerRadius[ 0 ]} - ${attrs.padding},(${attrs.h}-${attrs.padding}*2)/8)`;
                    progressRect.setAttribute('cornerRadiusExp', cornerRadiusExp);
                }

                // 处理进度条的FillShaders和StrokeShaders
                fillShaders.forEach(shader => {
                    if (shader.getAttribute('tag') === '1') {
                        try {
                            const shaderClone = shader.cloneNode(true) as Element;
                            shaderClone.removeAttribute('tag');
                            progressRect.appendChild(shaderClone);
                        } catch (error) {
                            console.error('克隆进度条Shader节点时发生错误:', error);
                        }
                    }
                });
                strokeShaders.forEach(shader => {
                    if (shader.getAttribute('tag') === '1') {
                        try {
                            const shaderClone = shader.cloneNode(true) as Element;
                            shaderClone.removeAttribute('tag');
                            progressRect.appendChild(shaderClone);
                        } catch (error) {
                            console.error('克隆进度条Shader节点时发生错误:', error);
                        }
                    }
                });

                progressGroup.appendChild(progressRect);
                fragment.appendChild(progressGroup);

                // 一次性插入所有新元素
                parentNode.insertBefore(fragment, bar);
                parentNode.removeChild(bar);

            } catch (error) {
                console.error('处理单个LinearProgressBar标签时发生错误:', error);
            }
        });
    } catch (error) {
        console.error('处理LinearProgressBar标签时发生错误:', error);
    }
}

/**
 * 检查文档中是否已存在特定类型和名称的元素
 */
function checkIfElementExists(rootElement: Element, tagName: string, name: string): boolean {
    try {
        const elements = Array.from(rootElement.getElementsByTagName(tagName));
        return elements.some(element => element.getAttribute('name') === name);
    } catch (error) {
        console.error(`检查元素 ${tagName} ${name} 是否存在时发生错误:`, error);
        return false;
    }
}

/**
 * 创建共享元素：只包含Function和FolmeConfig，根据需要选择性创建
 */
function createSharedElements(
    doc: Document,
    names: { function: string, config: string },
    options: { createFunction: boolean, createConfig: boolean, createFra: boolean }
): DocumentFragment {
    const fragment = doc.createDocumentFragment();

    // 创建共享的Function元素
    if (options.createFunction) {
        const functionElement = doc.createElement('Function');
        functionElement.setAttribute('name', names.function);
        const animationCommand = doc.createElement('AnimationCommand');
        animationCommand.setAttribute('target', 'fra');
        animationCommand.setAttribute('command', 'play');
        functionElement.appendChild(animationCommand);
        fragment.appendChild(functionElement);
    }

    // 创建共享的FramerateController元素
    if (options.createFra) {
        const framerateController = doc.createElement('FramerateController');
        framerateController.setAttribute('name', 'fra');
        framerateController.setAttribute('initPause', 'true');
        framerateController.setAttribute('loop', 'false');

        const controlPoint1 = doc.createElement('ControlPoint');
        controlPoint1.setAttribute('frameRate', '120');
        controlPoint1.setAttribute('time', '0');

        const controlPoint2 = doc.createElement('ControlPoint');
        controlPoint2.setAttribute('frameRate', '120');
        controlPoint2.setAttribute('time', '1000');

        const controlPoint3 = doc.createElement('ControlPoint');
        controlPoint3.setAttribute('frameRate', '0');
        controlPoint3.setAttribute('time', '1500');

        framerateController.appendChild(controlPoint1);
        framerateController.appendChild(controlPoint2);
        framerateController.appendChild(controlPoint3);
        fragment.appendChild(framerateController);
    }

    // 创建共享的FolmeConfig元素
    if (options.createConfig) {
        const folmeConfig = doc.createElement('FolmeConfig');
        folmeConfig.setAttribute('name', names.config);
        folmeConfig.setAttribute('ease', '-2,1,0.5');
        folmeConfig.setAttribute('onBegin', `'${names.function}'`);
        folmeConfig.setAttribute('onUpdate', `'${names.function}'`);
        folmeConfig.setAttribute('onEnd', `'${names.function}'`);
        fragment.appendChild(folmeConfig);
    }

    return fragment;
}

/**
 * 创建背景Rectangle元素
 */
function createBackgroundRectangle(
    doc: Document, x: string, y: string, w: string, h: string,
    fillColor: string, strokeColor: string, alpha: string,
    cornerRadius: string, weight: string, strokeAlign: string
): Element {
    const rectangle = doc.createElement('Rectangle');
    rectangle.setAttribute('x', x);
    rectangle.setAttribute('y', y);
    rectangle.setAttribute('w', w);
    rectangle.setAttribute('h', h);
    rectangle.setAttribute('fillColor', fillColor);
    rectangle.setAttribute('strokeColor', strokeColor);
    rectangle.setAttribute('alpha', alpha);
    rectangle.setAttribute('cornerRadiusExp', cornerRadius);
    rectangle.setAttribute('weight', weight);
    rectangle.setAttribute('strokeAlign', strokeAlign);

    return rectangle;
}

/**
 * 计算对齐后的坐标
 */
function calculateAlignedCoordinates(attrs: {
    x: string;
    y: string;
    w: string;
    h: string;
    align: string;
    alignV: string;
}): { alignedX: string; alignedY: string } {
    let alignedX = attrs.x;
    let alignedY = attrs.y;

    // 处理水平对齐
    switch (attrs.align) {
        case 'center':
            alignedX = `${attrs.x} + -${attrs.w} / 2`;
            break;
        case 'right':
            alignedX = `${attrs.x} + -${attrs.w}`;
            break;
        // 'left' 是默认值，不需要特殊处理
    }

    // 处理垂直对齐
    switch (attrs.alignV) {
        case 'center':
            alignedY = `${attrs.y} + -${attrs.h} / 2`;
            break;
        case 'bottom':
            alignedY = `${attrs.y} + -${attrs.h}`;
            break;
        // 'top' 是默认值，不需要特殊处理
    }

    return { alignedX, alignedY };
}
