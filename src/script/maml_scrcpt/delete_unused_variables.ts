/**
 * 删除未使用变量
 * @param rootElement - 根元素
 */
export function delete_unused_variables(rootElement: Element): void {
    if (!rootElement || !rootElement.nodeType) {
        console.error('传入的rootElement无效');
        return;
    }

    // 变量标签名列表（包含大小写变体）
    const variableTagNames = [
        'var', 'Var', 'VAR',
        'variablecommand', 'VariableCommand', 'VARIABLECOMMAND',
        'permanence', 'Permanence', 'PERMANENCE',
        'permanencecommand', 'PermanenceCommand', 'PERMANENCECOMMAND',
        'Variable', 'VARIABLE'
    ];

    // 存储所有变量节点 - 键为变量名，值为对应的元素节点列表
    const variableNodes = new Map<string, Element[]>();

    // 存储被直接使用的变量（不是在变量定义中被使用）
    const directlyUsedVariables = new Set<string>();

    // 存储变量依赖关系 - 键为变量名，值为该变量依赖的所有变量名集合
    const variableDependencies = new Map<string, Set<string>>();

    // 存储最终确定要保留的变量
    const variablesToKeep = new Set<string>();

    // 标记用于防止循环依赖导致的无限递归
    const visitingVariables = new Set<string>();

    // 存储被用作数组索引的变量
    const variablesUsedAsIndex = new Set<string>();

    /**
     * 检查节点是否有子元素标签（忽略纯空白的文本节点）
     */
    function hasChildElements(node: Element): boolean {
        for (let i = 0; i < node.childNodes.length; i++) {
            const child = node.childNodes[ i ];
            // 检查元素节点
            if (child.nodeType === 1) { // ELEMENT_NODE
                return true;
            }
            // 检查非空文本节点
            if (child.nodeType === 3 && child.textContent && child.textContent.trim().length > 0) { // TEXT_NODE
                return true;
            }
        }
        return false;
    }

    /**
     * 从表达式中提取变量引用
     * @param expression 要分析的表达式
     * @param isVariableDefinition 是否是在变量定义中，用于区分直接使用和变量依赖
     * @param sourceVariable 如果是变量定义，指定源变量名以建立依赖关系
     * @returns 提取的变量引用列表
     */
    function extractVariableReferences(
        expression: string,
        isVariableDefinition: boolean = false,
        sourceVariable: string | null = null
    ): string[] {
        if (!expression) return [];

        const extractedVariables: string[] = [];

        try {
            // 规则1: 匹配 #varName 和 #varName[index] 形式的变量引用
            // 确保使用\b词边界防止#var1和#var10混淆
            const hashRefs = expression.match(/#(\w[\w\-._]*)\b(?:\[([^\]]*)\])?/g);
            if (hashRefs) {
                for (const ref of hashRefs) {
                    const varNameMatch = ref.match(/#(\w[\w\-._]*)\b/);
                    const indexMatch = ref.match(/\[([^\]]*)\]/);
                    if (varNameMatch && varNameMatch[ 1 ]) {
                        const varName = varNameMatch[ 1 ];
                        const index = indexMatch ? indexMatch[ 1 ] : null;
                        const fullRef = index ? `${varName}[${index}]` : varName;
                        extractedVariables.push(fullRef);

                        // 根据上下文决定是记录直接使用还是依赖关系
                        if (!isVariableDefinition) {
                            directlyUsedVariables.add(fullRef);
                        } else if (sourceVariable) {
                            // 建立依赖关系：如果在变量A的定义中使用了变量B，则A依赖于B
                            if (!variableDependencies.has(sourceVariable)) {
                                variableDependencies.set(sourceVariable, new Set<string>());
                            }
                            variableDependencies.get(sourceVariable)?.add(fullRef);
                        }
                    }
                }
            }

            // 规则2: 匹配 @varName 和 @varName[index] 形式的变量引用
            const atRefs = expression.match(/@(\w[\w\-._]*)\b(?:\[([^\]]*)\])?/g);
            if (atRefs) {
                for (const ref of atRefs) {
                    const varNameMatch = ref.match(/@(\w[\w\-._]*)\b/);
                    const indexMatch = ref.match(/\[([^\]]*)\]/);
                    if (varNameMatch && varNameMatch[ 1 ]) {
                        const varName = varNameMatch[ 1 ];
                        const index = indexMatch ? indexMatch[ 1 ] : null;
                        const fullRef = index ? `${varName}[${index}]` : varName;
                        extractedVariables.push(fullRef);

                        if (!isVariableDefinition) {
                            directlyUsedVariables.add(fullRef);
                        } else if (sourceVariable) {
                            if (!variableDependencies.has(sourceVariable)) {
                                variableDependencies.set(sourceVariable, new Set<string>());
                            }
                            variableDependencies.get(sourceVariable)?.add(fullRef);
                        }
                    }
                }
            }

            // 规则3: 匹配 $varName 和 $$varName 形式的变量引用
            const dollarRefs = expression.match(/(\$\$|\$)(\w[\w\-._]*)\b/g);
            if (dollarRefs) {
                for (const ref of dollarRefs) {
                    const varNameMatch = ref.match(/(\$\$|\$)(\w[\w\-._]*)\b/);
                    if (varNameMatch && varNameMatch[ 2 ]) {
                        const varName = varNameMatch[ 2 ];
                        extractedVariables.push(varName);

                        if (!isVariableDefinition) {
                            directlyUsedVariables.add(varName);
                        } else if (sourceVariable && sourceVariable !== varName) {
                            if (!variableDependencies.has(sourceVariable)) {
                                variableDependencies.set(sourceVariable, new Set<string>());
                            }
                            variableDependencies.get(sourceVariable)?.add(varName);
                        }
                    }
                }
            }

            // 规则4: 处理特殊情况 - 字符串插值形式 'text_'+#var
            const stringTemplates = expression.match(/'[^']*'\s*\+\s*[#@$][\w\-._]+|[#@$][\w\-._]+\s*\+\s*'[^']*'/g);
            if (stringTemplates) {
                for (const template of stringTemplates) {
                    // 提取变量部分
                    const varMatch = template.match(/[#@$]([\w\-._]+)/);
                    if (varMatch && varMatch[ 1 ]) {
                        const varName = varMatch[ 1 ];
                        extractedVariables.push(varName);

                        if (!isVariableDefinition) {
                            directlyUsedVariables.add(varName);
                        } else if (sourceVariable && sourceVariable !== varName) {
                            if (!variableDependencies.has(sourceVariable)) {
                                variableDependencies.set(sourceVariable, new Set<string>());
                            }
                            variableDependencies.get(sourceVariable)?.add(varName);
                        }
                    }
                }
            }

            // 规则5: 处理函数调用中的变量引用，如max(#var1,#var2)
            const functionCalls = expression.match(/\b\w+\([^)]*\)/g);
            if (functionCalls) {
                for (const call of functionCalls) {
                    // 提取函数参数部分
                    const paramsMatch = call.match(/\(([^)]*)\)/);
                    if (paramsMatch && paramsMatch[ 1 ]) {
                        // 递归处理函数参数
                        const innerVars = extractVariableReferences(
                            paramsMatch[ 1 ],
                            isVariableDefinition,
                            sourceVariable
                        );
                        extractedVariables.push(...innerVars);
                    }
                }
            }

            // 规则6: 处理包含算术或逻辑运算的复杂表达式
            // 这里不需要特殊处理，因为前面的正则已经可以捕获到单独的变量引用

            // 检查变量是否被用作数组索引
            const arrayIndexMatches = expression.match(/\b(\w+)\s*\[\s*#(\w+)/g);
            if (arrayIndexMatches) {
                for (const match of arrayIndexMatches) {
                    const indexVar = match.match(/#(\w+)/)?.[ 1 ];
                    if (indexVar) {
                        variablesUsedAsIndex.add(indexVar);
                    }
                }
            }

        } catch (error) {
            console.error('提取变量引用时出错:', error, '表达式:', expression);
        }

        return [ ...new Set(extractedVariables) ]; // 去重
    }

    /**
     * 递归确定变量是否被使用
     * @param varName 要检查的变量名
     * @returns 变量是否被使用
     */
    function isVariableUsed(varName: string): boolean {
        // 如果已确认要保留的变量，直接返回true
        if (variablesToKeep.has(varName)) {
            return true;
        }

        // 直接使用的变量
        if (directlyUsedVariables.has(varName)) {
            variablesToKeep.add(varName); // 缓存结果
            return true;
        }

        // 检查是否有对该变量数组元素的直接使用
        for (const usedVar of directlyUsedVariables) {
            if (usedVar.startsWith(`${varName}[`)) {
                variablesToKeep.add(varName); // 缓存结果
                return true;
            }
        }

        // 防止循环依赖
        if (visitingVariables.has(varName)) {
            return false;
        }

        // 标记当前正在访问此变量，用于检测循环依赖
        visitingVariables.add(varName);

        // 检查变量是否被用作数组索引
        if (variablesUsedAsIndex.has(varName)) {
            variablesToKeep.add(varName); // 缓存结果
            return true;
        }

        // 递归检查使用此变量的其他变量是否被使用
        for (const [ dependentVar, dependencies ] of variableDependencies.entries()) {
            if (dependencies.has(varName)) {
                if (isVariableUsed(dependentVar)) {
                    // 移除访问标记
                    visitingVariables.delete(varName);
                    variablesToKeep.add(varName); // 缓存结果
                    return true;
                }
            }

            // 检查是否有对该变量数组元素的间接使用
            for (const dep of dependencies) {
                if (dep.startsWith(`${varName}[`)) {
                    if (isVariableUsed(dependentVar)) {
                        // 移除访问标记
                        visitingVariables.delete(varName);
                        variablesToKeep.add(varName); // 缓存结果
                        return true;
                    }
                }
            }
        }

        // 检查此变量是否依赖其他变量（反向依赖检查）
        if (variableDependencies.has(varName)) {
            const dependencies = variableDependencies.get(varName)!;
            for (const dependency of dependencies) {
                // 提取基本变量名（去除可能的数组索引）
                const baseDepName = dependency.includes('[') ?
                    dependency.substring(0, dependency.indexOf('[')) :
                    dependency;

                // 检查依赖的变量是否被使用
                if (isVariableUsed(baseDepName)) {
                    // 如果依赖的变量被使用，那么这个变量也是被使用的
                    // 移除访问标记
                    visitingVariables.delete(varName);
                    variablesToKeep.add(varName); // 缓存结果
                    return true;
                }
            }
        }

        // 移除访问标记
        visitingVariables.delete(varName);
        return false;
    }

    /**
     * 处理变量标签中的所有属性和内容，提取变量引用
     */
    function processVariableNode(node: Element, varName: string): void {
        // 处理expression属性
        const expression = node.getAttribute('expression');
        if (expression) {
            extractVariableReferences(expression, true, varName);
        }

        // 处理values属性
        const values = node.getAttribute('values');
        if (values) {
            extractVariableReferences(values, true, varName);
        }

        // 处理所有其他属性
        const attributes = node.attributes;
        for (let i = 0; i < attributes.length; i++) {
            const attr = attributes[ i ];
            if (attr.name !== 'name' && attr.name !== 'expression' && attr.name !== 'values') {
                extractVariableReferences(attr.value, true, varName);
            }
        }

        // 处理文本内容
        if (node.textContent) {
            extractVariableReferences(node.textContent, true, varName);
        }
    }

    /**
     * 处理非变量节点中的所有属性和内容，提取变量引用
     */
    function processNonVariableNode(node: Element): void {
        // 处理所有属性
        const attributes = node.attributes;
        for (let i = 0; i < attributes.length; i++) {
            const attr = attributes[ i ];
            // 特殊处理name属性，检查是否与已定义的变量名匹配
            if (attr.name === 'name' && variableNodes.has(attr.value)) {
                // 如果name属性值与某个变量名匹配，则将该变量标记为直接使用
                directlyUsedVariables.add(attr.value);
            } else {
                extractVariableReferences(attr.value, false);
            }
        }

        // 处理文本内容
        if (node.textContent && node.childNodes.length === 1 && node.childNodes[ 0 ].nodeType === 3) {
            // 只处理直接的文本内容，不处理有子元素的情况
            extractVariableReferences(node.textContent, false);
        }
    }

    /**
     * 查找并记录所有变量节点
     */
    function findAllVariableNodes(element: Element): void {
        // 第一遍遍历：收集所有变量节点
        const collectVariableNodes = (node: Element) => {
            // 检查当前节点是否是变量节点
            if (variableTagNames.includes(node.tagName)) {
                const name = node.getAttribute('name');
                if (name) {
                    if (!variableNodes.has(name)) {
                        variableNodes.set(name, []);
                    }
                    variableNodes.get(name)?.push(node);
                }
            }

            // 递归处理子元素
            for (let i = 0; i < node.childNodes.length; i++) {
                const child = node.childNodes[ i ];
                if (child.nodeType === 1) { // 只处理元素节点 (1 = Element)
                    collectVariableNodes(child as Element);
                }
            }
        };

        // 第二遍遍历：处理变量引用和依赖关系
        const processAllNodes = (node: Element) => {
            // 检查当前节点是否是变量节点
            if (variableTagNames.includes(node.tagName)) {
                const name = node.getAttribute('name');
                if (name) {
                    // 记录此变量定义中的变量引用
                    processVariableNode(node, name);
                }
            } else {
                // 非变量节点，检查其中的变量引用
                processNonVariableNode(node);
            }

            // 递归处理子元素
            for (let i = 0; i < node.childNodes.length; i++) {
                const child = node.childNodes[ i ];
                if (child.nodeType === 1) { // 只处理元素节点 (1 = Element)
                    processAllNodes(child as Element);
                }
            }
        };

        // 先收集所有变量节点
        collectVariableNodes(element);
        // 然后处理所有节点中的变量引用
        processAllNodes(element);
    }

    try {
        // 查找所有变量节点和变量引用
        findAllVariableNodes(rootElement);

        // 第三次处理：识别引号中的变量引用
        const processQuotedVariables = (node: Element) => {
            // 检查所有属性中的引号变量引用
            const attributes = node.attributes;
            for (let i = 0; i < attributes.length; i++) {
                const attr = attributes[ i ];
                const value = attr.value;

                // 查找引号中的变量引用
                const quotedVars = value.match(/['"](\w[\w\-._]*)['"](?!\s*:)/g);
                if (quotedVars) {
                    for (const qVar of quotedVars) {
                        const varNameMatch = qVar.match(/['"](\w[\w\-._]*)['"]/);
                        if (varNameMatch && varNameMatch[ 1 ]) {
                            const varName = varNameMatch[ 1 ];
                            if (variableNodes.has(varName)) {
                                // 这是对已定义变量的引用
                                directlyUsedVariables.add(varName);
                            }
                        }
                    }
                }

                // 特别处理params属性，它可能包含逗号分隔的参数列表
                if (attr.name === 'params') {
                    const params = value.split(',');
                    for (const param of params) {
                        const trimmedParam = param.trim();
                        // 检查是否是带引号的变量名
                        const quotedVarMatch = trimmedParam.match(/^['"](\w[\w\-._]*)['"]$/);
                        if (quotedVarMatch && quotedVarMatch[ 1 ]) {
                            const varName = quotedVarMatch[ 1 ];
                            if (variableNodes.has(varName)) {
                                // 这是对已定义变量的引用
                                directlyUsedVariables.add(varName);
                            }
                        }
                    }
                }
            }

            // 处理文本内容中的引号变量引用
            if (node.textContent) {
                const text = node.textContent;
                const quotedVars = text.match(/['"](\w[\w\-._]*)['"](?!\s*:)/g);
                if (quotedVars) {
                    for (const qVar of quotedVars) {
                        const varNameMatch = qVar.match(/['"](\w[\w\-._]*)['"]/);
                        if (varNameMatch && varNameMatch[ 1 ]) {
                            const varName = varNameMatch[ 1 ];
                            if (variableNodes.has(varName)) {
                                // 这是对已定义变量的引用
                                directlyUsedVariables.add(varName);
                            }
                        }
                    }
                }
            }

            // 递归处理子元素
            for (let i = 0; i < node.childNodes.length; i++) {
                const child = node.childNodes[ i ];
                if (child.nodeType === 1) { // 只处理元素节点
                    processQuotedVariables(child as Element);
                }
            }
        };

        // 处理引号中的变量引用
        processQuotedVariables(rootElement);

        // 分析变量依赖关系，确定哪些变量需要保留
        // 使用多次迭代来解决复杂的依赖链
        let keepFindingDependencies = true;
        let iterations = 0;
        const maxIterations = 10; // 设置最大迭代次数，防止异常情况下的无限循环

        while (keepFindingDependencies && iterations < maxIterations) {
            const previousSize = variablesToKeep.size;

            // 找出所有需要保留的变量
            for (const varName of variableNodes.keys()) {
                if (isVariableUsed(varName)) {
                    variablesToKeep.add(varName);
                }
            }

            // 如果没有新增要保留的变量，则停止迭代
            keepFindingDependencies = (variablesToKeep.size > previousSize);
            iterations++;
        }

        if (iterations >= maxIterations) {
            console.warn('依赖分析达到最大迭代次数，可能存在复杂的循环依赖');
        }

        // 删除未使用的变量节点
        for (const [ varName, nodes ] of variableNodes.entries()) {
            if (!variablesToKeep.has(varName)) {

                for (const node of nodes) {
                    try {
                        // 检查是否有子标签，如果有则不删除
                        if (hasChildElements(node)) {
                            continue;
                        }

                        const parentNode = node.parentNode;
                        if (parentNode) {
                            parentNode.removeChild(node);
                        } else {
                            console.error(`变量节点 ${varName} 没有父节点，无法删除`);
                        }
                    } catch (error) {
                        console.error(`删除变量节点时出错: ${varName}`, error);
                    }
                }
            }
        }

    } catch (error) {
        console.error('删除未使用变量时发生错误:', error);
    }
}