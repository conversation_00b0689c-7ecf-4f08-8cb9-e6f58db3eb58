/**
 * 将多个VariableBinders标签合并为一个VariableBinders标签
 * @param rootElement - 根元素
 */
export function merge_variable_binders(rootElement: Element): void {
    try {
        // 获取所有VariableBinders标签
        const VariableBindersList = rootElement.getElementsByTagName('VariableBinders');

        // 如果没有或只有一个VariableBinders标签，直接返回
        if (VariableBindersList.length <= 1) {
            return;
        }

        // 使用第一个VariableBinders作为主标签
        const mainVariableBinders = VariableBindersList[ 0 ];

        // 从第二个开始遍历所有其他VariableBinders标签
        for (let i = VariableBindersList.length - 1; i > 0; i--) {
            const currentCommands = VariableBindersList[ i ];

            // 将当前标签的所有子节点移动到主标签中
            while (currentCommands.firstChild) {
                mainVariableBinders.appendChild(currentCommands.firstChild);
            }

            // 移除当前的VariableBinders标签
            currentCommands.parentNode?.removeChild(currentCommands);
        }
    } catch (error) {
        // 打印错误但不中断程序
        console.error('合并VariableBinders标签时发生错误:', error);
    }
}
