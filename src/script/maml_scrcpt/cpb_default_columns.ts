// ContentProviderBinderTransformer.ts
// 内容提供者绑定器转换器：确保<ContentProviderBinder>标签的columns属性包含所有子<Variable>标签的column属性值
// import { XMLSerializer } from 'xmldom';
// import { format_xml } from './format_xml';
/**
 * 转换所有 <ContentProviderBinder> 标签，确保它们的 columns 属性包含所有子 <Variable> 标签的 column 属性值。
 * 如果 columns 属性不存在，则创建它。如果已存在，则添加新的列，避免重复。
 * 当 variableElements 为空时，不添加或更新 columns 属性。
 * @param root - manifest.xml 的根 Element 节点
 */
export const cpb_default_columns = (root: Element): void => {
    // 查找所有 <ContentProviderBinder> 元素
    const contentProviderBinders = Array.from(root.getElementsByTagName('ContentProviderBinder')) as Element[];

    if (contentProviderBinders.length === 0) {
        return;
    }

    // const serializer = new XMLSerializer();

    contentProviderBinders.forEach(binder => {
        // 查找所有子 <Variable> 标签
        const variableElements = Array.from(binder.getElementsByTagName('Variable')) as Element[];

        // 如果 variableElements 为空，则不添加或更新 columns 属性
        if (variableElements.length === 0) {
            return;
        }

        // 获取现有的 columns 属性值
        const existingColumnsAttr = binder.getAttribute('columns');
        const existingColumns = existingColumnsAttr ? existingColumnsAttr.split(',').map(col => col.trim()) : [];
        const columnsSet = new Set(existingColumns);
        // 收集所有需要添加的 column
        variableElements.forEach(variable => {
            const column = variable.getAttribute('column');
            if (column && !columnsSet.has(column)) {
                columnsSet.add(column);
            }
        });

        // 生成更新后的 columns 字符串
        const updatedColumns = Array.from(columnsSet).join(',');

        // 检查 columns 是否发生变化
        if (existingColumnsAttr !== updatedColumns) {

            // 修改前
            // const serializer = new XMLSerializer();
            // const beforeXML = serializer.serializeToString(binder);
            // console.log(`\n\n%c修改前的<ContentProviderBinder> 片段：\n${format_xml(beforeXML)}`, 'color:#bf2c9f;');

            // 更新 columns 属性
            binder.setAttribute('columns', updatedColumns);

            // 修改后
            // const afterXML = serializer.serializeToString(binder);
            // console.log(`%c修改后的 <ContentProviderBinder> 片段：\n${format_xml(afterXML)}\n\n`, 'color: #0055ff');

        }
    });
};
