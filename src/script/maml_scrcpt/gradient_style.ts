// GradientTextAndDateTimeTransformer.ts
// 渐变文字和日期时间转换器：将<Text>和<DateTime> 和 <Image> 和 <ImageNumber> 和 <ImageChars> 和 <Time> 和 <SvgText> 标签中color属性包含多个颜色值的元素转换为渐变效果
// import { XMLSerializer } from 'xmldom';
// import { format_xml } from './format_xml';
/**
 * 生成唯一的 name 属性
 * @param existingNames - 已存在的 name 集合
 * @param baseName - 基础名称
 * @returns 唯一的名称
 */
const generateUniqueName = (existingNames: Set<string>, baseName: string = 'generatedName'): string => {
    let uniqueName = baseName;
    let counter = 1;
    while (existingNames.has(uniqueName)) {
        uniqueName = `${baseName}_${counter}`;
        counter++;
    }
    existingNames.add(uniqueName);
    return uniqueName;
};

/**
 * 保持原始颜色格式不变
 * 支持 "#ffffff", "#ffffffff", "argb(255,255,255,255)", "@xxx"
 * @param color - 原始颜色字符串
 * @returns 原始格式的颜色字符串
 */
const formatColor = (color: string): string => {
    color = color.trim();
    if (color.startsWith('#')) {
        // 直接返回原始的十六进制颜色值
        return color;
    } else if (color.toLowerCase().startsWith('argb(') && color.endsWith(')')) {
        // 保持原始的 argb 格式
        return color;
    } else if (color.startsWith('@')) {
        // 保持原始的 @xxx 格式
        return color;
    }
    console.warn(`颜色格式不正确: ${color}. 使用默认颜色#000000.`);
    return '#000000'; // 默认黑色
};

/**
 * 解析颜色字符串，判断是否包含多个颜色
 * @param colorStr - 颜色字符串
 * @returns 是否包含多个颜色
 */
const hasMultipleColors = (colorStr: string): boolean => {
    // 如果不包含逗号，肯定是单个颜色
    if (!colorStr.includes(',')) {
        return false;
    }

    // 检查是否是 argb 格式
    if (colorStr.trim().toLowerCase().startsWith('argb(')) {
        return false;
    }

    // 分割颜色字符串
    const colors = colorStr.split(',').map(c => c.trim()).filter(c => c.length > 0);
    return colors.length > 1;
};

/**
 * 解析 color 和 position 属性
 */
const parseColorsAndPositions = (colorAttr: string, positionAttr?: string): { colors: string[]; positions: string[] } => {
    // 分割并过滤空值
    const colors = colorAttr.split(',')
        .map(color => color.trim())
        .filter(color => color.length > 0)
        .map(color => formatColor(color));

    let positions: string[] = [];

    if (positionAttr) {
        positions = positionAttr.split(',').map(pos => pos.trim());
    } else {
        // 如果 position 属性不存在，根据颜色数量均分
        if (colors.length === 1) {
            positions = [ '0.00' ];
        } else {
            const step = 1 / (colors.length - 1);
            positions = colors.map((_, index) => (index * step).toFixed(2));
        }
    }

    // 确保 positions 数量与 colors 数量一致
    if (positions.length !== colors.length) {
        console.warn(`colors 和 positions 数量不一致。根据 colors 数量重新均分 positions。`);
        if (colors.length === 1) {
            positions = [ '0.00' ];
        } else {
            const step = 1 / (colors.length - 1);
            positions = colors.map((_, index) => (index * step).toFixed(2));
        }
    }

    return { colors, positions };
};

/**
 * 判断是否为图片类型的标签
 * @param tagName - 标签名称
 * @returns 是否为图片类型
 */
const isImageTag = (tagName: string): boolean => {
    return [ 'Image', 'ImageNumber', 'ImageChars', 'Time' ].includes(tagName);
};

/**
 * 转换所有 color 属性包含多个颜色值的 <Text> 和 <DateTime> 和 <Image> 和 <ImageNumber> 和 <ImageChars> 和 <Time> 和 <SvgText> 标签，将其替换为 <Group> 标签结构。
 * @param root - manifest.xml 的根 Element 节点
 */
export const gradient_style = (root: Element): void => {
    // 查找所有符合条件的元素
    const textElements = Array.from(root.getElementsByTagName('Text')) as Element[];
    const dateTimeElements = Array.from(root.getElementsByTagName('DateTime')) as Element[];
    const imageElements = Array.from(root.getElementsByTagName('Image')) as Element[];
    const ImageNumberElements = Array.from(root.getElementsByTagName('ImageNumber')) as Element[];
    const ImageCharsElements = Array.from(root.getElementsByTagName('ImageChars')) as Element[];
    const TimeElements = Array.from(root.getElementsByTagName('Time')) as Element[];
    const SvgTextElements = Array.from(root.getElementsByTagName('SvgText')) as Element[];

    const allElements = [ ...textElements, ...dateTimeElements, ...imageElements, ...ImageNumberElements, ...ImageCharsElements, ...TimeElements, ...SvgTextElements ];

    // 过滤出 color 属性包含多个颜色值的元素
    const elementsToTransform = allElements.filter(elem => {
        const colorAttr = elem.getAttribute('color');
        return colorAttr && hasMultipleColors(colorAttr);
    });

    if (elementsToTransform.length === 0) {
        return;
    }


    // 收集所有现有的 name 属性以确保生成唯一名称
    const existingNames = new Set<string>();
    Array.from(root.getElementsByTagName('*')).forEach((elem) => {
        const nameAttr = elem.getAttribute('name');
        if (nameAttr) {
            existingNames.add(nameAttr);
        }
    });

    // 初始化序号计数器
    let sequenceNumber = 1;

    // 遍历每个符合条件的元素并进行转换
    elementsToTransform.forEach((element) => {
        const tagName = element.tagName; // 'Text' 或 'DateTime' 或 'Image' 或 'ImageNumber' 或 'ImageChars' 或 'Time' 或 'SvgText'
        const isImage = isImageTag(tagName);
        const widthProp = isImage ? 'bmp_width' : 'text_width';
        const heightProp = isImage ? 'bmp_height' : 'text_height';

        // 获取或生成 name 属性
        let name = element.getAttribute('name');
        if (!name) {
            name = generateUniqueName(existingNames, `${tagName}_name_${sequenceNumber}`);
            element.setAttribute('name', name);
        }

        // 获取属性值，设置默认值或条件添加
        const x = element.getAttribute('x') || '0';
        const y = element.getAttribute('y') || '0';
        const align = element.getAttribute('align');
        const alignV = element.getAttribute('alignV');
        const alpha = element.getAttribute('alpha');
        const visibility = element.getAttribute('visibility');

        // 获取 color 和 position 属性
        const colorAttr = element.getAttribute('color')!;
        const positionAttr = element.getAttribute('position') ?? undefined;

        // 解析 color 和 position 属性
        const { colors, positions } = parseColorsAndPositions(colorAttr, positionAttr);

        // 创建 <Group> 元素
        const group = root.ownerDocument.createElement('Group');
        group.setAttribute('x', `${x}-2`);
        group.setAttribute('y', `${y}-2`);
        if (align) {
            group.setAttribute('align', align);
        }
        if (alignV) {
            group.setAttribute('alignV', alignV);
        }
        group.setAttribute('w', `#${name}.${widthProp}+4`);
        group.setAttribute('h', `#${name}.${heightProp}+4`);
        group.setAttribute('clip', 'true');
        group.setAttribute('layered', 'true');
        if (alpha) {
            group.setAttribute('alpha', alpha);
        }
        if (visibility) {
            group.setAttribute('visibility', visibility);
        }

        // 创建新的 <Text> 或 <DateTime> 或 <Image> 或 <ImageNumber> 或 <ImageChars> 或 <Time> 元素，继承原属性和内容，去掉 position, x, y, visibility, alpha
        const newElement = root.ownerDocument.createElement(tagName);
        Array.from(element.attributes).forEach(attr => {
            if (![ 'color', 'position', 'x', 'y', 'visibility', 'alpha', 'align', 'alignV' ].includes(attr.name)) {
                newElement.setAttribute(attr.name, attr.value);
            }
        });
        newElement.setAttribute('x', '2');
        newElement.setAttribute('y', '2');
        newElement.setAttribute('color', '#000000'); // 设置 color 为黑色

        // 继承嵌套内容
        while (element.firstChild) {
            newElement.appendChild(element.firstChild);
        }

        // 创建 <Rectangle> 元素
        const rectangle = root.ownerDocument.createElement('Rectangle');
        rectangle.setAttribute('x', '0');
        rectangle.setAttribute('y', '0');
        rectangle.setAttribute('w', `#${name}.${widthProp}+4`);
        rectangle.setAttribute('h', `#${name}.${heightProp}+4`);
        rectangle.setAttribute('fillColor', '#000000'); // 使用第一个原始格式的颜色
        rectangle.setAttribute('xfermodeNum', '5');

        // 创建 <FillShaders> 元素
        const fillShaders = root.ownerDocument.createElement('FillShaders');

        // 创建 <LinearGradient> 元素
        const linearGradient = root.ownerDocument.createElement('LinearGradient');
        linearGradient.setAttribute('x', '0');
        linearGradient.setAttribute('y', '0');
        linearGradient.setAttribute('x1', `#${name}.${widthProp}+4`);
        linearGradient.setAttribute('y1', '0');
        linearGradient.setAttribute('tile', 'clamp');

        // 添加 <GradientStop> 元素
        colors.forEach((colorValue, index) => {
            const gradientStop = root.ownerDocument.createElement('GradientStop');
            gradientStop.setAttribute('color', colorValue);
            gradientStop.setAttribute('position', positions[ index ] || '0');
            linearGradient.appendChild(gradientStop);
        });

        fillShaders.appendChild(linearGradient);
        rectangle.appendChild(fillShaders);
        group.appendChild(newElement);
        group.appendChild(rectangle);

        // 创建一个文档片段，将新元素插入到原标签的位置
        const fragment = root.ownerDocument.createDocumentFragment();
        fragment.appendChild(group);

        // 获取原标签的父节点
        const parent = element.parentNode;
        if (!parent) {
            console.warn(`<${tagName}> 标签没有父节点，无法替换。`);
            return;
        }

        // 替换原标签
        parent.replaceChild(fragment, element);

        // 修改前
        // const serializer = new XMLSerializer();
        // const beforeXML = serializer.serializeToString(element);
        // console.log(`\n\n%c修改前的<Text 和 DateTime 和 Image 和 ImageNumber 和 ImageChars 和 Time 和 SvgText> 片段：\n${format_xml(beforeXML)}`, 'color:#bf2c9f;');

        // 修改后
        // const afterXML = serializer.serializeToString(group);
        // console.log(`%c修改后的 <Text 和 DateTime 和 Image 和 ImageNumber 和 ImageChars 和 Time 和 SvgText> 片段：\n${format_xml(afterXML)}\n\n`, 'color: #0055ff');

        // 增加序号计数器
        sequenceNumber++;
    });
};
