/**
 * 将ArcProgressBar标签转换为进度条标签
 * @param rootElement XML文档中的根元素节点
 */
export function arc_progress_bar(rootElement: Element): void {
    try {
        // 获取所有ArcProgressBar标签并转换为数组
        const bars = Array.from(rootElement.getElementsByTagName('ArcProgressBar'));

        if (bars.length === 0) {
            return;
        }

        // 使用第一个bar的document
        const doc = bars[ 0 ].ownerDocument;
        const firstParent = bars[ 0 ].parentNode;
        if (!firstParent) {
            console.error('找不到第一个进度条的父节点');
            return;
        }

        // 创建共享元素的名称
        const sharedNames = {
            function: 'fra120_shared',
            config: 'config_progress_shared',
        };

        // 检查是否已存在共享元素，如果不存在则创建
        const functionExists = checkIfElementExists(rootElement, 'Function', sharedNames.function);
        const configExists = checkIfElementExists(rootElement, 'FolmeConfig', sharedNames.config);
        const fraExists = checkIfElementExists(rootElement, 'FramerateController', 'fra');

        // 如果需要共享元素，则创建它们
        if (!functionExists || !configExists || !fraExists) {
            // 创建共享元素（只有Function和FolmeConfig）
            const sharedElements = createSharedElements(doc, sharedNames, {
                createFunction: !functionExists,
                createConfig: !configExists,
                createFra: !fraExists
            });

            // 将共享元素插入到第一个进度条的父元素之前
            firstParent.insertBefore(sharedElements, bars[ 0 ]);
        }

        // 处理每个ArcProgressBar标签
        bars.forEach((bar) => {
            try {
                // 获取父节点
                const parentNode = bar.parentNode;
                if (!parentNode) {
                    console.error('找不到父节点');
                    return;
                }

                // 生成随机字符串用于命名
                const randomString = bar.getAttribute('id') || Math.random().toString(36).substring(2, 8);

                // 必要属性列表
                const requiredAttrs = [ 'x', 'y', 'radius', 'startAngle', 'sweep', 'close', 'progress', 'strokeColor', 'padding', 'weight', 'cap', 'alpha' ];

                // 获取所有属性
                const attrs = {
                    x: bar.getAttribute('x') || '0',
                    y: bar.getAttribute('y') || '0',
                    radius: parseInt(bar.getAttribute('radius') || '200', 10),
                    startAngle: bar.getAttribute('startAngle') || '0',
                    sweep: bar.getAttribute('sweep') || '360',
                    close: bar.getAttribute('close') || 'false',
                    progress: bar.getAttribute('progress') || '0',
                    strokeColor: (() => {
                        const colors = (bar.getAttribute('strokeColor') || '#ffffff,#277af7').split(',');
                        // 如果只有一个颜色，则背景使用白色，进度条使用那个颜色
                        return colors.length === 1 ? [ '#ffffff', colors[ 0 ] ] : colors;
                    })(),
                    padding: parseInt(bar.getAttribute('padding') || '5', 10),
                    weight: (() => {
                        const weights = (bar.getAttribute('weight') || '20,20').split(',').map(w => parseInt(w, 10));
                        // 如果只有一个宽度值，则背景和进度条使用相同的宽度
                        return weights.length === 1 ? [ weights[ 0 ], weights[ 0 ] ] : weights;
                    })(),
                    cap: bar.getAttribute('cap') || 'round',
                    alpha: (() => {
                        const alphas = (bar.getAttribute('alpha') || '255,255').split(',');
                        // 如果只有一个透明度值，则背景透明度为该值的0.3倍，进度条使用原透明度
                        if (alphas.length === 1) {
                            const alpha = parseInt(alphas[ 0 ], 10);
                            return [ Math.floor(alpha * 0.3).toString(), alphas[ 0 ] ];
                        }
                        return alphas;
                    })()
                };

                // 使用文档片段收集所有新元素
                const fragment = bar.ownerDocument.createDocumentFragment();

                // 创建 VirtualElement
                const virtualElement = bar.ownerDocument.createElement('VirtualElement');
                virtualElement.setAttribute('name', `${randomString}`);
                virtualElement.setAttribute('float1', '0');
                virtualElement.setAttribute('folmeMode', 'true');
                fragment.appendChild(virtualElement);

                // 创建 FolmeState 元素（每个进度条自己的）
                const folmeState = bar.ownerDocument.createElement('FolmeState');
                folmeState.setAttribute('name', `progress_${randomString}`);
                folmeState.setAttribute('float1', `${attrs.sweep} * ${attrs.progress}`);
                fragment.appendChild(folmeState);

                // 创建 Var 元素（使用共享的FolmeConfig）
                const varElement = bar.ownerDocument.createElement('Var');
                varElement.setAttribute('name', `var_progress_${randomString}`);
                varElement.setAttribute('expression', attrs.progress);
                varElement.setAttribute('type', 'string');

                const trigger = bar.ownerDocument.createElement('Trigger');
                const folmeCommand = bar.ownerDocument.createElement('FolmeCommand');
                folmeCommand.setAttribute('target', `${randomString}`);
                folmeCommand.setAttribute('states', `'progress_${randomString}'`);
                folmeCommand.setAttribute('command', 'to');
                folmeCommand.setAttribute('config', `'${sharedNames.config}'`);  // 使用共享的FolmeConfig

                trigger.appendChild(folmeCommand);
                varElement.appendChild(trigger);
                fragment.appendChild(varElement);

                // 获取 FillShaders 和 StrokeShaders 标签
                const fillShaders = Array.from(bar.getElementsByTagName('FillShaders'));
                const strokeShaders = Array.from(bar.getElementsByTagName('StrokeShaders'));

                // 创建背景 Arc
                const backgroundArc = bar.ownerDocument.createElement('Arc');
                // 设置必要属性
                backgroundArc.setAttribute('x', attrs.x);
                backgroundArc.setAttribute('y', attrs.y);
                backgroundArc.setAttribute('w', `${(attrs.radius * 2).toString()}`);
                backgroundArc.setAttribute('h', `${(attrs.radius * 2).toString()}`);
                backgroundArc.setAttribute('startAngle', `${-90} + ${attrs.startAngle}`);
                backgroundArc.setAttribute('sweep', attrs.sweep);
                backgroundArc.setAttribute('close', attrs.close);
                backgroundArc.setAttribute('strokeColor', attrs.strokeColor[ 0 ]);
                backgroundArc.setAttribute('weight', attrs.weight[ 0 ].toString());
                backgroundArc.setAttribute('cap', attrs.cap);
                backgroundArc.setAttribute('alpha', attrs.alpha[ 0 ]);

                // 继承其他属性
                Array.from(bar.attributes).forEach(attr => {
                    if (!requiredAttrs.includes(attr.name)) {
                        backgroundArc.setAttribute(attr.name, attr.value);
                    }
                });

                // 处理背景的 FillShaders 和 StrokeShaders
                fillShaders.forEach(shader => {
                    if (shader.getAttribute('tag') === '0') {
                        const shaderClone = shader.cloneNode(true) as Element;
                        shaderClone.removeAttribute('tag');
                        backgroundArc.appendChild(shaderClone);
                    }
                });
                strokeShaders.forEach(shader => {
                    if (shader.getAttribute('tag') === '0') {
                        const shaderClone = shader.cloneNode(true) as Element;
                        shaderClone.removeAttribute('tag');
                        backgroundArc.appendChild(shaderClone);
                    }
                });
                fragment.appendChild(backgroundArc);

                // 创建进度 Arc
                const progressArc = bar.ownerDocument.createElement('Arc');
                // 设置必要属性
                progressArc.setAttribute('x', attrs.x);
                progressArc.setAttribute('y', attrs.y);
                progressArc.setAttribute('w', `${(attrs.radius * 2).toString()}`);
                progressArc.setAttribute('h', `${(attrs.radius * 2).toString()}`);
                progressArc.setAttribute('startAngle', `${-90} + ${attrs.startAngle}`);
                progressArc.setAttribute('sweep', `#${randomString}.float1`);
                progressArc.setAttribute('close', attrs.close);
                progressArc.setAttribute('strokeColor', attrs.strokeColor[ 1 ]);
                const progressWeight = Math.max(2, attrs.weight[ 1 ] - attrs.padding * 2);
                progressArc.setAttribute('weight', progressWeight.toString());
                progressArc.setAttribute('cap', attrs.cap);
                progressArc.setAttribute('alpha', attrs.alpha[ 1 ]);

                // 继承其他属性
                Array.from(bar.attributes).forEach(attr => {
                    if (!requiredAttrs.includes(attr.name)) {
                        progressArc.setAttribute(attr.name, attr.value);
                    }
                });

                // 处理进度条的 FillShaders 和 StrokeShaders
                fillShaders.forEach(shader => {
                    if (shader.getAttribute('tag') === '1') {
                        const shaderClone = shader.cloneNode(true) as Element;
                        shaderClone.removeAttribute('tag');
                        progressArc.appendChild(shaderClone);
                    }
                });
                strokeShaders.forEach(shader => {
                    if (shader.getAttribute('tag') === '1') {
                        const shaderClone = shader.cloneNode(true) as Element;
                        shaderClone.removeAttribute('tag');
                        progressArc.appendChild(shaderClone);
                    }
                });
                fragment.appendChild(progressArc);

                // 替换原始节点
                parentNode.insertBefore(fragment, bar);
                parentNode.removeChild(bar);

            } catch (error) {
                console.error('处理单个ArcProgressBar标签时发生错误:', error);
            }
        });
    } catch (error) {
        console.error('处理ArcProgressBar标签时发生错误:', error);
    }
}

/**
 * 检查文档中是否已存在特定类型和名称的元素
 */
function checkIfElementExists(rootElement: Element, tagName: string, name: string): boolean {
    try {
        const elements = Array.from(rootElement.getElementsByTagName(tagName));
        return elements.some(element => element.getAttribute('name') === name);
    } catch (error) {
        console.error(`检查元素 ${tagName} ${name} 是否存在时发生错误:`, error);
        return false;
    }
}

/**
 * 创建共享元素：只包含Function和FolmeConfig，根据需要选择性创建
 */
function createSharedElements(
    doc: Document,
    names: { function: string, config: string },
    options: { createFunction: boolean, createConfig: boolean, createFra: boolean }
): DocumentFragment {
    const fragment = doc.createDocumentFragment();

    // 创建共享的Function元素
    if (options.createFunction) {
        const functionElement = doc.createElement('Function');
        functionElement.setAttribute('name', names.function);
        const animationCommand = doc.createElement('AnimationCommand');
        animationCommand.setAttribute('target', 'fra');
        animationCommand.setAttribute('command', 'play');
        functionElement.appendChild(animationCommand);
        fragment.appendChild(functionElement);
    }

    // 创建共享的FramerateController元素
    if (options.createFra) {
        const framerateController = doc.createElement('FramerateController');
        framerateController.setAttribute('name', 'fra');
        framerateController.setAttribute('initPause', 'true');
        framerateController.setAttribute('loop', 'false');

        const controlPoint1 = doc.createElement('ControlPoint');
        controlPoint1.setAttribute('frameRate', '120');
        controlPoint1.setAttribute('time', '0');

        const controlPoint2 = doc.createElement('ControlPoint');
        controlPoint2.setAttribute('frameRate', '120');
        controlPoint2.setAttribute('time', '1000');

        const controlPoint3 = doc.createElement('ControlPoint');
        controlPoint3.setAttribute('frameRate', '0');
        controlPoint3.setAttribute('time', '1500');

        framerateController.appendChild(controlPoint1);
        framerateController.appendChild(controlPoint2);
        framerateController.appendChild(controlPoint3);
        fragment.appendChild(framerateController);
    }

    // 创建共享的FolmeConfig元素
    if (options.createConfig) {
        const folmeConfig = doc.createElement('FolmeConfig');
        folmeConfig.setAttribute('name', names.config);
        folmeConfig.setAttribute('ease', '-2,1,0.5');
        folmeConfig.setAttribute('onBegin', `'${names.function}'`);
        folmeConfig.setAttribute('onUpdate', `'${names.function}'`);
        folmeConfig.setAttribute('onEnd', `'${names.function}'`);
        fragment.appendChild(folmeConfig);
    }

    return fragment;
}
