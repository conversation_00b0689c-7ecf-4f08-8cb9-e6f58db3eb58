import xmlFormatter from 'xml-formatter';  // 使用默认导入

/**
 * 格式化 XML 字符串，如果格式化失败，则尝试补全根节点后再次格式化
 * @param xmlString - 需要格式化的 XML 字符串
 * @returns 格式化后的 XML 字符串，如果格式化失败则返回 null
 */
function format_xml(xmlString: string): string | null {
    try {
        // 如果 XML 存在多个根节点，添加一个新的根节点
        if ((xmlString.match(/<[^>]+>/g) || []).length > 1 && !xmlString.includes('<root>')) {
            xmlString = `<root>${xmlString}</root>`;
        }

        // 尝试直接格式化
        return xmlFormatter(xmlString);
    } catch (error) {
        console.log('格式化错误，尝试补全根节点：', (error as Error).message);

        // 如果发生错误，补全根节点后再次尝试格式化
        if (!xmlString.trim().startsWith('<')) {
            xmlString = '<root>' + xmlString + '</root>';  // 添加默认根节点
        }

        try {
            return xmlFormatter(xmlString);  // 再次尝试格式化
        } catch (fallbackError) {
            console.log('格式化失败：', (fallbackError as Error).message);
            return null;
        }
    }
}

export { format_xml };  // 命名导出函数
