// 消息类型定义
type MessageType = 'success' | 'error' | 'warning' | 'loading';

// 消息队列
const messageQueue: { type: MessageType, content: string, duration: number }[] = [];

// 当前是否有消息显示
let isShowingMessage = false;
// 定时器
let messageTimer: number | null = null;
// 是否正在处理消息
let isProcessingMessage = false;
// loading开始时间
let loadingStartTime: number | null = null;
// 消息动画状态
const messageAnimationState = {
    isAnimating: false,
    animationEndTime: 0
};

// DOM观察器，用于确保SVG图标存在
let messageObserver: MutationObserver | null = null;

// 用于调整SVG元素样式的辅助函数
function adjustSvgElement(svg: SVGElement) {
    svg.style.width = '100%';
    svg.style.height = '100%';
    svg.style.minWidth = '20px';
    svg.style.minHeight = '20px';
    // 保持纵横比
    svg.style.aspectRatio = '1 / 1';
    // 确保SVG内部元素可见
    svg.style.overflow = 'visible';
    // 调整视口大小，确保图标在小尺寸下也能清晰显示
    svg.setAttribute('preserveAspectRatio', 'xMidYMid meet');
}

// SVG图标的内联数据
const SVG_ICONS = {
    success: `<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_528_830)">
<path d="M99.9857 199.971C119.761 199.971 139.092 194.107 155.535 183.121C171.977 172.134 184.793 156.519 192.36 138.249C199.928 119.979 201.908 99.8748 198.05 80.4795C194.192 61.0842 184.67 43.2684 170.686 29.2852C156.703 15.3019 138.887 5.77921 119.492 1.92124C100.097 -1.93673 79.9928 0.0433229 61.7228 7.61101C43.4528 15.1787 27.8372 27.9941 16.8506 44.4367C5.86406 60.8792 -3.86292e-08 80.2105 0 99.9858C5.18001e-08 126.504 10.5342 151.935 29.2851 170.686C48.0361 189.437 73.4679 199.971 99.9857 199.971ZM99.9857 177.76C84.6068 177.76 69.5732 173.2 56.7859 164.656C43.9986 156.113 34.0318 143.969 28.1459 129.761C22.26 115.553 20.7193 99.9187 23.7185 84.835C26.7177 69.7514 34.1223 55.8958 44.9958 45.0202C55.8694 34.1447 69.7236 26.7376 84.8067 23.7356C99.8898 20.7336 115.524 22.2715 129.733 28.1548C143.942 34.0381 156.088 44.0026 164.634 56.7883C173.18 69.5741 177.743 84.6068 177.746 99.9858C177.75 110.2 175.741 120.315 171.835 129.752C167.929 139.19 162.202 147.765 154.981 154.989C147.76 162.213 139.186 167.943 129.75 171.853C120.314 175.762 110.2 177.775 99.9857 177.775V177.76Z" fill="#28C840"/>
<path d="M153.071 77.2337L89.9214 140.383C88.4437 141.86 86.0476 141.86 84.5698 140.383L47.1083 102.921C45.6306 101.444 45.6306 99.0476 47.1083 97.5698L58.8819 85.7966C60.3597 84.3185 62.7558 84.3188 64.2335 85.7966L87.2458 108.808L135.946 60.1083C137.424 58.6306 139.819 58.6306 141.298 60.1083L153.071 71.8819C154.548 73.3597 154.549 75.756 153.071 77.2337Z" fill="#28C840"/>
</g>
<defs>
<clipPath id="clip0_528_830">
<rect width="200" height="200" fill="white"/>
</clipPath>
</defs>
</svg>`,
    error: `<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_528_823)">
<path d="M99.9857 199.971C119.761 199.971 139.092 194.107 155.535 183.121C171.977 172.134 184.793 156.519 192.36 138.249C199.928 119.979 201.908 99.8748 198.05 80.4795C194.192 61.0842 184.67 43.2684 170.686 29.2852C156.703 15.3019 138.887 5.77921 119.492 1.92124C100.097 -1.93673 79.9928 0.0433229 61.7228 7.61101C43.4528 15.1787 27.8372 27.9941 16.8506 44.4367C5.86406 60.8792 -3.86292e-08 80.2105 0 99.9858C5.18001e-08 126.504 10.5342 151.935 29.2851 170.686C48.0361 189.437 73.4679 199.971 99.9857 199.971ZM99.9857 177.76C84.6068 177.76 69.5732 173.2 56.7859 164.656C43.9986 156.113 34.0318 143.969 28.1459 129.761C22.26 115.553 20.7193 99.9187 23.7185 84.835C26.7177 69.7514 34.1223 55.8958 44.9958 45.0202C55.8694 34.1447 69.7236 26.7376 84.8067 23.7356C99.8898 20.7336 115.524 22.2715 129.733 28.1548C143.942 34.0381 156.088 44.0026 164.634 56.7883C173.18 69.5741 177.743 84.6068 177.746 99.9858C177.75 110.2 175.741 120.315 171.835 129.752C167.929 139.19 162.202 147.765 154.981 154.989C147.76 162.213 139.186 167.943 129.75 171.853C120.314 175.762 110.2 177.775 99.9857 177.775V177.76Z" fill="#FF5F57"/>
<path d="M140.008 124.583C141.337 125.915 141.337 128.071 140.008 129.403L129.403 140.008C128.071 141.337 125.916 141.34 124.584 140.008L100.002 115.427L75.4214 140.008C74.0894 141.337 71.9312 141.337 70.6019 140.008L59.9968 129.403C58.6677 128.074 58.6677 125.915 59.9968 124.583L84.5808 100.003L59.9968 75.4215C58.6677 74.0895 58.6677 71.9311 59.9968 70.602L70.6019 59.9965C71.9339 58.6678 74.0896 58.6678 75.4214 59.9965L100.003 84.5811L124.584 59.9967C125.916 58.6678 128.071 58.6678 129.403 59.9967L140.008 70.602C141.337 71.9311 141.337 74.0895 140.008 75.4215L115.427 100.002L140.008 124.583Z" fill="#FF5F57"/>
</g>
<defs>
<clipPath id="clip0_528_823">
<rect width="200" height="200" fill="white"/>
</clipPath>
</defs>
</svg>`,
    warning: `<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_528_833)">
<path d="M99.9857 199.971C119.761 199.971 139.092 194.107 155.535 183.121C171.977 172.134 184.793 156.519 192.36 138.249C199.928 119.979 201.908 99.8748 198.05 80.4795C194.192 61.0842 184.67 43.2684 170.686 29.2852C156.703 15.3019 138.887 5.77921 119.492 1.92124C100.097 -1.93673 79.9928 0.0433229 61.7228 7.61101C43.4528 15.1787 27.8372 27.9941 16.8506 44.4367C5.86406 60.8792 -3.86292e-08 80.2105 0 99.9858C5.18001e-08 126.504 10.5342 151.935 29.2851 170.686C48.0361 189.437 73.4679 199.971 99.9857 199.971ZM99.9857 177.76C84.6068 177.76 69.5732 173.2 56.7859 164.656C43.9986 156.113 34.0318 143.969 28.1459 129.761C22.26 115.553 20.7193 99.9187 23.7185 84.835C26.7177 69.7514 34.1223 55.8958 44.9958 45.0202C55.8694 34.1447 69.7236 26.7376 84.8067 23.7356C99.8898 20.7336 115.524 22.2715 129.733 28.1548C143.942 34.0381 156.088 44.0026 164.634 56.7883C173.18 69.5741 177.743 84.6068 177.746 99.9858C177.75 110.2 175.741 120.315 171.835 129.752C167.929 139.19 162.202 147.765 154.981 154.989C147.76 162.213 139.186 167.943 129.75 171.853C120.314 175.762 110.2 177.775 99.9857 177.775V177.76Z" fill="#FEBC30"/>
<path d="M87 54C87 51.7909 88.7909 50 91 50H108C110.209 50 112 51.7909 112 54V113C112 115.209 110.209 117 108 117H91C88.7909 117 87 115.209 87 113V54Z" fill="#FEBC30"/>
<path d="M87 130C87 127.791 88.7909 126 91 126H108C110.209 126 112 127.791 112 130V147C112 149.209 110.209 151 108 151H91C88.7909 151 87 149.209 87 147V130Z" fill="#FEBC30"/>
</g>
<defs>
<clipPath id="clip0_528_833">
<rect width="200" height="200" fill="white"/>
</clipPath>
</defs>
</svg>`,
    loading: `<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_528_836)">
<path d="M99.9857 199.971C119.761 199.971 139.092 194.107 155.535 183.121C171.977 172.134 184.793 156.519 192.36 138.249C199.928 119.979 201.908 99.8748 198.05 80.4795C194.192 61.0842 184.67 43.2684 170.686 29.2852C156.703 15.3019 138.887 5.77921 119.492 1.92124C100.097 -1.93673 79.9928 0.0433229 61.7228 7.61101C43.4528 15.1787 27.8372 27.9941 16.8506 44.4367C5.86406 60.8792 -3.86292e-08 80.2105 0 99.9858C5.18001e-08 126.504 10.5342 151.935 29.2851 170.686C48.0361 189.437 73.4679 199.971 99.9857 199.971ZM99.9857 177.76C84.6068 177.76 69.5732 173.2 56.7859 164.656C43.9986 156.113 34.0318 143.969 28.1459 129.761C22.26 115.553 20.7193 99.9187 23.7185 84.835C26.7177 69.7514 34.1223 55.8958 44.9958 45.0202C55.8694 34.1447 69.7236 26.7376 84.8067 23.7356C99.8898 20.7336 115.524 22.2715 129.733 28.1548C143.942 34.0381 156.088 44.0026 164.634 56.7883C173.18 69.5741 177.743 84.6068 177.746 99.9858C177.75 110.2 175.741 120.315 171.835 129.752C167.929 139.19 162.202 147.765 154.981 154.989C147.76 162.213 139.186 167.943 129.75 171.853C120.314 175.762 110.2 177.775 99.9857 177.775V177.76Z" fill="#0D84FF" fill-opacity="0.2"/>
<path d="M199.971 100.029C199.971 73.5108 189.437 48.0791 170.686 29.3281C151.935 10.5771 126.504 0.0429688 99.9857 0.0429688C97.0385 0.0429687 94.2119 1.21377 92.1279 3.2978C90.0438 5.38184 88.873 8.2084 88.873 11.1557C88.873 14.1029 90.0438 16.9295 92.1279 19.0135C94.2119 21.0976 97.0385 22.2684 99.9857 22.2684C120.609 22.2684 140.388 30.4609 154.971 45.0438C169.553 59.6267 177.746 79.4054 177.746 100.029C177.746 102.976 178.917 105.802 181.001 107.886C183.085 109.969 185.912 111.14 188.859 111.14C191.806 111.14 194.632 109.969 196.716 107.886C198.8 105.802 199.971 102.976 199.971 100.029Z" fill="#0D84FF"/>
</g>
<defs>
<clipPath id="clip0_528_836">
<rect width="200" height="200" fill="white"/>
</clipPath>
</defs>
</svg>`
};

// 初始化DOM观察器
function initMessageObserver() {
    // 如果已经初始化过，不重复初始化
    if (messageObserver) return;

    try {
        // 创建观察器实例
        messageObserver = new MutationObserver((mutations) => {
            // 遍历所有变化
            mutations.forEach((mutation) => {
                // 检查是否是节点移除
                if (mutation.type === 'childList' && mutation.removedNodes.length > 0) {
                    const messageContainer = document.getElementById('message-container');
                    if (!messageContainer || !isShowingMessage) return;

                    // 检查图标容器是否存在
                    const iconContainer = messageContainer.querySelector('div[style*="flex-shrink"]');
                    if (!iconContainer) return;

                    // 检查图标容器是否有SVG子节点
                    const svgElement = iconContainer.querySelector('svg');
                    if (!svgElement && isShowingMessage) {
                        console.warn('检测到SVG图标丢失，尝试恢复...');

                        // 找到消息类型的容器样式
                        let messageType: MessageType = 'success';
                        if (messageContainer.style.backgroundColor.includes('255, 95, 87')) {
                            messageType = 'error';
                        } else if (messageContainer.style.backgroundColor.includes('254, 188, 46')) {
                            messageType = 'warning';
                        } else if (messageContainer.style.backgroundColor.includes('10, 132, 255')) {
                            messageType = 'loading';
                        }

                        // 恢复图标
                        iconContainer.innerHTML = SVG_ICONS[ messageType ];

                        // 调整SVG尺寸
                        const newSvgElement = iconContainer.querySelector('svg');
                        if (newSvgElement) {
                            adjustSvgElement(newSvgElement as SVGElement);

                            // 如果是loading类型，需要恢复旋转动画
                            if (messageType === 'loading') {
                                newSvgElement.style.animation = 'rotate 1s linear infinite';
                            }
                        }
                    }
                }
            });
        });

        // 配置观察选项
        const config = { childList: true, subtree: true };

        // 开始观察文档
        messageObserver.observe(document.body, config);

    } catch (error) {
        console.error('初始化消息观察器失败:', error);
    }
}

// 处理消息队列
async function processMessageQueue() {
    if (isProcessingMessage || messageQueue.length === 0) {
        return;
    }

    isProcessingMessage = true;
    const message = messageQueue.shift(); // 取出队列中的第一条消息

    if (!message) {
        isProcessingMessage = false;
        return;
    }

    // 如果当前有动画正在进行，等待动画完成
    if (messageAnimationState.isAnimating) {
        const now = Date.now();
        if (now < messageAnimationState.animationEndTime) {
            await new Promise(resolve => setTimeout(resolve, messageAnimationState.animationEndTime - now));
        }
    }

    try {
        await createMessage(message.type, message.content, message.duration);
    } catch (error) {
        console.error('处理消息失败:', error);
    }

    isProcessingMessage = false;

    // 继续处理下一个消息
    if (messageQueue.length > 0) {
        processMessageQueue();
    }
}

// 创建消息提示
async function createMessage(type: MessageType, content: string, duration: number = 3000) {
    // 使用 Unicode 空格替换普通空格以保留多个空格
    content = content.replace(/ /g, '\u00A0');

    // 确保观察器已初始化
    initMessageObserver();

    console.log('createMessage', content);
    return new Promise<void>((resolve) => {
        const handleMessage = async () => {
            try {
                const messageContainer = document.getElementById('message-container');

                if (!messageContainer) {
                    console.error('未找到消息容器元素');
                    resolve();
                    return;
                }

                // 清除现有的定时器
                if (messageTimer) {
                    clearTimeout(messageTimer);
                    messageTimer = null;
                }

                // 设置背景和边框颜色
                setMessageStyles(messageContainer, type);

                // 如果当前正在显示消息，立即隐藏
                if (isShowingMessage) {
                    await hideCurrentMessage(messageContainer);
                }

                // 清空消息容器
                messageContainer.innerHTML = '';

                // 创建图标容器
                const iconContainer = document.createElement('div');
                iconContainer.style.cssText = `
                    flex-shrink: 0;
                    width: 20px;
                    height: 20px;
                    margin-right: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    overflow: visible;
                    position: relative;
                `;

                // 使用内联SVG替代图片元素
                switch (type) {
                    case 'success':
                        iconContainer.innerHTML = SVG_ICONS.success;
                        iconContainer.style.filter = 'drop-shadow(0 0 10px #28c840cc)';
                        adjustSvgSize(iconContainer);
                        break;
                    case 'error':
                        iconContainer.innerHTML = SVG_ICONS.error;
                        iconContainer.style.filter = 'drop-shadow(0 0 10px #ff5f57cc)';
                        adjustSvgSize(iconContainer);
                        break;
                    case 'warning':
                        iconContainer.innerHTML = SVG_ICONS.warning;
                        iconContainer.style.filter = 'drop-shadow(0 0 10px #febc2ecc)';
                        adjustSvgSize(iconContainer);
                        break;
                    case 'loading':
                        iconContainer.innerHTML = SVG_ICONS.loading;
                        iconContainer.style.filter = 'drop-shadow(0 0 10px #0a84ff)';

                        // 添加旋转动画
                        const style = document.createElement('style');
                        style.textContent = `
                            @keyframes rotate {
                                from { transform: rotate(0deg); }
                                to { transform: rotate(360deg); }
                            }
                        `;
                        document.head.appendChild(style);

                        // 获取SVG元素并添加动画
                        const svgElement = iconContainer.querySelector('svg');
                        if (svgElement) {
                            svgElement.style.animation = 'rotate 1s linear infinite';
                            adjustSvgElement(svgElement as SVGElement);
                        }

                        loadingStartTime = Date.now();
                        break;
                }

                // 调整SVG尺寸以适应容器
                function adjustSvgSize(container: HTMLElement) {
                    const svg = container.querySelector('svg');
                    if (svg) {
                        adjustSvgElement(svg as SVGElement);
                    }
                }

                // 创建文本容器
                const textContainer = document.createElement('div');
                textContainer.className = 'text-container';
                textContainer.style.cssText = `
                    overflow: hidden;
                    position: relative;
                    white-space: nowrap;
                    width: auto;
                    max-width: calc(100% - 24px);
                    transition: width 0.3s ease-in-out;
                    display: flex;
                    align-items: center;
                `;

                // 创建文本元素
                const textElement = document.createElement('div');
                textElement.className = 'message-text';
                textElement.textContent = content;
                textElement.style.cssText = `
                    display: block;
                    white-space: nowrap;
                    position: relative;
                    color: inherit;
                    z-index: 1;
                    opacity: 0;
                    transform: scale(0.8);
                    transition: all 0.3s ease-in-out;
                `;

                // 如果是loading类型，添加持续时间显示
                let durationElement: HTMLDivElement | null = null;
                if (type === 'loading') {
                    durationElement = document.createElement('div');
                    durationElement.style.cssText = `
                        margin-left: 5px;
                        color: #0a84ff;
                        font-size: 12px;
                        text-align: right;
                        font-weight: 500;
                        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                        filter: drop-shadow(0 0 10px #0a84ff7f);
                    `;
                    durationElement.textContent = ' 0.00 秒';

                    // 更新持续时间
                    const updateDuration = () => {
                        if (loadingStartTime && durationElement && isShowingMessage) {
                            const durationSec = ((Date.now() - loadingStartTime) / 1000).toFixed(2);
                            durationElement.textContent = ` ${durationSec} 秒`;
                            requestAnimationFrame(updateDuration);
                        }
                    };
                    requestAnimationFrame(updateDuration);
                }

                // 添加元素到容器
                textContainer.appendChild(textElement);
                messageContainer.appendChild(iconContainer);
                messageContainer.appendChild(textContainer);
                if (durationElement) {
                    messageContainer.appendChild(durationElement);
                }

                // 设置初始位置
                messageContainer.style.transform = 'translateX(-50%) translateY(-100%)';
                messageContainer.style.opacity = '0';

                // 显示消息
                messageContainer.classList.add('show');
                isShowingMessage = true;

                // 执行动画
                await animateMessageDisplay(messageContainer);

                // 检查是否需要滚动
                checkAndStartScroll(textElement, duration);

                resolve();
            } catch (error) {
                console.error('显示消息失败:', error);
                messageAnimationState.isAnimating = false;
            }
            resolve();
        };
        handleMessage();
    });
}

function setMessageStyles(messageContainer: HTMLElement, type: MessageType) {
    // 添加基础样式
    messageContainer.style.backdropFilter = 'blur(8px)';
    messageContainer.style.transition = 'all 0.3s ease-in-out, backdrop-filter 0.3s ease-in-out';

    // 根据消息类型设置不同的背景色和边框
    switch (type) {
        case 'success':
            messageContainer.style.backgroundColor = 'rgba(40, 200, 64, 0.05)';
            messageContainer.style.border = '1px solid rgba(40, 200, 64, 0.2)';
            break;
        case 'error':
            messageContainer.style.backgroundColor = 'rgba(255, 95, 87, 0.05)';
            messageContainer.style.border = '1px solid rgba(255, 95, 87, 0.2)';
            break;
        case 'warning':
            messageContainer.style.backgroundColor = 'rgba(254, 188, 46, 0.05)';
            messageContainer.style.border = '1px solid rgba(254, 188, 46, 0.2)';
            break;
        case 'loading':
            messageContainer.style.backgroundColor = 'rgba(10, 132, 255, 0.05)';
            messageContainer.style.border = '1px solid rgba(10, 132, 255, 0.2)';
            break;
    }
}

function hideCurrentMessage(messageContainer: HTMLElement): Promise<void> {
    return new Promise(resolve => {
        messageAnimationState.isAnimating = true;
        messageAnimationState.animationEndTime = Date.now() + 200;
        messageContainer.style.transition = 'all 0.2s ease-in-out';
        messageContainer.style.transform = 'translateX(-50%) translateY(-100%)';
        messageContainer.style.opacity = '0';
        setTimeout(() => {
            messageAnimationState.isAnimating = false;
            isShowingMessage = false;
            resolve();
        }, 200);
    });
}

async function animateMessageDisplay(messageContainer: HTMLElement): Promise<void> {
    return new Promise(resolve => {
        requestAnimationFrame(() => {
            messageAnimationState.isAnimating = true;
            messageAnimationState.animationEndTime = Date.now() + 300;

            const textElement = messageContainer.querySelector('.message-text') as HTMLElement;
            const textContainer = messageContainer.querySelector('.text-container') as HTMLElement;
            textContainer.style.width = 'auto'; // 让宽度自适应

            messageContainer.style.transition = 'all 0.3s ease-in-out';
            messageContainer.style.transform = 'translateX(-50%) translateY(0)';
            messageContainer.style.opacity = '1';
            textElement.style.opacity = '1';
            textElement.style.transform = 'scale(1)';

            // 确保 DOM 更新完成后再测量
            requestAnimationFrame(() => {
                messageAnimationState.isAnimating = false;
                resolve();
            });
        });
    });
}


// 检查并开始文本滚动
function checkAndStartScroll(textElement: HTMLElement, duration: number = 5000) {
    // 使用 scrollWidth 和 clientWidth 判断是否溢出
    const parent = textElement.parentElement;
    if (!parent) return;

    const parentWidth = parent.clientWidth;
    const scrollWidth = textElement.scrollWidth;

    const tolerance = 1;
    if (scrollWidth > parentWidth + tolerance) {
        // 需要滚动
        const gapChars = 6; // 6个字符的间隔
        const fontSize = parseFloat(getComputedStyle(textElement).fontSize || '16'); // 获取字体大小
        const averageCharWidth = fontSize * 0.6; // 估算单个字符的宽度（根据字体不同可能需要调整）
        const gapWidth = averageCharWidth * gapChars; // 计算间隔宽度

        const scrollDistance = scrollWidth + gapWidth;

        // 创建或获取已有的滚动动画样式
        let scrollStyle = document.getElementById('text-scroll-style') as HTMLStyleElement;
        if (!scrollStyle) {
            scrollStyle = document.createElement('style');
            scrollStyle.id = 'text-scroll-style';
            document.head.appendChild(scrollStyle);
        }
        scrollStyle.textContent = `
            @keyframes textScroll {
                0% { transform: translateX(0); }
                100% { transform: translateX(-${scrollDistance}px); }
            }
        `;

        // 复制文本内容并添加间隔
        const duplicateText = textElement.cloneNode(true) as HTMLElement;
        duplicateText.style.position = 'absolute';
        duplicateText.style.left = `${scrollWidth + gapWidth}px`;
        duplicateText.style.whiteSpace = 'nowrap';
        duplicateText.style.color = 'inherit';
        duplicateText.textContent = textElement.textContent; // 复制文本

        // 将复制的文本添加到父容器
        parent.appendChild(duplicateText);
        parent.style.position = 'relative';
        parent.style.display = 'flex';
        parent.style.alignItems = 'center';
        parent.style.overflow = 'hidden';

        // 设置动画
        textElement.style.animation = `textScroll ${scrollDistance / 60}s linear infinite`; // scrollSpeed = 60px/s
        duplicateText.style.animation = `textScroll ${scrollDistance / 60}s linear infinite`;

        // 计算动画持续时间
        const scrollDuration = (scrollDistance / 60) * 1000; // 60px/s

        // 存储清理函数，在消息关闭时调用
        const cleanup = () => {
            if (scrollStyle) {
                try {
                    scrollStyle.remove();
                } catch (e) {
                    console.warn('移除滚动样式失败:', e);
                }
            }
            textElement.style.animation = ''; // 清除动画
            duplicateText.style.animation = ''; // 清除动画
            try {
                duplicateText.remove(); // 移除复制的文本
            } catch (e) {
                console.warn('移除复制文本元素失败:', e);
            }
        };

        // 设置消息自动关闭的时间为滚动持续时间
        if (duration === 0) {
            // 如果 duration 为0，表示不自动关闭，需要手动关闭
            // 可根据需求调整，此处不设置定时器
        } else {
            messageTimer = window.setTimeout(() => {
                const messageContainer = document.getElementById('message-container');
                if (messageContainer) {
                    hideCurrentMessage(messageContainer).then(() => {
                        cleanup();
                        isShowingMessage = false;
                        messageTimer = null;
                        processMessageQueue();
                    });
                }
            }, scrollDuration);
        }
    } else {
        // 不需要滚动，正常显示
        if (duration > 0) {
            messageTimer = window.setTimeout(() => {
                const messageContainer = document.getElementById('message-container');
                if (messageContainer) {
                    hideCurrentMessage(messageContainer).then(() => {
                        isShowingMessage = false;
                        messageTimer = null;
                        processMessageQueue();
                    });
                }
            }, duration);
        } else {
            // duration 为0，表示不自动关闭，需要手动关闭
            // 可根据需求调整，此处不设置定时器
        }
    }
}


// 导出消息方法
/**
 * 显示成功消息
 * @param content 消息内容
 * @param duration 消息显示时间,默认3000ms
 */
export function messageSuccess(content: string, duration: number = 3000) {
    enqueueMessage({ type: 'success', content, duration });
}

/**
 * 显示错误消息
 * @param content 消息内容
 * @param duration 消息显示时间,默认3000ms
 */
export function messageError(content: string, duration: number = 3000) {
    enqueueMessage({ type: 'error', content, duration });
}

/**
 * 显示警告消息
 * @param content 消息内容
 * @param duration 消息显示时间,默认3000ms
 */
export function messageWarning(content: string, duration: number = 3000) {
    enqueueMessage({ type: 'warning', content, duration });
}

/**
 * 显示加载中消息
 * @param content 消息内容
 * @param duration 消息显示时间,默认0表示不自动关闭
 */
export function messageLoading(content: string, duration: number = 0) {
    enqueueMessage({ type: 'loading', content, duration });
}

/**
 * 将消息添加到队列
 * @param message 消息对象
 */
function enqueueMessage(message: { type: MessageType, content: string, duration: number }) {
    messageQueue.push(message);
    // 确保观察器已初始化
    initMessageObserver();
    processMessageQueue();
}

/**
 * 关闭loading消息
 */
export async function closeLoading() {
    const messageContainer = document.getElementById('message-container');
    if (messageContainer && isShowingMessage) {

        await hideCurrentMessage(messageContainer);

        // 清空最新消息和loading开始时间
        // latestMessage = null; // 已移除，不再使用 latestMessage
        isProcessingMessage = false;
        loadingStartTime = null;

        // 继续处理队列中的下一个消息
        processMessageQueue();
    }
}

// 在页面加载时初始化观察器
if (typeof window !== 'undefined') {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initMessageObserver);
    } else {
        initMessageObserver();
    }
}