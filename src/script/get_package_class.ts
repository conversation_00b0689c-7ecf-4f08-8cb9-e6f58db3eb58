import { messageSuccess, messageError, messageLoading } from './prompt_message';
import { adb_all } from './commands';
import { invoke } from '@tauri-apps/api/core';

declare global {
    interface Window {
        获取包名: () => Promise<string>;
        获取包名类名: () => Promise<string>;
        获取icon: () => Promise<string>;
    }
}

/**
 * 提取设备数据
 * @param command 执行的adb命令
 * @param matchPattern 匹配的正则表达式
 * @param resultTemplate 结果模板函数
 * @param loadingMessage 加载消息
 * @param errorMessage 错误消息
 * @returns 返回提取结果
 */
async function extractDeviceData(command: string, matchPattern: RegExp, resultTemplate: (packageName: string, className: string) => string, loadingMessage: string, errorMessage: string): Promise<string> {
    let textToCopy = ''; // 用于保存所有结果

    function stringExtract(deviceId: string, stdout: string) {
        try {
            // 首先尝试标准格式的正则匹配
            const match = stdout.match(matchPattern);
            if (match) {
                const packageName = match[ 1 ];
                const className = match[ 2 ] || ""; // 确保className有值
                // 使用模板构建结果字符串
                const resultText = resultTemplate(packageName, className);
                textToCopy += resultText;
            } else {
                // 检查是否是特殊系统界面
                const specialMatch = stdout.match(/{.*?\s+(\w+)}/);
                if (specialMatch && specialMatch[ 1 ] === 'NotificationShade') {
                    // 通知栏打开时，默认使用系统界面
                    const packageName = 'com.android.systemui';
                    const className = 'com.android.systemui.notification.NotificationShade';
                    const resultText = resultTemplate(packageName, className);
                    textToCopy += resultText;
                } else {
                    console.warn(`设备 ${deviceId} 未找到匹配的信息`);
                }
            }
        } catch (err) {
            console.error(`设备 ${deviceId} 解析失败:`, err);
        }
    }

    try {
        messageLoading(loadingMessage);

        // 获取所有设备的当前活动窗口信息
        const deviceResults = await adb_all(command.split(' '), true);

        // 遍历处理每个设备的结果
        for (const [ deviceId, result ] of Object.entries(deviceResults)) {
            if (typeof result === 'string') {
                stringExtract(deviceId, result);
            }
        }

        if (!textToCopy) {
            throw (errorMessage);
        }

        // 复制到剪贴板
        await invoke('拷贝到粘贴板', { text: textToCopy });

        messageSuccess(`已复制所有设备的相关信息到剪贴板`);

        return textToCopy.trim();
    } catch (error) {
        messageError(`${error}`);
        return '';
    }
}

/**
 * 获取包名
 * @returns 返回提取结果packageName
 */
export async function 获取包名() {
    return await extractDeviceData(
        'shell dumpsys window | grep mCurrentFocus',
        /{.*?\s+([\w.]+)\/([^}]*)/,  // 更新正则表达式，捕获类名(可能为空)
        (packageName) => `${packageName}\n`,
        '正在获取包名',
        '检查设备是否连接'
    );
}

/**
 * 获取包名类名
 * @returns 返回提取结果<IntentCommand package="${packageName}" class="${className}" />
 */
export async function 获取包名类名() {
    return await extractDeviceData(
        'shell dumpsys window | grep mCurrentFocus',
        /{.*?\s+([\w.]+)\/([^}]+)/,
        (packageName, className) => `<IntentCommand package="${packageName}" class="${className}" />\n`,
        '正在获取包名类名',
        '检查设备是否连接'
    );
}

/**
 * 获取 icon
 * @returns 返回提取结果<Image name="AppIcon" srcType="ApplicationIcon" srcExp="'${packageName},${className}'"/>
 */
export async function 获取icon() {
    return await extractDeviceData(
        'shell dumpsys window | grep mCurrentFocus',
        /{.*?\s+([\w.]+)\/([^}]+)/,
        (packageName, className) => `<Image name="AppIcon" srcType="ApplicationIcon" srcExp="'${packageName},${className}'"/>\n`,
        '正在获取图标',
        '检查设备是否连接'
    );
}

// 将函数暴露给全局
window.获取包名 = 获取包名;
window.获取包名类名 = 获取包名类名;
window.获取icon = 获取icon;
