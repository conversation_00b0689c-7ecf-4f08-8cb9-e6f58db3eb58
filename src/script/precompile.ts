import { invoke } from "@tauri-apps/api/core";
import { listen } from '@tauri-apps/api/event';

interface ProgressEvent {
    processed: number;
    total: number;
    progress: number;
}

/**
 * 执行预编译命令
 */
export async function 预编译(inputPath: string) {
    try {

        const has9PatchFiles = await invoke<boolean>('检查9patch文件', {
            inputPath
        });

        if (!has9PatchFiles) {
            return {};
        }

        const exportButton = document.querySelector('.button_max') as HTMLButtonElement;
        if (!exportButton) return;

        const originalText = exportButton.textContent;

        // 只有在有点9图时才设置初始样式
        exportButton.style.backgroundColor = '#0002';
        exportButton.style.color = '#fff';
        exportButton.style.textShadow = '0 0 10px #0005';
        exportButton.classList.add('progressing');

        // 监听进度事件
        const unlisten = await listen<ProgressEvent>('progress', ({ payload }) => {
            const { processed, total, progress } = payload;
            requestAnimationFrame(() => {
                exportButton.style.setProperty('--progress', `${(progress * 100) - 100}%`);
                exportButton.textContent = `预编译点9图 ${processed}/${total}`;
            });

            if (processed === total) {
                setTimeout(() => {
                    exportButton.classList.remove('progressing');
                    exportButton.style.removeProperty('--progress');
                    exportButton.style.backgroundColor = '#4169e1';
                    exportButton.style.color = '#fff';
                    exportButton.style.textShadow = 'none';
                    exportButton.textContent = originalText || '导出 MTZ';
                    // 1秒后恢复原始样式
                    setTimeout(() => {
                        exportButton.style.backgroundColor = '#4169e1';
                        exportButton.style.color = '#fff';
                        exportButton.style.textShadow = 'none';
                    }, 500);
                }, 500);
                unlisten();
            }
        });

        const result = await invoke<Record<string, Uint8Array>>('预编译', {
            inputPath
        });
        return result;
    } catch (error) {
        // 尝试解析返回的错误JSON
        const errorStr = error instanceof Error ? error.message : String(error);
        const errorObj = JSON.parse(errorStr);

        // 直接输出错误对象
        console.error('预编译过程发生错误:', errorObj);

        return {};
    }
}
