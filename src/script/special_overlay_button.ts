/**
 * 特殊覆盖按钮功能
 * 在窗口右上角添加一个按钮，连续点击5次后在窗口上覆盖一层页面
 */

import { adb, getDevices } from './commands';
import { messageSuccess, messageLoading, messageWarning } from './prompt_message';

// 特殊按钮状态管理
let clickCount = 0;
let isOverlayVisible = false;

/**
 * 检查设备并选择设备（如果有多个设备）- 专用于切换环境功能的小型弹窗
 * @param envName 要切换到的环境名称
 * @param envType 环境类型：'dev' | 'preview' | '4qa' | 'production'
 * @returns 选中的设备对象，如果失败或取消则返回null
 */
async function checkAndSelectDevice(envName: string, envType: string): Promise<any | null> {
    try {
        messageLoading('正在加载设备列表...', 3000);
        const devices = await getDevices();
        const onlineDevices = devices.filter(device => device.status === 'device');

        if (onlineDevices.length === 0) {
            messageWarning('没有设备连接，请连接设备后重试');
            return null;
        }

        // 无论是单设备还是多设备，都显示设备选择对话框以便用户进行环境切换
        await showCompactDeviceSelectionDialog(onlineDevices, envName, envType);
        return null; // 不再返回选中的设备，因为环境切换在对话框内完成

    } catch (error) {
        messageWarning(`设备检查失败: ${error}`);
        return null;
    }
}

/**
 * 检查设备是否拥有Root权限
 * @param deviceId 设备ID
 * @returns 如果有Root权限则返回true，否则返回false
 */
async function checkDeviceRootStatus(deviceId: string): Promise<boolean> {
    try {
        // 尝试重启adbd获取root权限。对于无root的设备，这步通常会失败或返回特定信息。
        const rootResult = await adb([ '-s', deviceId, 'root' ]);

        // 如果明确返回无法以root身份运行，则直接判断为无root
        if (rootResult.includes('adbd cannot run as root') || rootResult.includes('production builds')) {
            console.log(`设备 ${deviceId} 无Root权限 (根据adb root输出判断)`);
            return false;
        }

        // 再次验证：尝试访问需要root权限的目录。这是最可靠的验证方法。
        // 即使`adb root`命令没有返回明确的错误，在某些生产版本的设备上它也可能静默失败。
        await adb([ '-s', deviceId, 'shell', 'ls', '/data/system/' ]);
        console.log(`设备 ${deviceId} 已获取Root权限`);
        return true;
    } catch (error) {
        const errorStr = String(error);
        // 如果错误信息包含 "Permission denied"，则确定无root权限
        if (errorStr.includes('Permission denied')) {
            console.log(`设备 ${deviceId} 无Root权限 (访问/data/system/被拒绝)`);
            return false;
        }
        // 对于其他错误，出于安全考虑，也判断为无root权限
        console.warn(`检查设备 ${deviceId} Root权限时发生未知错误，判断为无权限:`, errorStr);
        return false;
    }
}

/**
 * 获取设备的当前主题环境
 * @param deviceId 设备ID
 * @returns 环境类型字符串 ('dev', 'preview', '4qa', 'production')
 */
async function getDeviceEnvironment(deviceId: string): Promise<string> {
    const environments = {
        dev: '/data/system/theme_dev',
        preview: '/data/system/theme_preview',
        '4qa': '/data/system/theme_4qa',
    };

    const existingEnvs = [];

    // 检查所有环境文件是否存在
    for (const [ env, path ] of Object.entries(environments)) {
        try {
            // adb() 函数现已修复，如果文件不存在 (ls 返回非0退出码)，它会正确地抛出异常。
            await adb([ '-s', deviceId, 'shell', 'ls', path ]);
            // 如果代码执行到这里，说明文件存在
            existingEnvs.push(env);
            console.log(`设备 ${deviceId} 发现环境文件: ${env} (${path})`);
        } catch (error) {
            // 捕获到错误意味着文件不存在，这是预期的行为，所以我们在这里什么都不做。
        }
    }

    // 如果存在多个环境文件，这是异常情况，需要警告
    if (existingEnvs.length > 1) {
        console.warn(`设备 ${deviceId} 存在多个环境文件: ${existingEnvs.join(', ')} - 这可能导致环境检测不准确，将使用第一个检测到的环境`);
    }

    // 如果有环境文件存在，返回第一个（但这种情况应该只有一个）
    if (existingEnvs.length > 0) {
        const detectedEnv = existingEnvs[ 0 ];
        console.log(`设备 ${deviceId} 最终检测环境: ${detectedEnv}`);
        return detectedEnv;
    }

    console.log(`设备 ${deviceId} 处于 production 环境（无环境标识文件）`);
    return 'production'; // 如果所有环境文件都不存在，则默认为正式环境
}

/**
 * 执行环境切换的核心逻辑
 * @param device 目标设备
 * @param envType 环境类型：'dev' | 'preview' | '4qa' | 'production'
 * @param envTagContainer 环境标签容器，用于更新显示
 */
async function performEnvironmentSwitch(device: any, envType: string, envTagContainer: HTMLElement): Promise<void> {
    const envNames = {
        'dev': '开发环境',
        'preview': '预发环境',
        '4qa': '测试环境',
        'production': '正式环境'
    };

    const envName = envNames[ envType as keyof typeof envNames ] || envType;

    try {
        messageLoading(`正在切换到${envName}...`);
        console.log(`开始切换设备 ${device.id} 到${envName}`);

        // 1. 执行 adb root 并检查是否成功获取权限
        try {
            const rootResult = await adb([ '-s', device.id, 'root' ]);
            console.log('adb root 命令执行结果:', rootResult);

            if (rootResult.includes('adbd cannot run as root') ||
                rootResult.includes('production builds') ||
                rootResult.includes('adbd is already running as root')) {
                if (rootResult.includes('adbd cannot run as root') || rootResult.includes('production builds')) {
                    messageWarning('没有权限执行该操作');
                    return;
                }
            }
        } catch (rootError) {
            const errorStr = String(rootError);
            console.error('adb root 命令执行失败:', errorStr);
            if (errorStr.includes('no devices/emulators found') || errorStr.includes('device not found')) {
                messageWarning('没有设备连接，请连接设备后重试');
                return;
            }
        }

        // 2. 验证root权限是否真正生效
        try {
            await adb([ '-s', device.id, 'shell', 'ls', '/data/system/' ]);
        } catch (verifyError) {
            const errorStr = String(verifyError);
            if (errorStr.includes('Permission denied')) {
                messageWarning('没有权限执行该操作');
                return;
            }
        }

        // 3. 删除所有环境标识文件
        const removeCommands = [
            { cmd: [ '-s', device.id, 'shell', 'rm', '/data/system/theme_dev' ], name: '开发环境标识文件' },
            { cmd: [ '-s', device.id, 'shell', 'rm', '/data/system/theme_preview' ], name: '预发环境标识文件' },
            { cmd: [ '-s', device.id, 'shell', 'rm', '/data/system/theme_4qa' ], name: '测试环境标识文件' }
        ];

        console.log('开始清理所有环境标识文件...');
        for (const { cmd, name } of removeCommands) {
            try {
                await adb(cmd);
                console.log(`成功删除 ${name}`);
            } catch (error) {
                console.log(`删除 ${name} 时出错: ${error}，但继续执行`);
            }
        }

        // 4. 根据环境类型创建对应的标识文件
        if (envType !== 'production') {
            const touchFile = `/data/system/theme_${envType}`;
            const envDisplayName = envNames[ envType as keyof typeof envNames ];

            console.log(`创建${envDisplayName}标识文件: ${touchFile}`);
            try {
                await adb([ '-s', device.id, 'shell', 'touch', touchFile ]);
                console.log(`成功创建${envDisplayName}标识文件`);

                // 验证文件是否真的创建成功
                await adb([ '-s', device.id, 'shell', 'ls', touchFile ]);
                console.log(`验证确认: ${envDisplayName}标识文件创建成功`);
            } catch (error) {
                console.error(`创建${envDisplayName}标识文件失败: ${error}`);
                messageWarning(`创建${envDisplayName}标识文件失败: ${error}`);
                return;
            }
        } else {
            console.log('切换到正式环境，无需创建标识文件');
        }

        // 5. 强制停止主题管理器以使环境切换生效
        await adb([ '-s', device.id, 'shell', 'am', 'force-stop', 'com.android.thememanager' ]);

        // 6. 等待一小段时间确保环境切换完全生效
        await new Promise(resolve => setTimeout(resolve, 500));

        // 7. 更新设备列表中的环境显示
        updateDeviceEnvironmentDisplay(envTagContainer, envType);

        messageSuccess(`成功切换到${envName}！`);

    } catch (error) {
        messageWarning(`切换到${envName}失败: ${error}`);
    }
}

/**
 * 更新设备环境显示
 * @param envTagContainer 环境标签容器
 * @param newEnvType 新的环境类型
 */
function updateDeviceEnvironmentDisplay(envTagContainer: HTMLElement, newEnvType: string): void {
    const envTag = envTagContainer.querySelector('.device-env-tag') as HTMLElement;
    if (!envTag) return;

    // 清除所有环境样式类
    envTag.classList.remove('dev', 'preview', 'qa4', 'production');

    // 根据新环境设置样式和文本
    switch (newEnvType) {
        case 'dev':
            envTag.classList.add('dev');
            envTag.textContent = '开发(dev)';
            break;
        case 'preview':
            envTag.classList.add('preview');
            envTag.textContent = '预发(preview)';
            break;
        case '4qa':
            envTag.classList.add('qa4');
            envTag.textContent = '测试(4qa)';
            break;
        case 'production':
            envTag.classList.add('production');
            envTag.textContent = '正式';
            break;
        default:
            envTag.classList.add('production');
            envTag.textContent = '正式';
    }
}

/**
 * 显示紧凑型设备选择对话框 - 专门为切换环境功能设计的小尺寸弹窗
 * @param devices 可用设备列表
 * @param envName 要切换到的环境名称，用于显示在标题中
 * @param envType 环境类型：'dev' | 'preview' | '4qa' | 'production'
 * @returns 用户选择的设备，如果取消则返回null
 */
async function showCompactDeviceSelectionDialog(devices: any[], envName: string, envType: string): Promise<void> {
    // 1. 首先异步获取所有设备的详细信息和Root状态
    const detailedDevicesPromises = devices.map(async (device) => {
        try {
            // 并行获取多个设备属性和root状态
            const [ brand, model, marketName, productName, hasRoot ] = await Promise.all([
                adb([ '-s', device.id, 'shell', 'getprop', 'ro.product.brand' ]).catch(() => '未知'),
                adb([ '-s', device.id, 'shell', 'getprop', 'ro.product.model' ]).catch(() => device.model || '未知设备'),
                adb([ '-s', device.id, 'shell', 'getprop', 'ro.product.marketname' ]).catch(() => ''),
                adb([ '-s', device.id, 'shell', 'getprop', 'ro.product.name' ]).catch(() => ''),
                checkDeviceRootStatus(device.id)
            ]);

            // 整理并确定最终显示的名称
            const brandName = brand.trim();
            const modelName = model.trim();
            const marketNameProp = marketName.trim();
            const productNameProp = productName.trim();
            let finalDisplayName = '';

            // 优先级：市场名称 > 品牌+型号 > 产品名称 > 基本型号
            if (marketNameProp) {
                finalDisplayName = marketNameProp;
            } else if (brandName && brandName !== '未知' && modelName) {
                finalDisplayName = `${brandName} ${modelName}`;
            } else if (productNameProp) {
                finalDisplayName = productNameProp;
            } else {
                finalDisplayName = modelName || '未知设备';
            }

            // 如果设备有Root权限，则进一步获取当前环境
            let environment: string | null = null;
            if (hasRoot) {
                environment = await getDeviceEnvironment(device.id);
            }

            return { ...device, finalDisplayName, hasRoot, environment };

        } catch (error) {
            console.warn(`获取设备 ${device.id} 详细信息失败:`, error);
            // 如果失败，则使用基本型号作为备用，并假定无Root权限
            return { ...device, finalDisplayName: device.model || '未知设备', hasRoot: false, environment: null };
        }
    });

    // 等待所有设备信息都获取完毕
    const detailedDevices = await Promise.all(detailedDevicesPromises);

    // 2. 信息获取完毕后，再创建并显示对话框
    const modal = document.createElement('div');
    modal.className = 'special-overlay';

    const dialog = document.createElement('div');
    dialog.className = 'special-button-container';
    dialog.onclick = (e) => e.stopPropagation();

    const title = document.createElement('div');
    title.textContent = `选择设备切换至${envName}`;
    title.className = 'overlay-title';

    dialog.appendChild(title);

    detailedDevices.forEach((device) => {
        const deviceItem = document.createElement('div');
        deviceItem.className = 'device-item';

        // 如果设备没有Root权限，添加disabled类
        if (!device.hasRoot) {
            deviceItem.classList.add('disabled');
        }

        // 创建左侧信息容器（设备名 + ID）
        const leftInfoContainer = document.createElement('div');
        leftInfoContainer.className = 'device-main-info';

        // 设备名称（显示完整名称）
        const deviceNameSpan = document.createElement('span');
        deviceNameSpan.className = 'device-name-simple';
        const displayName = device.finalDisplayName;

        // 设备ID
        const deviceIdSpan = document.createElement('span');
        deviceIdSpan.className = 'device-id-simple';
        const deviceIdSuffix = device.id.slice(-4);

        // 组合显示：设备名 ID
        deviceNameSpan.textContent = `${displayName} ${deviceIdSuffix}`;
        leftInfoContainer.appendChild(deviceNameSpan);

        // 创建右侧信息容器（环境）
        const rightInfoContainer = document.createElement('div');
        rightInfoContainer.className = 'device-secondary-info';

        if (!device.hasRoot) {
            // 无权限设备：显示无权限标签
            const noPermissionTag = document.createElement('span');
            noPermissionTag.className = 'device-env-tag no-permission';
            noPermissionTag.textContent = '无权限';
            rightInfoContainer.appendChild(noPermissionTag);
        } else {
            // 有权限设备：显示环境标签
            const envTag = document.createElement('span');
            envTag.className = 'device-env-tag';

            // 根据环境设置不同的样式类和显示文本（包含英文标识）
            switch (device.environment) {
                case 'dev':
                    envTag.classList.add('dev');
                    envTag.textContent = '开发(dev)';
                    break;
                case 'preview':
                    envTag.classList.add('preview');
                    envTag.textContent = '预发(preview)';
                    break;
                case '4qa':
                    envTag.classList.add('qa4');
                    envTag.textContent = '测试(4qa)';
                    break;
                case 'production':
                    envTag.classList.add('production');
                    envTag.textContent = '正式';
                    break;
                default:
                    envTag.classList.add('production');
                    envTag.textContent = '正式';
            }

            rightInfoContainer.appendChild(envTag);
        }

        // 组装设备项目
        deviceItem.appendChild(leftInfoContainer);
        deviceItem.appendChild(rightInfoContainer);

        // 添加点击事件（只有有权限的设备才能点击）
        if (device.hasRoot) {
            deviceItem.addEventListener('click', async () => {
                try {
                    // 直接在此处执行环境切换逻辑
                    await performEnvironmentSwitch(device, envType, rightInfoContainer);
                } catch (error) {
                    console.error('环境切换失败:', error);
                    messageWarning(`环境切换失败: ${error}`);
                }
            });
        }

        dialog.appendChild(deviceItem);
    });

    const cancelButton = document.createElement('button');
    cancelButton.textContent = '取消';
    cancelButton.className = 'overlay-button close-button';
    cancelButton.onclick = () => {
        cleanup();
    };
    dialog.appendChild(cancelButton);

    // 清理函数
    const cleanup = () => {
        document.removeEventListener('keydown', handleKeyDown);
        if (modal.parentNode === document.body) {
            document.body.removeChild(modal);
        }
    };

    // 处理 ESC 键
    const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
            cleanup();
        }
    };

    modal.appendChild(dialog);
    document.body.appendChild(modal);
    document.addEventListener('keydown', handleKeyDown);
}

/**
 * 启动主题环境切换流程
 * @param envType 环境类型：'dev' | 'preview' | '4qa' | 'production'
 */
async function switchThemeEnvironment(envType: string): Promise<void> {
    const envNames = {
        'dev': '开发环境',
        'preview': '预发环境',
        '4qa': '测试环境',
        'production': '正式环境'
    };

    const envName = envNames[ envType as keyof typeof envNames ] || envType;

    try {
        // 检查并显示设备选择对话框，环境切换逻辑在设备选择对话框内部处理
        await checkAndSelectDevice(envName, envType);
    } catch (error) {
        messageWarning(`启动环境切换失败: ${error}`);
    }
}

// 创建按钮元素
function createSpecialButton(): HTMLElement {
    const button = document.createElement('div');
    button.className = 'special-overlay-btn';
    // 点击事件处理
    button.addEventListener('click', handleButtonClick);

    return button;
}

// 按钮点击处理函数
function handleButtonClick(): void {
    if (isOverlayVisible) {
        console.log('覆盖层已显示，忽略点击');
        return;
    }

    clickCount++;

    // 连续点击5次后显示覆盖层
    if (clickCount >= 5) {
        showOverlay();
        clickCount = 0;
    }
}

// 显示覆盖层
function showOverlay(): void {
    if (isOverlayVisible) {
        return;
    }

    const overlay = document.createElement('div');
    overlay.className = 'special-overlay';

    const buttonContainer = document.createElement('div');
    buttonContainer.className = 'special-button-container';
    buttonContainer.onclick = (e) => e.stopPropagation();

    const title = document.createElement('div');
    title.textContent = '切换主题商店环境';
    title.className = 'overlay-title';
    buttonContainer.appendChild(title);

    const buttonLabels = [ '开发环境(dev)', '预发环境(preview)', '测试环境(4qa)', '正式环境' ];
    const envTypes = [ 'dev', 'preview', '4qa', 'production' ];

    buttonLabels.forEach((label, index) => {
        const button = document.createElement('button');
        button.className = 'overlay-button';
        button.textContent = label;
        button.onclick = () => switchThemeEnvironment(envTypes[ index ]);
        buttonContainer.appendChild(button);
    });

    const closeButton = document.createElement('button');
    closeButton.className = 'overlay-button close-button';
    closeButton.textContent = '关闭页面';
    closeButton.onclick = hideOverlay;
    buttonContainer.appendChild(closeButton);

    overlay.appendChild(buttonContainer);
    document.body.appendChild(overlay);
    isOverlayVisible = true;
    console.log('覆盖层已显示');
}

// 隐藏覆盖层
function hideOverlay(): void {
    const overlay = document.querySelector('.special-overlay');
    if (overlay) {
        overlay.remove();
        isOverlayVisible = false;
        console.log('覆盖层已隐藏');
    }
}

// 初始化
function initSpecialButton(): void {
    // 确保页面加载完成后再创建按钮
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            const button = createSpecialButton();
            document.body.appendChild(button);


        });
    } else {
        const button = createSpecialButton();
        document.body.appendChild(button);


    }
}

// 启动功能
initSpecialButton();