import { join, appLocalDataDir } from '@tauri-apps/api/path';

export async function get_the_root_directory_path(dirName: string): Promise<string> {
    try {
        // 获取应用程序的本地数据目录
        const baseDir = await appLocalDataDir();
        // 拼接目标目录路径
        const targetDir = await join(baseDir, dirName);
        return targetDir;
    } catch (error) {
        console.error('获取应用目录失败:', error);
        throw error;
    }
}
