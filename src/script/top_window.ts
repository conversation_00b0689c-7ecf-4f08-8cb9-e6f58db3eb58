// 声明全局类型
declare global {
    interface Window {
        置顶: () => void;
    }
}

import { messageSuccess, messageError } from './prompt_message';
import { Window } from '@tauri-apps/api/window'
import { Store } from '@tauri-apps/plugin-store';

const appWindow = Window.getCurrent();
let store: Store;

// 变量窗口状态
let winTop = false;

// 窗口置顶
async function 置顶(): Promise<void> {
    try {
        if (winTop) {
            await appWindow.setAlwaysOnTop(false);
            await store.set('winTop', false);
            messageSuccess('已取消窗口置顶');
            winTop = false;
        } else {
            await appWindow.setAlwaysOnTop(true);
            await store.set('winTop', true);
            messageSuccess('窗口置顶成功');
            winTop = true;
        }
        updatePinButtonStyle(winTop);
    } catch (err: unknown) {
        const errorMessage = err instanceof Error ? err.message : String(err);
        messageError(`窗口置顶操作失败: ${errorMessage}`);
        console.error('窗口置顶操作失败:', err);
    }
}

// 更新按钮样式
function updatePinButtonStyle(isPinned: boolean): void {
    try {
        const pinButton = document.getElementById('pin-window');
        if (pinButton) {
            // 设置按钮背景
            pinButton.style.background = isPinned ? '#4169e1' : 'transparent';
            pinButton.style.border = isPinned ? 'none' : '';

            // 设置SVG图标颜色
            const svgIcon = pinButton.querySelector('.svgIcon') as HTMLElement;
            if (svgIcon) {
                if (isPinned) {
                    svgIcon.style.filter = 'invert(1) brightness(1.5)';
                    svgIcon.style.opacity = '1';
                } else {
                    svgIcon.style.removeProperty('filter');
                    svgIcon.style.opacity = '';
                }
            }
        }
    } catch (err) {
        console.error('更新按钮样式失败:', err);
    }
}

// 初始化
(async () => {
    try {
        // 初始化存储
        store = await Store.load('.settings.dat');
        // 读取上次保存的置顶状态
        winTop = await store.get('winTop') || false;
        // 设置初始窗口状态
        await appWindow.setAlwaysOnTop(winTop);
        updatePinButtonStyle(winTop);
        // 将函数暴露给全局
        window.置顶 = 置顶;
    } catch (error) {
        console.error('模块初始化失败:', error);
        messageError(`模块初始化失败: ${error}`);
    }
})();