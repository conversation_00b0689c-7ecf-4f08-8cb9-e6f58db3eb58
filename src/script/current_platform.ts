import { platform } from '@tauri-apps/plugin-os';

// 声明全局变量
declare global {
    interface Window {
        platform: string;
    }
}

// 初始化平台信息
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // 获取平台信息
        window.platform = platform();
    } catch (error) {
        console.error('获取平台信息失败:', error);
        // 设置一个默认值以避免程序崩溃
        window.platform = 'unknown';
    }
})
