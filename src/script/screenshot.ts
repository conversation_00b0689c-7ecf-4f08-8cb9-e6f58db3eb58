import { messageSuccess, messageError, messageLoading } from './prompt_message';
import { adb_all } from './commands';
import { desktopDir, join } from '@tauri-apps/api/path';
import { mkdir, exists } from "@tauri-apps/plugin-fs";

declare global {
    interface Window {
        截屏: (outpath?: string) => Promise<string[] | undefined>;
    }
}

/**
 * 确保目录存在
 * @param dirPath 需要确认存在的目录路径
 * @returns 成功返回true，失败返回false
 */
async function ensureDirectoryExists(dirPath: string): Promise<boolean> {
    try {
        // 检查目录是否存在
        const dirExists = await exists(dirPath);
        if (!dirExists) {
            // 如果不存在则创建，使用recursive确保父目录也会被创建
            await mkdir(dirPath, { recursive: true });
            console.log(`成功创建目录: ${dirPath}`);
        }
        return true;
    } catch (error) {
        console.error(`创建目录失败: ${dirPath}`, error);
        messageError(`创建目录失败: ${dirPath} - ${error}`);
        return false;
    }
}

/**
 * 截屏
 * @returns 无返回值,截屏结果通过消息提示显示
 */
export default async function 截屏(outpath?: string): Promise<string[] | undefined> {
    try {
        messageLoading('正在截屏...');

        // 1. 在所有设备上执行截图命令
        const tempScreenshotFile = '/sdcard/.miniEditorScreenCap.0';
        const screencapResult = await adb_all([ 'shell', 'screencap', '-p', tempScreenshotFile ], true);

        const deviceIds = Object.keys(screencapResult);

        if (deviceIds.length === 0) {
            messageError('未连接任何设备或截图失败');
            return undefined;
        }

        // 2. 准备输出目录
        const outputDir = outpath || await desktopDir();
        await ensureDirectoryExists(outputDir);

        const savedFilePaths: string[] = [];

        // 3. 为每个设备拉取截图
        await Promise.all(deviceIds.map(async (deviceId) => {
            const result = screencapResult[ deviceId ];
            // 检查特定设备的截图是否成功
            if (result && typeof result === 'string' && result.includes('error')) {
                console.error(`设备 ${deviceId} 截图失败: ${result}`);
                return; // 跳过此设备
            }

            const timestamp = new Date().getTime();
            const devIDForFilename = deviceId.replace(/[.:]/g, '_');
            const imgPath = await join(outputDir, `${devIDForFilename}_${timestamp}.png`);

            // 从设备拉取文件
            await adb_all([ '-s', deviceId, 'pull', tempScreenshotFile, imgPath ]);

            // 删除设备上的临时文件 - 删除失败不阻塞进程
            try {
                await adb_all([ '-s', deviceId, 'shell', 'rm', tempScreenshotFile ]);
            } catch (error) {
                console.error(`设备 ${deviceId} 临时文件删除失败，但不影响截图流程: ${error}`);
            }

            savedFilePaths.push(imgPath);
            console.log(`设备 ${deviceId} 的截图已保存至: ${imgPath}`);
        }));

        if (savedFilePaths.length > 0) {
            messageSuccess(`${savedFilePaths.length} 张截图已保存`);
        } else {
            messageError('所有设备截图均失败');
        }

        return savedFilePaths;
    } catch (error) {
        messageError(`截屏失败: ${error}`);
        return undefined;
    }
}

// 将函数暴露给全局
window.截屏 = 截屏;
