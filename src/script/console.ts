import { invoke } from "@tauri-apps/api/core";

// 创建一个Console类来管理控制台状态和操作
export class Console {
    private static instance: Console;
    private originalConsoleError: typeof console.error = console.error;

    private constructor() {
        this.initializeErrorHandling();
        this.initializeConsoleContainer();
    }

    private initializeErrorHandling() {
        // 重写 console.error
        console.error = (message?: unknown, ...optionalParams: unknown[]) => {
            this.originalConsoleError.call(console, message, ...optionalParams);
            this.updateConsoleVisibility(true);
        };


        // 添加全局错误监听
        window.addEventListener('error', (event) => {
            this.logError(`运行错误: ${event.message}\n位置: ${event.filename}:${event.lineno}:${event.colno}`);
            event.preventDefault();
        });

        // 添加未捕获的 Promise 错误监听
        window.addEventListener('unhandledrejection', (event) => {
            this.logError(`Promise错误: ${event.reason?.toString() || '未知的 Promise 错误'}`);
            event.preventDefault();
        });
    }

    private initializeConsoleContainer() {
        const consoleContainer = document.querySelector('.console-container');
        if (consoleContainer) {
            consoleContainer.addEventListener('click', () => {
                invoke('open_devtools').catch((err) => {
                    this.logError(`打开开发者工具失败: ${err}`);
                });
                this.clearErrors();
            });
        }
    }

    public static getInstance(): Console {
        if (!Console.instance) {
            Console.instance = new Console();
        }
        return Console.instance;
    }

    public updateConsoleVisibility(hasErrors: boolean) {
        const consoleContainer = document.querySelector('.console-container');
        if (consoleContainer) {
            const element = consoleContainer as HTMLElement;
            if (hasErrors) {
                element.style.visibility = 'visible';
                element.style.opacity = '1';
            } else {
                element.style.opacity = '0';
                setTimeout(() => {
                    if (element.style.opacity === '0') {
                        element.style.visibility = 'hidden';
                    }
                }, 300);
            }
        }
    }

    public logError(message: string) {
        console.error(message);
        this.updateConsoleVisibility(true);
    }

    public clearErrors() {
        this.updateConsoleVisibility(false);
    }
}

// 创建一个全局实例
export const consoleInstance = Console.getInstance();
