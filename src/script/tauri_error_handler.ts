/**
 * Tauri 错误处理工具
 * 专门处理 Tauri 应用中的各种错误情况
 */

/**
 * 检查是否是 Tauri 回调 ID 错误
 * 这种错误通常发生在页面重新加载时，Rust 后端仍在执行异步操作
 */
export function isTauriCallbackError(error: unknown): boolean {
    const errorStr = String(error);
    return errorStr.includes('callback id') || 
           errorStr.includes('Couldn\'t find callback') ||
           errorStr.includes('callback not found');
}

/**
 * 检查是否是 Tauri 连接错误
 */
export function isTauriConnectionError(error: unknown): boolean {
    const errorStr = String(error);
    return errorStr.includes('connection') ||
           errorStr.includes('IPC') ||
           errorStr.includes('backend');
}

/**
 * 统一的 Tauri 错误处理器
 */
export class TauriErrorHandler {
    private static instance: TauriErrorHandler;
    private errorCount: number = 0;
    private lastErrorTime: number = 0;

    public static getInstance(): TauriErrorHandler {
        if (!TauriErrorHandler.instance) {
            TauriErrorHandler.instance = new TauriErrorHandler();
        }
        return TauriErrorHandler.instance;
    }

    /**
     * 处理 Tauri 错误
     * @param error 错误对象
     * @param context 错误上下文
     * @returns 是否应该忽略此错误
     */
    public handleError(error: unknown, context: string = ''): boolean {
        const now = Date.now();
        this.errorCount++;
        
        // 如果是回调 ID 错误，通常可以安全忽略
        if (isTauriCallbackError(error)) {
            console.warn(`[Tauri] 检测到回调 ID 错误 (${context}):`, error);
            console.warn('[Tauri] 这通常是由页面重新加载导致的，可以安全忽略');
            return true; // 建议忽略
        }

        // 如果是连接错误，可能需要重试
        if (isTauriConnectionError(error)) {
            console.error(`[Tauri] 检测到连接错误 (${context}):`, error);
            console.error('[Tauri] 建议检查 Tauri 后端状态');
            return false; // 不建议忽略
        }

        // 其他错误
        console.error(`[Tauri] 未知错误 (${context}):`, error);
        this.lastErrorTime = now;
        return false; // 不建议忽略
    }

    /**
     * 获取错误统计
     */
    public getErrorStats(): { count: number; lastErrorTime: number } {
        return {
            count: this.errorCount,
            lastErrorTime: this.lastErrorTime
        };
    }

    /**
     * 重置错误统计
     */
    public resetStats(): void {
        this.errorCount = 0;
        this.lastErrorTime = 0;
    }
}

// 全局错误处理器实例
export const tauriErrorHandler = TauriErrorHandler.getInstance();

/**
 * 便捷函数：处理 Tauri 错误
 */
export function handleTauriError(error: unknown, context: string = ''): boolean {
    return tauriErrorHandler.handleError(error, context);
}

/**
 * 安全执行 Tauri 操作的包装器
 */
export async function safeTauriOperation<T>(
    operation: () => Promise<T>,
    context: string = '',
    fallbackValue?: T
): Promise<T | undefined> {
    try {
        return await operation();
    } catch (error) {
        const shouldIgnore = handleTauriError(error, context);
        
        if (shouldIgnore) {
            console.log(`[Tauri] 忽略错误，返回备用值 (${context})`);
            return fallbackValue;
        } else {
            // 重新抛出错误
            throw error;
        }
    }
}

/**
 * 监听全局未捕获的 Promise 拒绝
 */
if (typeof window !== 'undefined') {
    window.addEventListener('unhandledrejection', (event) => {
        if (isTauriCallbackError(event.reason)) {
            console.warn('[Tauri] 捕获到未处理的回调 ID 错误，已自动忽略:', event.reason);
            event.preventDefault(); // 阻止错误冒泡到控制台
        }
    });
}

/**
 * 创建带有错误处理的 Promise 包装器
 */
export function createSafePromise<T>(
    executor: (resolve: (value: T) => void, reject: (reason?: any) => void) => void,
    context: string = ''
): Promise<T> {
    return new Promise<T>((resolve, reject) => {
        executor(
            resolve,
            (error) => {
                const shouldIgnore = handleTauriError(error, context);
                if (shouldIgnore) {
                    // 将回调错误转换为中断错误
                    reject(new Error('Operation aborted due to page reload'));
                } else {
                    reject(error);
                }
            }
        );
    });
}
