import { adb_all } from './commands';
import { messageLoading, messageSuccess, messageError } from './prompt_message';

// 定义安装参数类型和说明
const installParams = {
    '-r': '替换已存在的应用',
    '-d': '允许降级安装',
    '-t': '允许安装测试包',
} as const;

type InstallParam = keyof typeof installParams;

// 根据错误信息获取需要的安装参数
function getParamsFromError(errMsg: unknown): InstallParam[] {
    const params: InstallParam[] = [];
    const errorStr = String(errMsg).toUpperCase();

    if (errorStr.includes('INSTALL_FAILED_ALREADY_EXISTS')) {
        params.push('-r');
    }
    if (errorStr.includes('INSTALL_FAILED_VERSION_DOWNGRADE')) {
        params.push('-d');
    }
    if (errorStr.includes('INSTALL_FAILED_TEST_ONLY')) {
        params.push('-t');
    }

    return params;
}

/**
 * 生成指定参数集合的所有非空组合
 * @param params 参数数组
 * @returns 参数组合数组
 */
function generateParamCombinations(params: InstallParam[]): InstallParam[][] {
    const result: InstallParam[][] = [];
    const total = Math.pow(2, params.length);

    for (let i = 1; i < total; i++) { // 从1开始，避免空组合
        const combination: InstallParam[] = [];
        for (let j = 0; j < params.length; j++) {
            if (i & (1 << j)) {
                combination.push(params[ j ]);
            }
        }
        result.push(combination);
    }

    return result;
}

/**
 * install_apk文件
 * 支持单个或多个APK文件安装
 * 自动处理常见安装错误:
 * - 已存在的应用会尝试使用-r参数重新安装
 * - 版本降级会尝试使用-d参数安装
 * - 测试包会尝试使用-t参数安装
 * - 其他错误会尝试使用-r -d -t的所有组合参数重新安装
 *
 * @param apkPaths 单个APK路径字符串或APK路径字符串数组
 * @returns 无返回值,安装结果通过消息提示显示
 *
 * 使用示例:
 * ```ts
 * // 安装单个APK
 * await install_apk('/path/to/app.apk')
 *
 * // 安装多个APK
 * await install_apk(['/path/to/app1.apk', '/path/to/app2.apk'])
 * ```
 */
export default async function install_apk(apkPaths: string | string[]) {
    const paths = Array.isArray(apkPaths) ? apkPaths : [ apkPaths ];
    const errors: string[] = [];
    const results: string[] = [];

    async function tryInstall(apkPath: string, params: string[] = []): Promise<boolean> {
        const apkName = apkPath.split(/[/\\]/).pop() || apkPath;
        try {
            messageLoading(`正在安装${params.length > 0 ? `，传入参数: ${params.join(' ')}` : ''}: ${apkName}`);
            console.log(`开始install_apk: ${apkName}，安装参数: ${params.join(' ') || '无'}`);

            // 等待安装完成并获取结果
            const result = await adb_all([ 'install', ...params, apkPath ], true);
            // 确保结果是字符串，如果是对象则尝试提取有用信息
            const resultStr = typeof result === 'object'
                ? JSON.stringify(result, null, 2)  // 格式化对象输出
                : String(result);

            console.log(`安装返回结果: ${resultStr}`);  // 添加详细日志

            const lowerResult = resultStr.toLowerCase();
            if (lowerResult.includes('success')) {
                messageSuccess(`${apkName} 安装成功`);
                results.push(`${apkName}: 安装成功`);
                console.log(`APK安装成功: ${apkName}`);
                return true;
            } else if (lowerResult.includes('failed')) {
                console.log(`APK安装失败: ${apkName}, 失败信息: ${resultStr}`);
                return false;
            } else {
                // 如果没有明确的成功或失败标识，视为失败
                console.log(`APK安装状态未知: ${apkName}, 完整返回信息: ${resultStr}`);
                return false;
            }
        } catch (error: unknown) {
            const errorMessage = error instanceof Error
                ? error.message
                : typeof error === 'object'
                    ? JSON.stringify(error, null, 2)
                    : String(error);
            console.log(`安装出现异常: ${apkName}, 错误信息: ${errorMessage}`);
            return false;
        }
    }

    for (const apkPath of paths) {
        const apkName = apkPath.split(/[/\\]/).pop() || apkPath;

        // 首次尝试安装（无参数）
        if (await tryInstall(apkPath)) {
            continue;
        }

        // 根据错误信息尝试使用特定参数
        const firstTryParams = getParamsFromError(await adb_all([ 'install', apkPath ], true).catch(e => e.message));
        if (firstTryParams.length > 0 && await tryInstall(apkPath, firstTryParams)) {
            continue;
        }

        // 尝试所有参数组合
        const combinationParams = [ '-r', '-d', '-t' ] as InstallParam[];
        const combinations = generateParamCombinations(combinationParams);
        let installed = false;

        for (const combo of combinations) {
            if (await tryInstall(apkPath, combo)) {
                installed = true;
                break;
            }
            // 短暂延迟，避免连续安装可能造成的问题
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        if (!installed) {
            const errorMsg = `${apkName}: 所有安装尝试均失败`;
            console.error(errorMsg);
            errors.push(errorMsg);
        }
    }

    // 打印最终安装结果
    console.log('=== 安装任务完成 ===');
    console.log(`成功安装: ${results.length} 个`);
    console.log(`失败数量: ${errors.length} 个`);

    if (errors.length > 0) {
        messageError(errors.join('\n'));
        throw new Error(errors.join('\n'));
    }
}
