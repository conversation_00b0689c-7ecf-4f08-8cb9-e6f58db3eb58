// // 在Mac上，窗口加载后先关闭阴影，然后加载阴影，因为偶尔会出现阴影消失问题，先关闭后打开目前看来可以修改该问题20250304
// Window, CloseRequestedEvent, getCurrentWindow, getAllWindows, LogicalSize, PhysicalSize, LogicalPosition, PhysicalPosition, UserAttentionType, Effect, EffectState, currentMonitor, monitorFromPoint, primaryMonitor, availableMonitors, cursorPosition
import { getCurrentWindow, LogicalSize, LogicalPosition, availableMonitors, primaryMonitor } from '@tauri-apps/api/window'
import { writeTextFile, readTextFile, exists } from '@tauri-apps/plugin-fs'
import { appDataDir, join } from '@tauri-apps/api/path'
import { platform } from '@tauri-apps/plugin-os';
const appWindow = getCurrentWindow();

// 添加一个标记，表示窗口是否已被初始化
// 使用Window标签作为键存储初始化状态
const windowInitialized = new Map<string, boolean>();

// 定义各窗口尺寸常量
const MAIN_WINDOW_WIDTH = 220;
const MAIN_WINDOW_HEIGHT = 420;
const SEARCH_COLOR_WIDTH = 1200;
const SEARCH_COLOR_HEIGHT = 880;
const CONFIG_WINDOW_WIDTH = 1200;
const CONFIG_WINDOW_HEIGHT = 860;

// 获取存储文件路径
const getStorePath = async () => join(await appDataDir(), 'window-state.json');

// 保存窗口位置到存储
async function saveWindowPosition() {
    try {
        // 只为主窗口保存位置
        if (appWindow.label !== 'search_color' && appWindow.label !== 'edit_config') {
            // 获取窗口位置（这可能是物理坐标或逻辑坐标，取决于Tauri的实现）
            const position = await appWindow.outerPosition();

            // 获取显示器信息，用于处理缩放因子
            await availableMonitors();

            // 尝试找到窗口所在的显示器
            let scaleFactor = 1;
            const currentMonitor = await getCurrentWindowMonitor(position);
            if (currentMonitor) {
                scaleFactor = currentMonitor.scaleFactor;
            } else {
                console.warn('无法确定窗口所在的显示器，使用默认缩放因子1');
            }

            // 转换为逻辑坐标（如果是物理坐标）
            // 注意：根据Tauri的具体实现，position可能已经是逻辑坐标
            // 添加一个调试日志以确定

            // 确保我们使用的是逻辑坐标（假设Tauri返回的是物理坐标）
            // 如果Tauri已经返回逻辑坐标，可以移除这个转换
            let logicalX = Math.round(position.x / scaleFactor);
            let logicalY = Math.round(position.y / scaleFactor);


            // 检查位置是否合理（防止异常大的值）
            if (logicalX > 5000 || logicalY > 5000 || logicalX < -2500 || logicalY < -2500) {
                console.warn('检测到异常的窗口位置值，忽略保存:', { x: logicalX, y: logicalY });
                return;
            }

            // 检查位置是否可见

            // 保存逻辑坐标
            const data = JSON.stringify({ x: logicalX, y: logicalY, isLogical: true });
            await writeTextFile(await getStorePath(), data);
        }
    } catch (error) {
        console.error('保存窗口位置失败:', error);
    }
}

// 获取窗口当前所在的显示器
async function getCurrentWindowMonitor(position: { x: number, y: number }) {
    try {
        const size = await appWindow.outerSize();
        const monitors = await availableMonitors();

        if (!monitors || monitors.length === 0) {
            return null;
        }

        // 计算窗口中心点
        const centerX = position.x + size.width / 2;
        const centerY = position.y + size.height / 2;

        // 找到包含窗口中心点的显示器
        for (const monitor of monitors) {
            const { x: mx, y: my } = monitor.position;
            const { width: mw, height: mh } = monitor.size;

            if (
                centerX >= mx && centerX <= mx + mw &&
                centerY >= my && centerY <= my + mh
            ) {
                return monitor;
            }
        }

        // 如果窗口中心点不在任何显示器内，找到最近的显示器
        let closestMonitor = monitors[ 0 ];
        let minDistance = Number.MAX_VALUE;

        for (const monitor of monitors) {
            const { x: mx, y: my } = monitor.position;
            const { width: mw, height: mh } = monitor.size;

            // 计算窗口中心点到显示器中心点的距离
            const monitorCenterX = mx + mw / 2;
            const monitorCenterY = my + mh / 2;
            const distance = Math.sqrt(
                Math.pow(centerX - monitorCenterX, 2) +
                Math.pow(centerY - monitorCenterY, 2)
            );

            if (distance < minDistance) {
                minDistance = distance;
                closestMonitor = monitor;
            }
        }

        return closestMonitor;
    } catch (error) {
        console.error('获取当前显示器失败:', error);
        return null;
    }
}

// 检查窗口位置是否在屏幕内
async function isPositionVisible(position: { x: number, y: number }) {
    try {
        const size = await appWindow.outerSize();
        const monitors = await availableMonitors();


        if (!monitors || monitors.length === 0) {
            console.warn('没有检测到显示器');
            return false;
        }

        // 检查提供的position是否为逻辑坐标，如果不是则转换
        // 假设所有显示器都有相同的缩放因子，取第一个显示器的缩放因子
        const scaleFactor = monitors[ 0 ].scaleFactor;

        // 窗口逻辑边界
        const windowLeft = position.x;
        const windowRight = position.x + size.width / scaleFactor; // 将物理大小转换为逻辑大小
        const windowTop = position.y;
        const windowBottom = position.y + size.height / scaleFactor;
        const windowArea = (size.width / scaleFactor) * (size.height / scaleFactor);


        // 检查每个显示器
        let maxOverlapPercentage = 0;


        for (let i = 0; i < monitors.length; i++) {
            const monitor = monitors[ i ];
            const { x: mx, y: my } = monitor.position;
            const { width: mw, height: mh } = monitor.size;
            const mScaleFactor = monitor.scaleFactor;

            // 转换为逻辑坐标
            const monitorLeft = mx / mScaleFactor;
            const monitorRight = (mx + mw) / mScaleFactor;
            const monitorTop = my / mScaleFactor;
            const monitorBottom = (my + mh) / mScaleFactor;


            // 计算窗口与显示器的重叠区域
            const overlapLeft = Math.max(windowLeft, monitorLeft);
            const overlapRight = Math.min(windowRight, monitorRight);
            const overlapTop = Math.max(windowTop, monitorTop);
            const overlapBottom = Math.min(windowBottom, monitorBottom);

            // 检查是否有重叠
            if (overlapLeft < overlapRight && overlapTop < overlapBottom) {
                // 计算重叠区域面积及占窗口的百分比
                const overlapWidth = overlapRight - overlapLeft;
                const overlapHeight = overlapBottom - overlapTop;
                const overlapArea = overlapWidth * overlapHeight;
                const overlapPercentage = (overlapArea / windowArea) * 100;


                // 记录最大重叠百分比
                if (overlapPercentage > maxOverlapPercentage) {
                    maxOverlapPercentage = overlapPercentage;

                }

                // 如果重叠区域大于等于窗口面积的50%，则认为可见
                if (overlapPercentage >= 50) {
                    return true;
                }
            }
        }

        // 如果有重叠但没有任何显示器上的重叠超过50%
        if (maxOverlapPercentage > 0) {
        } else {
        }

        return false;
    } catch (error) {
        console.error('检查窗口可见性失败:', error);
        return false;
    }
}

// 获取默认中心位置
async function getDefaultCenterPosition() {
    try {
        const windowSize = new LogicalSize(MAIN_WINDOW_WIDTH, MAIN_WINDOW_HEIGHT);
        const mainMonitor = await primaryMonitor();

        if (mainMonitor) {
            const { x, y } = mainMonitor.position;
            const { width, height } = mainMonitor.size;
            const scaleFactor = mainMonitor.scaleFactor;

            // 转换为逻辑坐标
            const logicalX = x / scaleFactor;
            const logicalY = y / scaleFactor;
            const logicalWidth = width / scaleFactor;
            const logicalHeight = height / scaleFactor;

            // 计算窗口在逻辑坐标中的中心位置
            return {
                x: logicalX + Math.floor((logicalWidth - windowSize.width) / 2),
                y: logicalY + Math.floor((logicalHeight - windowSize.height) / 2)
            };
        } else {
            const monitors = await availableMonitors();
            if (monitors && monitors.length > 0) {
                const firstMonitor = monitors[ 0 ];
                const { x, y } = firstMonitor.position;
                const { width, height } = firstMonitor.size;
                const scaleFactor = firstMonitor.scaleFactor;

                // 转换为逻辑坐标
                const logicalX = x / scaleFactor;
                const logicalY = y / scaleFactor;
                const logicalWidth = width / scaleFactor;
                const logicalHeight = height / scaleFactor;

                return {
                    x: logicalX + Math.floor((logicalWidth - windowSize.width) / 2),
                    y: logicalY + Math.floor((logicalHeight - windowSize.height) / 2)
                };
            }
        }

        // 后备默认位置
        return { x: 100, y: 100 };
    } catch (error) {
        console.error('获取默认中心位置失败:', error);
        return { x: 100, y: 100 };
    }
}

// 初始化窗口
window.addEventListener('DOMContentLoaded', async () => {
    try {
        // 获取操作系统类型
        const isWindows = platform() === 'windows';
        // 先隐藏窗口，避免闪烁
        await appWindow.hide();

        // 根据窗口类型设置对应的尺寸
        const windowLabel = appWindow.label;
        let windowSize;


        // 标记该窗口已开始初始化
        windowInitialized.set(windowLabel || 'main', true);

        // 根据窗口类型设置尺寸
        if (windowLabel === 'search_color') {
            // 搜索颜色窗口 - 创建时已设置正确尺寸，这里确认一下
            windowSize = new LogicalSize(SEARCH_COLOR_WIDTH, SEARCH_COLOR_HEIGHT);
        } else if (windowLabel === 'edit_config') {
            // 编辑配置窗口
            windowSize = new LogicalSize(CONFIG_WINDOW_WIDTH, CONFIG_WINDOW_HEIGHT);
        } else {
            // 主窗口或其他窗口
            windowSize = new LogicalSize(MAIN_WINDOW_WIDTH, MAIN_WINDOW_HEIGHT);
        }

        // 设置窗口尺寸（对于搜索颜色窗口，这是确认性设置，避免闪烁）
        if (windowLabel === 'search_color') {
            // 对于搜索颜色窗口，检查当前尺寸是否已经正确
            try {
                const currentSize = await appWindow.innerSize();
                if (currentSize.width !== SEARCH_COLOR_WIDTH || currentSize.height !== SEARCH_COLOR_HEIGHT) {
                    console.log('搜索颜色窗口尺寸需要调整:', currentSize, '->', windowSize);
                    await appWindow.setSize(windowSize);
                } else {
                    console.log('搜索颜色窗口尺寸已正确:', currentSize);
                }
            } catch (error) {
                console.error('检查窗口尺寸失败，使用默认设置:', error);
                await appWindow.setSize(windowSize);
            }
        } else {
            await appWindow.setSize(windowSize);
        }

        // 设置窗口最小尺寸限制，确保用户无法将窗口缩小到低于设计尺寸
        let minSize: LogicalSize;
        if (windowLabel === 'search_color') {
            // 搜索颜色窗口最小尺寸：1250x1080
            minSize = new LogicalSize(SEARCH_COLOR_WIDTH, SEARCH_COLOR_HEIGHT);
        } else if (windowLabel === 'edit_config') {
            // 配置窗口最小尺寸：1200x860
            minSize = new LogicalSize(CONFIG_WINDOW_WIDTH, CONFIG_WINDOW_HEIGHT);
        } else {
            // 主窗口最小尺寸：220x420
            minSize = new LogicalSize(MAIN_WINDOW_WIDTH, MAIN_WINDOW_HEIGHT);
        }

        try {
            await appWindow.setMinSize(minSize);
            console.log(`${windowLabel || 'main'}窗口最小尺寸已设置为: ${minSize.width}x${minSize.height}`);
        } catch (error) {
            console.error(`设置${windowLabel || 'main'}窗口最小尺寸失败:`, error);
        }

        // Windows 平台上先设置阴影，避免圆角闪烁
        if (isWindows) {
            try {
                await appWindow.setShadow(true);
            } catch (error) {
                console.error('启用窗口阴影失败:', error);
            }
        }

        // 对于搜索颜色窗口和编辑配置窗口，设置居中；对于主窗口，读取保存的位置
        if (windowLabel === 'search_color' || windowLabel === 'edit_config') {
            // 子窗口居中显示
            await appWindow.center();
        } else {
            // 读取保存的位置
            let position;

            if (await exists(await getStorePath())) {
                try {
                    const data = await readTextFile(await getStorePath());
                    const savedData = JSON.parse(data);
                    position = { x: savedData.x, y: savedData.y };

                    // 检查是否标记为逻辑坐标
                    savedData.isLogical === true;

                    // 获取显示器信息，用于检查位置合理性
                    const monitors = await availableMonitors();

                    // 计算所有显示器覆盖的区域边界（考虑缩放因子）
                    let minX = Number.MAX_VALUE;
                    let minY = Number.MAX_VALUE;
                    let maxX = Number.MIN_VALUE;
                    let maxY = Number.MIN_VALUE;

                    if (monitors && monitors.length > 0) {
                        for (const monitor of monitors) {
                            const { x, y } = monitor.position;
                            const { width, height } = monitor.size;
                            const scaleFactor = monitor.scaleFactor;

                            // 转换为逻辑坐标
                            const logicalX = x / scaleFactor;
                            const logicalY = y / scaleFactor;
                            const logicalRight = (x + width) / scaleFactor;
                            const logicalBottom = (y + height) / scaleFactor;

                            minX = Math.min(minX, logicalX);
                            minY = Math.min(minY, logicalY);
                            maxX = Math.max(maxX, logicalRight);
                            maxY = Math.max(maxY, logicalBottom);
                        }

                        // 添加一个合理的边界容差（允许窗口稍微超出屏幕）
                        const tolerance = 10; // 逻辑像素
                        minX -= tolerance;
                        minY -= tolerance;
                        maxX += tolerance;
                        maxY += tolerance;

                        // 检查保存的位置是否在合理范围内
                        if (position.x < minX || position.x > maxX || position.y < minY || position.y > maxY) {
                            console.warn('读取到超出屏幕合理范围的窗口位置，使用默认位置:', position, '屏幕范围:', { minX, minY, maxX, maxY });
                            position = await getDefaultCenterPosition();
                            // 保存默认位置
                            await writeTextFile(await getStorePath(), JSON.stringify({ ...position, isLogical: true }));
                        }
                    }

                    // 检查位置是否在屏幕内
                    const isVisible = await isPositionVisible(position);

                    if (!isVisible) {
                        // 获取新位置（屏幕中心）
                        position = await getDefaultCenterPosition();
                    }
                } catch (e) {
                    console.error('解析保存的窗口位置失败:', e);
                    position = await getDefaultCenterPosition();
                }
            } else {
                position = await getDefaultCenterPosition();
            }

            // 设置窗口位置 (使用LogicalPosition确保使用逻辑坐标)
            await appWindow.setPosition(new LogicalPosition(position.x, position.y));
        }

        // 非 Windows 平台上，设置窗口阴影
        if (!isWindows) {
            try {
                // 使用Tauri的窗口API设置shadow为true
                await appWindow.setShadow(true);
            } catch (error) {
                console.error('启用窗口阴影失败:', error);
            }
        }

        // 所有属性设置完毕后，直接显示窗口
        await appWindow.show();

        // 对于搜索颜色窗口，需要特殊处理焦点和前台状态
        if (windowLabel === 'search_color') {
            // 确保窗口获得焦点
            await appWindow.setFocus();

            // 临时置顶确保窗口在前台，然后取消置顶
            await appWindow.setAlwaysOnTop(true);
            setTimeout(async () => {
                try {
                    await appWindow.setAlwaysOnTop(false);
                } catch (error) {
                    console.error('取消窗口置顶失败:', error);
                }
            }, 100);
        }

        // 子窗口的位置由各自的文件处理，此处不再设置
    } catch (error) {
        console.error('初始化窗口失败:', error);
        await appWindow.show();
    }
});

(async function setupWindowListeners() {
    try {
        await appWindow.onMoved(({ }) => {
            saveWindowPosition();
        });
    } catch (error) {
        console.error('注册窗口移动事件监听器失败:', error);
    }
})();
