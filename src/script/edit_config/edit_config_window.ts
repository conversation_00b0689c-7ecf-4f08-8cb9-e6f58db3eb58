import { invoke } from '@tauri-apps/api/core';
import { messageError, messageSuccess } from '../prompt_message';

// 导入相关模块
import '../title_bar';
import '../help';
import '../user_name';
import '../current_platform';
import '../fix_env_vars';
import '../screenshot';
import '../get_root_dir';

/**
 * 打开配置文件目录
 */
export async function openConfigDirectory(): Promise<void> {
    try {
        console.log('开始获取配置目录路径...');

        // 获取配置目录路径
        const configDir = await invoke<string>('获取配置目录');
        console.log('获取到配置目录路径:', configDir);

        // 打开配置目录
        await invoke('打开目录或文件', { path: configDir });
        console.log('成功打开配置目录');

        messageSuccess('已打开配置文件目录');
    } catch (error) {
        console.error('打开配置目录失败:', error);
        messageError(`打开配置目录失败: ${error}`);
    }
}

// 页面加载完成后初始化功能
document.addEventListener('DOMContentLoaded', () => {
    // 延迟处理DOM操作，确保所有元素都已完全加载
    setTimeout(() => {
        // 为编辑配置按钮添加点击事件监听 (在主窗口中执行)
        const editConfigBtn = document.getElementById('edit_config');
        if (editConfigBtn) {
            editConfigBtn.addEventListener('click', () => {
                openConfigDirectory();
            });
        }
    }, 300); // 延迟300ms等待DOM完全加载
});
