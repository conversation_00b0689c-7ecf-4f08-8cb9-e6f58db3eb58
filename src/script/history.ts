import { invoke } from "@tauri-apps/api/core";
import { dirname, join } from "@tauri-apps/api/path";
import { 生成应用按钮 } from './apply/buttons';
import { 更新输入框 } from './update_input';
import { messageSuccess, messageLoading, messageWarning, messageError } from './prompt_message';

declare global {
    interface Window {
        dragFiles: string;
        parentDir: string;
        cacheDir: string;
        showHistory: () => Promise<void>;
    }
}

interface HistoryItem {
    path: string;
    timestamp: number;
}

// 性能优化：缓存DOM元素和状态
class HistoryManager {
    private searchInput: HTMLInputElement | null = null;
    private isOpen = false;

    constructor() {
        this.initializeElements();
    }

    private initializeElements() {
        this.searchInput = document.querySelector('input[type="text"]') as HTMLInputElement;
    }

    // 防抖函数
    debounce<T extends (...args: any[]) => any>(func: T, wait: number): T {
        let timeout: number;
        return ((...args: any[]) => {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), wait);
        }) as T;
    }

    // 获取搜索文本
    getSearchText(): string {
        return this.searchInput?.value.toLowerCase() || '';
    }

    // 检查是否打开
    isHistoryOpen(): boolean {
        return this.isOpen;
    }

    // 设置状态
    setOpen(isOpen: boolean) {
        this.isOpen = isOpen;
    }

    // 更新当前项目（暂时不需要存储）
    updateCurrentItems(_items: HistoryItem[]) {
        // 暂时不需要存储项目列表
    }
}

// 创建全局历史管理器实例
const historyManager = new HistoryManager();

/**
 * 优化后的关闭历史记录容器函数
 */
async function closeHistoryContainer(): Promise<void> {
    const historyContainer = document.getElementById('history-container');
    if (!historyContainer) return;

    // 使用CSS类而不是内联样式，提高性能
    historyContainer.classList.add('closing');
    historyManager.setOpen(false);

    // 使用Promise和requestAnimationFrame优化动画
    return new Promise<void>(resolve => {
        requestAnimationFrame(() => {
            setTimeout(() => {
                historyContainer.style.display = 'none';
                historyContainer.classList.remove('closing');
                // 移除全局点击监听器
                document.removeEventListener('click', globalClickHandler);
                // 注意：鼠标离开监听器会随着容器隐藏自动失效
                resolve();
            }, 100); // 减少动画时间提高响应性
        });
    });
}

// 删除了handleContainerClick函数，现在使用事件委托统一处理

/**
 * 优化后的打开历史记录容器函数
 */
async function openHistoryContainer() {
    try {
        const result = await invoke<{ items: HistoryItem[] }>('清理失效路径');

        if (result.items.length === 0) {
            messageWarning('暂无历史记录');
            return;
        }

        // 缓存DOM查询
        const historyContainer = document.getElementById('history-container');
        if (!historyContainer) {
            console.error('未找到历史记录容器元素');
            return;
        }

        // 设置事件委托处理历史记录项点击
        setupHistoryEventDelegation(historyContainer);

        // 添加全局点击事件监听器，点击外部关闭历史记录
        setupGlobalClickListener();

        // 添加鼠标离开事件监听器
        setupMouseLeaveListener(historyContainer);

        // 使用CSS类优化动画性能
        historyContainer.className = 'history-container opening';
        historyContainer.style.display = 'flex';
        historyManager.setOpen(true);

        // 优化动画：使用单个requestAnimationFrame
        requestAnimationFrame(() => {
            historyContainer.classList.remove('opening');
            historyContainer.classList.add('open');
        });

        // 使用DocumentFragment批量操作DOM，提高性能
        const fragment = document.createDocumentFragment();

        // 缓存搜索文本
        const searchText = historyManager.getSearchText();

        // 按时间戳倒序排序（使用更高效的排序）
        const sortedItems = result.items.sort((a, b) => b.timestamp - a.timestamp);
        historyManager.updateCurrentItems(sortedItems);

        // 清空现有内容
        historyContainer.innerHTML = '';

        // 批量创建历史记录项
        sortedItems.forEach((item, index) => {
            const historyItem = createHistoryItem(item, index, searchText);
            fragment.appendChild(historyItem);
        });

        // 一次性添加所有元素到DOM
        historyContainer.appendChild(fragment);

        return result;
    } catch (error) {
        const historyContainer = document.getElementById('history-container');
        if (historyContainer) {
            historyContainer.style.display = 'none';
        }
        messageWarning('暂无历史记录');
        return { items: [] };
    }
}

/**
 * 优化的历史记录项创建函数
 */
function createHistoryItem(item: HistoryItem, _index: number, searchText: string): HTMLElement {
    const historyItem = document.createElement('div');
    historyItem.className = 'history-list';
    historyItem.dataset.path = item.path; // 使用data属性存储路径

    const textContainer = document.createElement('div');
    textContainer.className = 'text-container';

    // 优化路径解析
    const pathParts = item.path.split(/[/\\]/);
    const displayPath = pathParts[ pathParts.length - 1 ];
    const displayPath2 = pathParts[ pathParts.length - 2 ];

    // 高亮匹配项
    if (searchText && displayPath.toLowerCase().includes(searchText)) {
        historyItem.classList.add('highlighted');
    }

    textContainer.textContent = `${displayPath2}/${displayPath}`;
    textContainer.title = item.path;

    // 创建删除按钮
    const deleteButton = createDeleteButton();

    // 为删除按钮添加直接的点击事件监听器
    deleteButton.addEventListener('click', async (e) => {
        console.log('删除按钮直接点击事件触发');
        e.stopPropagation();
        e.preventDefault();
        await handleDeleteClick(historyItem, item.path);
    });

    // 创建背景动画层
    const backgroundLayer = document.createElement('div');
    backgroundLayer.className = 'background-layer';

    // 组装元素
    historyItem.appendChild(backgroundLayer);
    historyItem.appendChild(textContainer);
    historyItem.appendChild(deleteButton);

    // 添加悬停效果（使用CSS类而不是内联样式）
    historyItem.addEventListener('mouseenter', () => {
        historyItem.classList.add('hovered');
    });

    historyItem.addEventListener('mouseleave', () => {
        historyItem.classList.remove('hovered');
    });

    return historyItem;
}

/**
 * 创建删除按钮
 */
function createDeleteButton(): HTMLElement {
    const deleteButton = document.createElement('button');
    deleteButton.className = 'del_button';
    deleteButton.textContent = '删除';

    // 添加调试事件监听器
    deleteButton.addEventListener('mouseenter', () => {
        console.log('鼠标进入删除按钮');
    });

    deleteButton.addEventListener('mouseleave', () => {
        console.log('鼠标离开删除按钮');
    });

    // 添加测试点击事件
    deleteButton.addEventListener('mousedown', (e) => {
        console.log('删除按钮鼠标按下事件');
        e.stopPropagation();
    });

    deleteButton.addEventListener('mouseup', (e) => {
        console.log('删除按钮鼠标抬起事件');
        e.stopPropagation();
    });

    return deleteButton;
}

/**
 * 设置全局点击监听器，点击历史记录容器外部时关闭
 */
function setupGlobalClickListener() {
    // 移除之前的监听器（如果存在）
    document.removeEventListener('click', globalClickHandler);
    // 添加新的监听器
    document.addEventListener('click', globalClickHandler);
}

/**
 * 设置鼠标离开监听器，鼠标离开历史记录容器时立即关闭
 */
function setupMouseLeaveListener(historyContainer: HTMLElement) {
    historyContainer.addEventListener('mouseleave', async () => {
        if (historyManager.isHistoryOpen()) {
            await closeHistoryContainer();
        }
    });
}

/**
 * 全局点击处理函数
 */
async function globalClickHandler(e: MouseEvent) {
    if (!historyManager.isHistoryOpen()) return;

    const historyContainer = document.getElementById('history-container');
    if (!historyContainer) return;

    const target = e.target as HTMLElement;
    console.log('全局点击事件:', target.className, target.textContent);

    // 如果点击的是历史记录容器内部，不关闭
    if (historyContainer.contains(target)) {
        console.log('点击了历史记录容器内部，不关闭');
        return;
    }

    // 如果点击的是触发历史记录的按钮，不关闭（避免立即重新打开）
    if (target.closest('#mtz-name-text') || target.id === 'mtz-name-text') {
        console.log('点击了触发按钮，不关闭');
        return;
    }

    // 点击外部，关闭历史记录
    console.log('点击了外部，关闭历史记录');
    await closeHistoryContainer();
}

/**
 * 使用事件委托处理历史记录项的点击事件
 */
function setupHistoryEventDelegation(historyContainer: HTMLElement) {
    historyContainer.addEventListener('click', async (e) => {
        const target = e.target as HTMLElement;
        console.log('历史记录容器点击事件:', target.className, target.textContent);

        // 如果点击的是容器本身（背景区域），关闭历史记录
        if (target.id === 'history-container') {
            console.log('点击了容器背景，关闭历史记录');
            await closeHistoryContainer();
            return;
        }

        const historyItem = target.closest('.history-list') as HTMLElement;

        // 如果没有点击到历史记录项，说明点击了空白区域，不处理
        if (!historyItem) {
            console.log('没有点击到历史记录项');
            return;
        }

        const itemPath = historyItem.dataset.path;
        if (!itemPath) {
            console.log('历史记录项没有路径数据');
            return;
        }

        // 处理删除按钮点击
        if (target.closest('.del_button')) {
            console.log('点击了删除按钮，阻止事件冒泡');
            e.stopPropagation();
            e.preventDefault();
            await handleDeleteClick(historyItem, itemPath);
            return;
        }

        // 处理历史记录项点击
        console.log('点击了历史记录项');
        await handleHistoryItemClick(historyItem, itemPath);
    });
}

/**
 * 处理历史记录项点击
 */
async function handleHistoryItemClick(_historyItem: HTMLElement, itemPath: string) {
    messageLoading('正在加载主题...');
    try {
        await closeHistoryContainer();

        // 更新UI
        const themeName = itemPath.split('/').pop()?.split('\\').pop() || '';
        更新输入框(themeName);
        await 生成应用按钮(itemPath);

        // 更新全局变量
        window.dragFiles = itemPath;
        window.parentDir = await dirname(itemPath);
        const cacheDir1 = await invoke<string>('获取缓存目录');
        window.cacheDir = await join(cacheDir1, themeName);

        messageSuccess('加载主题成功');
    } catch (error) {
        console.error('处理历史记录项点击事件失败:', error);
        messageError(`加载主题失败: ${error}`);
    }
}

/**
 * 处理删除按钮点击
 */
async function handleDeleteClick(historyItem: HTMLElement, itemPath: string) {
    try {
        console.log('开始删除历史记录:', itemPath);
        messageLoading('正在删除历史记录...');

        // 获取主题名称
        const themeName = itemPath.split('/').pop()?.split('\\').pop() || '';

        // 获取缓存目录
        const cacheDir = await invoke<string>('获取缓存目录');
        const themeCacheDir = await join(cacheDir, themeName);

        // 删除历史记录和缓存目录
        await invoke('删除历史记录和缓存', {
            source_path: itemPath,
            cache_dir: themeCacheDir
        });

        // 更新输入框和应用按钮
        更新输入框('');
        await 生成应用按钮('');

        // 重置全局变量
        window.dragFiles = '';
        window.parentDir = '';
        window.cacheDir = '';

        // 添加删除动画并移除元素
        historyItem.classList.add('deleting');
        setTimeout(() => {
            historyItem.remove();
        }, 300);

        messageSuccess('删除成功');
    } catch (error) {
        console.error('删除历史记录失败:', error);
        messageError(`删除失败: ${error}`);
    }
}

/**
 * 切换历史记录列表的显示和隐藏
 */
async function toggleHistoryList() {
    if (historyManager.isHistoryOpen()) {
        await closeHistoryContainer();
    } else {
        await openHistoryContainer();
    }
}

/**
 * 防抖搜索功能
 */
const debouncedSearch = historyManager.debounce((searchText: string) => {
    const historyContainer = document.getElementById('history-container');
    if (!historyContainer || !historyManager.isHistoryOpen()) return;

    const historyItems = historyContainer.querySelectorAll('.history-list');
    historyItems.forEach(item => {
        const textContainer = item.querySelector('.text-container');
        const itemText = textContainer?.textContent?.toLowerCase() || '';

        if (searchText === '' || itemText.includes(searchText.toLowerCase())) {
            (item as HTMLElement).style.display = 'flex';
            if (searchText !== '') {
                item.classList.add('highlighted');
            } else {
                item.classList.remove('highlighted');
            }
        } else {
            (item as HTMLElement).style.display = 'none';
            item.classList.remove('highlighted');
        }
    });
}, 300);

// 导出主要函数
window.showHistory = async () => {
    await openHistoryContainer();
};

export {
    openHistoryContainer,
    closeHistoryContainer,
    toggleHistoryList,
    debouncedSearch
};
