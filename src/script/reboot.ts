declare global {
    interface Window {
        reboot: (edl?: boolean) => Promise<void>;
    }
}

import { messageSuccess, messageError, messageLoading } from './prompt_message';
import { adb } from './commands';
import { isPrivilegedUser } from './user_name';

/**
 * 根据用户权限控制按钮显示
 */
function updateButtonVisibility() {
    const fastbootButton = document.getElementById('FASTBOOT');
    const edlButton = document.getElementById('edl');

    if (fastbootButton && edlButton) {
        const displayStyle = isPrivilegedUser ? 'flex' : 'none';
        fastbootButton.style.display = displayStyle;
        edlButton.style.display = displayStyle;
    }
}

/**
 * 刷机模式
 * @param edl 是否进入EDL模式
 * @returns 无返回值
 */
export default async function reboot(edl = false) {
    messageLoading('正在重启设备')
    // 断开所有连接
    try {
        if (edl) {
            await adb([ 'reboot', 'edl' ]);
            messageSuccess('成功进入EDL模式');
        } else {
            await adb([ 'reboot', 'bootloader' ]);
            messageSuccess('成功进入FASTBOOT模式');
        }
    } catch (error) {
        messageError(`${error}`);
    }
}

// 在DOM加载完成后设置按钮可见性
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(updateButtonVisibility, 2000);
});

window.reboot = reboot;
