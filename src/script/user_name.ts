import { invoke } from '@tauri-apps/api/core';

// 添加全局用户名缓存
let cachedUsername: string | null = null;
// 添加权限用户标志
export let isPrivilegedUser = false;

// 立即执行函数初始化用户权限
(async () => {
    try {
        cachedUsername = await invoke<string>('获取用户名称');
        // 判断是否为特权用户
        isPrivilegedUser = cachedUsername === 'zhangchuanqiang';
        // console.log('用户权限初始化完成', cachedUsername, isPrivilegedUser);
    } catch (error) {
        console.log('初始化用户时出错:', error);
    }
})();

/**
 * 获取缓存的用户名
 */
export async function getCachedUsername(): Promise<string> {
    if (cachedUsername === null) {
        cachedUsername = await invoke<string>('获取用户名称');
    }
    return cachedUsername;
}