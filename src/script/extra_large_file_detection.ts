import { invoke } from "@tauri-apps/api/core";
import { messageError, messageLoading, messageSuccess } from "./prompt_message";
import { 打开目录或文件 } from "./file_operations";

// 声明全局类型
declare global {
    interface Window {
        超大文件检测: typeof 超大文件检测;
    }
}

/**
 * 执行超大文件检测命令，检测指定路径下的文件是否超过1M
 * @param sourcePath 需要检测的文件夹路径
 * @returns 检测结果
 */
export async function 超大文件检测(sourcePath: string) {
    console.log(sourcePath);
    if (!sourcePath) {
        messageError('请先拖入主题文件');
        return '';
    }

    try {
        messageLoading('检测大文件中...');
        const result = await invoke<string>('超大文件检测', {
            sourcePath: sourcePath
        });

        if (result) {
            // 有超大文件时，打开文件所在目录
            打开目录或文件(result);
            messageSuccess('检测大文件完成，请查看"超大文件列表.txt"，该文件并不会被打包进mtz中。');
        } else {
            // 没有超大文件时，显示提示消息
            messageSuccess('未发现超过1M的文件');
        }
        return result;
    } catch (error) {
        console.error('超大文件检测失败:', error);
        return '';
    }
}

// 将函数暴露给全局
window.超大文件检测 = 超大文件检测;
