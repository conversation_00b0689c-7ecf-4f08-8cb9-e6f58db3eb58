import { invoke } from "@tauri-apps/api/core";
import { messageError, messageLoading, messageSuccess } from "./prompt_message";
import { readTextFile } from '@tauri-apps/plugin-fs';
import JSON5 from 'json5';

// 声明全局类型
declare global {
    interface Window {
        replace_color: typeof replace_color;
    }
}



/*
replace_color函数实现功能，将xml中对应标签的值修改为指定值，具体实现为：
函数传入 sourcePath 主题包所在路径
1.读取 sourcePath/colors 目录，获取目录中每个图片中间1像素颜色值，并将其按照文件名设置为变量，例如目录中有图片名为高亮色蓝，图片颜色为#0055ff，则设置变量高亮色蓝="#0055ff"，如果没有 colors 目录则提示未在根目录找到 colors 目录。
2.读取“src-tauri/config/colors.json5”，文件内容为
{
    "com.android.contacts": {
        "高亮色蓝": [
            "miuix_appcompat_action_bar_title_text_color_light",
            "setting_action_bar_tab_text_color",
        ],
        "高亮色红": [
            "bh_txt_red",
        ],
        ...
    },
    ...
}
获取每个对象中对应颜色数组，在theme_path/对象名/theme_values.xml文件中修改对应数组下的xml标签的值为数组名变量的值，需要注意xml中对应标签的值是#xxxxxxxx（argb）,其中#xx不要改动，只改#xx后面6个值，如果对应数组中没有值或没有对应对象，则处理存在的值。

具体修改例子：
com.android.contacts/theme_values.xml内容如下
<?xml version="1.0" encoding="utf-8"?>
<MIUI_Theme_Values template="DarkTemplate">
  <color name="miuix_appcompat_action_bar_title_text_color_light" >#7fffffff</color>
  <color name="setting_action_bar_tab_text_color" >#ffffffff</color>
</MIUI_Theme_Values>

colors.json5中
"com.android.contacts": {
    "高亮色蓝": [
        "miuix_appcompat_action_bar_title_text_color_light",
        "setting_action_bar_tab_text_color",
    ],
    ...
}

colors目录中
高亮色蓝.png颜色值为#0055ff

则修改后com.android.contacts/theme_values.xml内容如下
<?xml version="1.0" encoding="utf-8"?>
<MIUI_Theme_Values template="DarkTemplate">
  <color name="miuix_appcompat_action_bar_title_text_color_light" >#7f0055ff</color>
  <color name="setting_action_bar_tab_text_color" >#ff0055ff</color>
</MIUI_Theme_Values>


需要注意src-tauri/config/colors.json5读取方式与该目录其他json5文件读取方式相同。否则可能存在读取不到文件的情况。

*/






/**
 * 执行replace_color命令
 * @param sourcePath 主题包路径
 */
export async function replace_color(sourcePath: string) {
    console.log('开始执行颜色替换:', sourcePath);
    if (!sourcePath) {
        messageError('请先拖入主题文件');
        return '';
    }

    // 显示确认弹窗
    const userChoice = await showReplaceColorDialog();

    if (userChoice === 'cancel') {
        console.log('用户取消了颜色替换操作');
        return '';
    }

    if (userChoice === 'edit-config') {
        // 打开配置文件进行编辑
        await openColorsConfig();
        return '';
    }

    if (userChoice === 'execute') {
        // 执行颜色替换
        return await executeReplaceColor(sourcePath);
    }

    return '';
}

/**
 * 显示颜色替换确认弹窗
 * @returns 用户选择的操作
 */
async function showReplaceColorDialog(): Promise<'edit-config' | 'execute' | 'cancel'> {
    return new Promise((resolve) => {
        // 创建弹窗容器，使用与设备选择相同的样式
        const modal = document.createElement('div');
        modal.className = 'special-overlay';

        const dialog = document.createElement('div');
        dialog.className = 'special-button-container';
        dialog.onclick = (e) => e.stopPropagation();

        // 标题
        const title = document.createElement('div');
        title.textContent = '颜色替换确认';
        title.className = 'overlay-title';
        title.style.margin = '0 5% -15px 5%'; // 使用负边距让标题和描述更紧凑

        // 说明文字
        const description = document.createElement('div');
        description.className = 'overlay-title';
        description.style.fontSize = '10px';
        description.style.color = '#666';
        description.style.margin = '0 5% 0px 5%'; // 覆盖默认的 margin，设置 15px 下边距
        description.textContent = '即将执行颜色替换操作，请选择：';

        dialog.appendChild(title);
        dialog.appendChild(description);

        // 编辑配置按钮
        const editConfigBtn = document.createElement('button');
        editConfigBtn.textContent = '编辑配置';
        editConfigBtn.className = 'overlay-button';
        editConfigBtn.onclick = () => {
            cleanup();
            resolve('edit-config');
        };

        // 确认执行按钮
        const executeBtn = document.createElement('button');
        executeBtn.textContent = '确认执行';
        executeBtn.className = 'overlay-button';
        executeBtn.onclick = () => {
            cleanup();
            resolve('execute');
        };

        // 取消按钮
        const cancelBtn = document.createElement('button');
        cancelBtn.textContent = '取消';
        cancelBtn.className = 'overlay-button close-button';
        cancelBtn.onclick = () => {
            cleanup();
            resolve('cancel');
        };

        dialog.appendChild(editConfigBtn);
        dialog.appendChild(executeBtn);
        dialog.appendChild(cancelBtn);

        modal.appendChild(dialog);

        const cleanup = () => {
            document.body.removeChild(modal);
        };

        // 点击遮罩层关闭弹窗
        modal.onclick = () => {
            cleanup();
            resolve('cancel');
        };

        document.body.appendChild(modal);
    });
}

/**
 * 获取当前主题路径
 * 这里可以从全局状态、localStorage 或让用户选择
 */
async function getCurrentThemePath(): Promise<string | null> {
    try {
        // 方法1: 从 localStorage 获取最近使用的主题路径
        const recentThemePath = localStorage.getItem('recent_theme_path');
        if (recentThemePath) {
            const { exists } = await import('@tauri-apps/plugin-fs');
            if (await exists(recentThemePath)) {
                return recentThemePath;
            }
        }

        // 方法2: 让用户选择主题目录
        const selectedPath = await selectThemeDirectory();
        if (selectedPath) {
            // 保存到 localStorage 以便下次使用
            localStorage.setItem('recent_theme_path', selectedPath);
            return selectedPath;
        }

        return null;
    } catch (error) {
        console.error('获取主题路径失败:', error);
        return null;
    }
}

/**
 * 让用户选择主题目录
 */
async function selectThemeDirectory(): Promise<string | null> {
    try {
        const selectedPath = await invoke<string>('选择目录');
        if (selectedPath) {
            // 验证是否是有效的主题目录（包含 description.xml）
            const { exists } = await import('@tauri-apps/plugin-fs');
            const descriptionPath = `${selectedPath}/description.xml`;
            if (await exists(descriptionPath)) {
                return selectedPath;
            } else {
                messageError('所选目录不是有效的主题目录（缺少 description.xml）');
                return null;
            }
        }
        return null;
    } catch (error) {
        console.error('选择主题目录失败:', error);
        return null;
    }
}

/**
 * 打开对应版本的配置目录进行编辑
 */
async function openColorsConfig(): Promise<void> {
    try {
        // 获取当前主题路径（从全局状态或用户选择）
        const themePath = await getCurrentThemePath();

        if (!themePath) {
            // 如果没有主题路径，打开 drawable 根目录让用户选择版本
            const drawableDir = await invoke<string>('get_resource_file_path', {
                relativePath: 'drawable'
            });
            await invoke('打开目录或文件', { path: drawableDir });
            messageSuccess('已打开 drawable 目录，请选择对应版本的子目录（如 v12、v15）');
            return;
        }

        // 获取主题的 UI 版本
        const uiVersion = await invoke<number>('get_theme_ui_version', { themePath });
        console.log('主题 UI 版本:', uiVersion);

        // 根据版本获取对应的版本目录名
        const versionDir = await invoke<string>('get_drawable_dir_by_version', { uiVersion });

        // 构建版本目录的完整路径
        const versionDirPath = await invoke<string>('get_resource_file_path', {
            relativePath: `drawable/${versionDir}`
        });

        console.log('打开版本目录:', versionDirPath);
        await invoke('打开目录或文件', { path: versionDirPath });
        messageSuccess(`已打开 ${versionDir} 版本目录`);
    } catch (error) {
        console.error('打开版本目录失败:', error);
        // 降级到 drawable 根目录
        try {
            const drawableDir = await invoke<string>('get_resource_file_path', {
                relativePath: 'drawable'
            });
            await invoke('打开目录或文件', { path: drawableDir });
            messageSuccess('已打开 drawable 目录');
        } catch (fallbackError) {
            messageError(`打开目录失败: ${error}`);
        }
    }
}

/**
 * 执行颜色替换的核心逻辑
 * @param sourcePath 主题包路径
 * @returns 执行结果
 */
async function executeReplaceColor(sourcePath: string): Promise<string> {
    try {
        messageLoading('正在处理颜色替换...');

        // 第一步：检查 colors 目录是否存在
        const colorsDir = `${sourcePath}/colors`;
        const { exists } = await import('@tauri-apps/plugin-fs');

        if (!(await exists(colorsDir))) {
            messageError('未在主题根目录找到 colors 目录');
            return '';
        }

        // 第二步：读取 colors 目录中的图片并提取颜色
        const colorMappings = await extractColorsFromImages(colorsDir);
        if (colorMappings.length === 0) {
            messageError('colors 目录中没有找到有效的图片文件');
            return '';
        }

        console.log('提取到的颜色映射:', colorMappings);

        // 第三步：读取配置文件
        const configData = await readColorsConfig(sourcePath);
        if (!configData) {
            messageError('读取颜色配置文件失败');
            return '';
        }

        // 第四步：调用后端处理 XML 文件
        const result = await invoke<string>('replace_xml_colors', {
            themePath: sourcePath,
            colorMappings: colorMappings,
            configData: JSON.stringify(configData)
        });

        console.log('颜色替换结果:', result);
        messageSuccess(`颜色替换完成: ${result}`);
        return result;

    } catch (error) {
        console.error('颜色替换失败:', error);
        messageError(`颜色替换失败: ${error}`);
        return '';
    }
}

/**
 * 从图片目录中提取颜色映射
 * @param colorsDir colors 目录路径
 * @returns 颜色映射数组
 */
async function extractColorsFromImages(colorsDir: string): Promise<Array<{ name: string, color: string }>> {
    try {
        const { readDir } = await import('@tauri-apps/plugin-fs');
        const entries = await readDir(colorsDir);

        const colorMappings: Array<{ name: string, color: string }> = [];

        for (const entry of entries) {
            if (entry.isFile && entry.name) {
                // 检查是否是图片文件
                const fileName = entry.name.toLowerCase();
                if (fileName.endsWith('.png') || fileName.endsWith('.jpg') || fileName.endsWith('.jpeg')) {
                    try {
                        // 获取文件名（不含扩展名）作为颜色名称
                        const colorName = entry.name.replace(/\.(png|jpg|jpeg)$/i, '');

                        // 构建完整的文件路径
                        const imagePath = `${colorsDir}/${entry.name}`;

                        // 获取图片中心像素的颜色
                        const centerColor = await getImageCenterColor(imagePath);

                        console.log(`提取颜色: ${colorName} = ${centerColor}`);

                        colorMappings.push({
                            name: colorName,
                            color: centerColor
                        });
                    } catch (error) {
                        console.error(`处理图片 ${entry.name} 失败:`, error);
                        // 继续处理下一个文件，不中断整个流程
                    }
                }
            }
        }

        return colorMappings;
    } catch (error) {
        console.error('读取 colors 目录失败:', error);
        throw error;
    }
}

/**
 * 获取图片中心像素的颜色
 * @param imagePath 图片文件路径
 * @returns 颜色值（HEX格式）
 */
async function getImageCenterColor(imagePath: string): Promise<string> {
    try {
        // 使用 Tauri 的文件系统 API 读取图片数据
        const { readFile } = await import('@tauri-apps/plugin-fs');
        const imageData = await readFile(imagePath);

        // 将图片数据转换为 Data URL
        const blob = new Blob([ imageData ], { type: 'image/png' });
        const dataUrl = await new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
        });

        return new Promise((resolve, reject) => {
            const img = new Image();

            img.onload = () => {
                try {
                    // 创建离屏Canvas
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    if (!ctx) {
                        reject(new Error('无法创建Canvas上下文'));
                        return;
                    }

                    // 设置Canvas尺寸为图片实际尺寸
                    canvas.width = img.naturalWidth;
                    canvas.height = img.naturalHeight;

                    // 禁用图像平滑，确保像素精确
                    ctx.imageSmoothingEnabled = false;

                    // 绘制图片
                    ctx.drawImage(img, 0, 0);

                    // 计算中心点坐标
                    const centerX = Math.floor(img.naturalWidth / 2);
                    const centerY = Math.floor(img.naturalHeight / 2);

                    // 获取中心像素颜色
                    const pixelData = ctx.getImageData(centerX, centerY, 1, 1).data;

                    // 转换为HEX格式
                    const hex = '#' +
                        ('0' + pixelData[ 0 ].toString(16)).slice(-2) +
                        ('0' + pixelData[ 1 ].toString(16)).slice(-2) +
                        ('0' + pixelData[ 2 ].toString(16)).slice(-2);

                    resolve(hex);
                } catch (error) {
                    reject(error);
                }
            };

            img.onerror = (error) => {
                reject(new Error(`图片加载失败: ${error}`));
            };

            // 使用 Data URL 作为图片源
            img.src = dataUrl;
        });
    } catch (error) {
        throw new Error(`读取图片文件失败: ${error}`);
    }
}

/**
 * 读取颜色配置文件
 * @param themePath 主题路径，用于确定版本
 * @returns 配置数据对象
 */
async function readColorsConfig(themePath: string): Promise<any> {
    try {
        // 获取主题的 UI 版本
        const uiVersion = await invoke<number>('get_theme_ui_version', { themePath });
        console.log('主题 UI 版本:', uiVersion);

        // 根据 uiVersion 确定配置文件路径（使用统一的版本映射配置）
        const versionDir = await invoke<string>('get_drawable_dir_by_version', { uiVersion });

        // 构建配置文件路径
        const configPath = await invoke<string>('get_resource_file_path', {
            relativePath: `drawable/${versionDir}/colors.json5`
        });

        console.log('使用配置文件:', configPath);

        const content = await readTextFile(configPath);
        const config = JSON5.parse(content);
        return config;
    } catch (error) {
        console.error('读取颜色配置文件失败:', error);
        // 如果版本化配置读取失败，尝试读取默认配置
        try {
            console.log('尝试读取默认配置文件...');
            const defaultConfigPath = await invoke<string>('获取配置文件路径', { fileName: 'colors.json5' });
            const content = await readTextFile(defaultConfigPath);
            const config = JSON5.parse(content);
            return config;
        } catch (fallbackError) {
            console.error('读取默认配置文件也失败:', fallbackError);
            throw error;
        }
    }
}

// 将函数暴露给全局
window.replace_color = replace_color;
