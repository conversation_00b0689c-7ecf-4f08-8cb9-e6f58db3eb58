import { adb_all, 检查连接设备数量 } from './commands';
import { messageLoading, messageError, messageSuccess } from './prompt_message';
import { invoke } from "@tauri-apps/api/core";
import { remove } from '@tauri-apps/plugin-fs';
export async function pushFile(paths: string[]) {

    try {

        // 检查连接设备数量
        if (await 检查连接设备数量(0)) {
            return;
        }

        console.log('开始推送文件到手机', paths);
        messageLoading(`正在推送文件到手机`);

        const zipPath = await invoke<string>('打包文件', {
            paths: paths
        });
        await adb_all([ 'push', zipPath, '/sdcard/1/mini_editor_push_files.zip' ], true);

        // 删除临时zip文件 - 删除失败不阻塞进程
        try {
            await remove(zipPath);
        } catch (error) {
            console.error(`临时文件删除失败，但不影响推送流程: ${error}`);
        }

        messageSuccess(`文件已推送至手机"/sdcard/1/"目录，文件名为mini_editor_push_files.zip`, 5000);

    } catch (error) {
        console.error(`推送文件失败:`, error);
        messageError(`推送文件失败 ${error}`);
    }
}
