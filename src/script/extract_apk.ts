import { messageSuccess, messageError, messageLoading, messageWarning } from './prompt_message';
import { adb, checkAndSelectDeviceGeneral } from './commands';
import { executeCommand } from './terminal_commands';
import { 打开目录或文件, open_uri } from './file_operations';
import { desktopDir, join } from '@tauri-apps/api/path';
import { invoke } from "@tauri-apps/api/core";

// 全局接口定义
declare global {
    interface Window {
        提取apk: (antiCompilation?: boolean) => Promise<string[] | string | void>;
        platform: string;
    }
}


/**
 * 提取并反编译apk文件
 */
export async function 提取apk(
    antiCompilation: boolean = false,
): Promise<string[] | string | void> {

    try {
        // 设备检查和选择
        const selectedDevice = await checkAndSelectDeviceGeneral(
            antiCompilation ? '选择设备进行APK反编译' : '选择设备进行APK提取'
        );
        if (!selectedDevice) {
            messageWarning('未选择设备，操作已取消');
            return;
        }

        const homeDirPath = await desktopDir();

        if (antiCompilation) {
            messageLoading('正在反编译apk...');

            // 获取当前活动包名 - 使用更健壮的方法
            let actionPackage: string | undefined;
            try {
                const result = await adb([ '-s', selectedDevice.id, 'shell', 'dumpsys', 'window', '|', 'grep', 'mCurrentFocus' ], true);
                console.log('dumpsys window 输出:', result);

                // 尝试标准格式匹配
                const match = result.match(/{.*?\s+([\w.]+)\/.*?}/);
                if (match) {
                    actionPackage = match[ 1 ];
                } else {
                    // 尝试特殊系统界面匹配
                    const specialMatch = result.match(/{.*?\s+(\w+)}/);
                    if (specialMatch && specialMatch[ 1 ] === 'NotificationShade') {
                        actionPackage = 'com.android.systemui';
                    }
                }
            } catch (error) {
                console.error('获取包名时出错:', error);
            }

            if (!actionPackage) {
                messageError('无法获取当前活动的包名，请确保设备屏幕处于活动状态');
                return;
            }
            const apkPath = (await adb([ '-s', selectedDevice.id, 'shell', 'pm', 'path', actionPackage ])).replace('package:', '').trim();
            const savePath = await join(homeDirPath, `${actionPackage}.apk`);
            await adb([ '-s', selectedDevice.id, 'pull', apkPath, savePath ]);

            try {
                const outputDecompilePath = await 反编译APK(savePath);
                if (!outputDecompilePath) {
                    messageError('反编译失败');
                    return;
                }
                messageSuccess(`反编译成功`);
                打开目录或文件(outputDecompilePath);
                return outputDecompilePath;
            } catch (decompileError) {
                messageError('反编译失败');
                throw decompileError;
            }

        } else {
            messageLoading('正在提取apk...');

            // 获取当前活动包名 - 使用更健壮的方法
            let actionPackage: string | undefined;
            try {
                const result = await adb([ '-s', selectedDevice.id, 'shell', 'dumpsys', 'window', '|', 'grep', 'mCurrentFocus' ], true);
                console.log('dumpsys window 输出:', result);

                // 尝试标准格式匹配
                const match = result.match(/{.*?\s+([\w.]+)\/.*?}/);
                if (match) {
                    actionPackage = match[ 1 ];
                } else {
                    // 尝试特殊系统界面匹配
                    const specialMatch = result.match(/{.*?\s+(\w+)}/);
                    if (specialMatch && specialMatch[ 1 ] === 'NotificationShade') {
                        actionPackage = 'com.android.systemui';
                    }
                }
            } catch (error) {
                console.error('获取包名时出错:', error);
            }

            if (!actionPackage) {
                messageError('无法获取当前活动的包名，请确保设备屏幕处于活动状态');
                return;
            }
            const apkPath = (await adb([ '-s', selectedDevice.id, 'shell', 'pm', 'path', actionPackage ])).replace('package:', '').trim();
            const savePath = await join(homeDirPath, `${actionPackage}.apk`);
            await adb([ '-s', selectedDevice.id, 'pull', apkPath, savePath ]);
            打开目录或文件(savePath);
            messageSuccess(`已提取到桌面`);
        }
    } catch (error) {
        messageError(`操作失败: ${error}`);
        return;
    }

}

/**
 * 反编译APK文件
 */
async function 反编译APK(
    savePath: string,
): Promise<string | void> {
    try {
        // 查找工具路径
        const apktoolPath = await invoke<string>('find_tool_path', { toolName: 'apktool' });
        if (!apktoolPath) {
            open_uri('https://apktool.org/docs/install');
            messageError('apktool 未安装，请先安装 apktool。如果你知道什么是包管理器，建议通过包管理器进行安装。');
            return;
        }

        // 执行反编译
        const outputDecompilePath = savePath.replace('.apk', '');
        await executeCommand(`${apktoolPath} d -f -s --keep-broken-res --resource-mode keep "${savePath}" -o "${outputDecompilePath}"`);
        return outputDecompilePath;
    } catch (error) {
        console.error(`反编译APK失败: ${error}`);
        throw error; // 重新抛出异常，让调用者处理
    }
}

// 将函数暴露给全局
window.提取apk = 提取apk;


/*

其实总结起来是三个功能，
1: 提取当前活动apk到桌面，成后打开提取的apk
    - 提取时打印正在提取apk...
    - 提取成功后打印已提取到桌面
    - 提取失败后打印提取失败

2: 提取当前活动apk并反编译该apk到桌面,成功后打开反编译的目录
    - 反编译时打印正在反编译apk...
    - 反编译成功后打印已反编译到桌面
    - 反编译失败后打印反编译失败

3: 如果传递了packageName这个数组，也需要传递saveDirPath这个参数，然后多线程异步并行提取apk到saveDirPath中，并反编译apk
    - 提取时不打印任何内容，在所有内容提取完成时打印成功数量和失败数量，如果没有失败则不打印失败数量

这三个功能里又包含了很多小功能
a. 提取apk时要检查连接设备数量是否为1，如果返回false则不执行后续代码，如果有反编译需求要检查apktool是否存在，如果不存在也不执行后续代码。
b. 获取当前活动apk在手机中的路径



*/
