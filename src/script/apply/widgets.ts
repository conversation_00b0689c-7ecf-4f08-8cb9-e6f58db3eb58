import { invoke } from '@tauri-apps/api/core';
import { messageSuccess, messageError, messageLoading } from '../prompt_message';
import { adb_all } from '../commands';
import { buttonCooldowns } from './utils';
import { 预编译 } from '../precompile';
import { ThemeParams, FileInfo } from './types';

/**
 * 应用小部件
 * @param filePath 小部件文件路径
 * @param includeFiles 需要包含的文件列表
 * @param name 小部件名称
 * @param button 按钮元素
 * @returns 无返回值
 */
export async function 应用小部件(_filePath: string, includeFiles?: FileInfo[], name?: string, button?: HTMLButtonElement) {
    const errors: string[] = [];
    let outMtzPath: string = '';
    const totalStartTime = performance.now();

    try {
        messageLoading(`正在应用${name || ''}小部件`);

        // 缓存文件
        const cacheStartTime = performance.now();
        const cacheDir = await invoke('缓存文件', {
            sourcePath: window.dragFiles
        }) as string;
        const cacheEndTime = performance.now();
        console.log(`缓存文件耗时: ${(cacheEndTime - cacheStartTime).toFixed(2)}ms`);

        // 预编译
        const precompileStartTime = performance.now();
        try {
            await 预编译(cacheDir);
        } catch (error) {
            errors.push(`预编译失败: ${error}`);
        }
        const precompileEndTime = performance.now();
        console.log(`预编译耗时: ${(precompileEndTime - precompileStartTime).toFixed(2)}ms`);

        const params: ThemeParams = {
            inputPath: cacheDir,
            outputPath: await invoke('获取缓存目录'),
        };

        if (includeFiles && includeFiles.length > 0) {
            params.includeFiles = includeFiles.map(file => file.name);
        }

        // 打包小部件
        const packStartTime = performance.now();
        try {
            outMtzPath = await invoke('打包主题', params);
        } catch (error) {
            errors.push(`打包小部件失败: ${error}`);
        }
        const packEndTime = performance.now();
        console.log(`打包小部件耗时: ${(packEndTime - packStartTime).toFixed(2)}ms`);

        // 关闭相关应用
        const stopStartTime = performance.now();
        try {
            await adb_all([ 'shell', 'am', 'force-stop', 'com.google.android.documentsui' ], true);
        } catch (error) {
            errors.push(`关闭原生文件管理应用失败: ${error}`);
        }

        try {
            await adb_all([ 'shell', 'am', 'force-stop', 'com.miui.personalassistant' ]);
        } catch (error) {
            errors.push(`关闭智能助理: ${error}`);
        }
        const stopEndTime = performance.now();
        console.log(`关闭相关应用耗时: ${(stopEndTime - stopStartTime).toFixed(2)}ms`);

        // 返回桌面
        const homeStartTime = performance.now();
        try {
            await adb_all([ 'shell', 'am', 'start', '-n', 'com.miui.home/.launcher.Launcher' ]);
        } catch (error) {
            try {
                await adb_all([ 'shell', 'input', 'keyevent', '3' ]);
            } catch (error) {
                errors.push(`返回桌面失败: ${error}`);
            }
        }
        const homeEndTime = performance.now();
        console.log(`返回桌面耗时: ${(homeEndTime - homeStartTime).toFixed(2)}ms`);

        // 启动智能助理
        const assistantStartTime = performance.now();
        try {
            await adb_all([ 'shell', 'am', 'start', '-n', 'com.miui.personalassistant/.maml.ImportMamlActivity' ]);
        } catch (error) {
            errors.push(`启动智能助理失败: ${error}`);
        }
        const assistantEndTime = performance.now();
        console.log(`启动智能助理耗时: ${(assistantEndTime - assistantStartTime).toFixed(2)}ms`);

        // 推送小部件文件
        const pushStartTime = performance.now();
        try {
            await adb_all([ 'push', outMtzPath, '/sdcard/Android/data/com.miui.personalassistant/files/designer/temp.mtz' ]);
        } catch (error) {
            errors.push(`推送小部件文件失败: ${error}`);
        }
        const pushEndTime = performance.now();
        console.log(`推送小部件文件耗时: ${(pushEndTime - pushStartTime).toFixed(2)}ms`);

        await new Promise(resolve => setTimeout(resolve, 500));

        // 应用小部件
        const applyStartTime = performance.now();
        try {
            await adb_all([ 'shell', 'am', 'start', '-n', 'com.miui.personalassistant/.maml.ImportMamlActivity', '-e', 'path', '/sdcard/Android/data/com.miui.personalassistant/files/designer/temp.mtz', '-e', 'xy', name ?? '' ]);
        } catch (error) {
            errors.push(`应用小部件失败: ${error}`);
        }
        const applyEndTime = performance.now();
        console.log(`应用小部件耗时: ${(applyEndTime - applyStartTime).toFixed(2)}ms`);

        // 备份小部件文件
        const backupStartTime = performance.now();
        try {
            await adb_all([ 'push', outMtzPath, '/sdcard/1/temp.mtz' ]);
        } catch (error) {
            errors.push(`推送小部件文件失败: ${error}`);
        }
        const backupEndTime = performance.now();
        console.log(`备份小部件文件耗时: ${(backupEndTime - backupStartTime).toFixed(2)}ms`);

        const totalEndTime = performance.now();
        console.log(`应用小部件总耗时: ${(totalEndTime - totalStartTime).toFixed(2)}ms`);

        if (errors.length > 0) {
            messageError(`应用${name || ''}小部件过程中发生以下错误:\n${errors.join('\n')}`);
        } else {
            messageSuccess(`应用${name || ''}小部件成功`);
        }

        if (button) {
            buttonCooldowns.set(button.textContent || '', false);
            button.style.opacity = '1';
        }
    } catch (error) {
        const totalEndTime = performance.now();
        console.log(`应用小部件失败，总耗时: ${(totalEndTime - totalStartTime).toFixed(2)}ms`);
        messageError(`应用${name || ''}小部件失败:${error}`);
        if (button) {
            buttonCooldowns.set(button.textContent || '', false);
            button.style.opacity = '1';
        }
    }
}