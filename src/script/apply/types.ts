// 声明全局变量类型
declare global {
    interface Window {
        dragFiles: string;
        parentDir: string;
        cacheDir: string;
    }
}

// 应用配置类型定义
export interface 应用配置 {
    主题: {
        [ key: string ]: {
            name: string;
            required: boolean;
            orGroup?: string;  // 用于标识或组
        }[];
    };
    小部件: {
        [ key: string ]: {
            name: string;
            required: boolean;
            orGroup?: string;  // 用于标识或组
        }[];
    };
}

export interface ThemeParams extends Record<string, unknown> {
    inputPath: string;
    outputPath: string;
    includeFiles?: string[];
}

// 导出文件类型接口
export interface FileInfo {
    name: string;
    required: boolean;
    orGroup?: string;
}