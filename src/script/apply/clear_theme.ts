import { messageSuccess, messageError, messageLoading } from '../prompt_message';
import { adb_all } from '../commands';
import { buttonCooldowns } from './utils';

/**
 * 清除调试效果，部分系统不生效
 * @param button 按钮元素
 * @returns 无返回值
 */
export async function 清除调试效果(button?: HTMLButtonElement) {
    const errors: string[] = [];
    try {
        messageLoading(`正在清除调试效果`);

        try {
            await adb_all([ 'shell', 'am', 'broadcast', '-a', 'miui.intent.action.CHECK_TIME_UP' ], true);
        } catch (error) {
            errors.push(`清除调试效果失败: ${error}`);
        }

        // 尝试修改为默认图标（需要debug包或者root权限）
        try {
            await adb_all([ 'shell', 'settings', 'put', 'global', 'is_default_icon', '0' ]);
        } catch (error) {
            // console.log(`尝试修改默认图标配置失败: ${error}`);
        }

        if (errors.length > 0) {
            messageError(`清除调试效果过程中发生以下错误:\n${errors.join('\n')}`);
        } else {
            messageSuccess(`清除调试效果成功，注意部分系统可能不生效`);
        }
        if (button) {
            buttonCooldowns.set(button.textContent || '', false);
            button.style.opacity = '1';
        }
    } catch (error) {
        messageError(`清除调试效果失败:${error}`);
        if (button) {
            buttonCooldowns.set(button.textContent || '', false);
            button.style.opacity = '1';
        }
    }
}