// 统一导出所有功能模块，方便外部导入

// 导出类型定义
export * from './types';

// 导出通用工具函数
export * from './utils';

// 导出按钮相关功能
export * from './buttons';

// 导出主题应用功能
export * from './theme';

// 导出字体应用功能
export * from './font';

// 导出开机动画应用功能
export * from './boot_animation';

// 导出AOD应用功能
export * from './aod';

// 导出小部件应用功能
export * from './widgets';

// 导出调试功能
export * from './clear_theme';

// 页面加载时自动生成按钮
import { 生成应用按钮 } from './buttons';

// 等待DOM加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
    // 初始化应用按钮
    生成应用按钮(window.dragFiles);
});