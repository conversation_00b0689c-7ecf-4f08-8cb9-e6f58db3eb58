import { messageError } from '../prompt_message';
import { getCachedUsername } from '../user_name';
import { 读取应用配置, 更新按钮显示状态, 创建按钮 } from './utils';
import { 应用主题 } from './theme';

/**
 * 生成应用按钮
 * @param cacheDir 根据指定目录中内容生成按钮
 * @returns 无返回值
 */
export async function 生成应用按钮(cacheDir: string | null = null) {
    try {
        // 特权用户检查移动到函数内部
        const username = await getCachedUsername();
        const isPrivilegedUser = username === 'zhangchuanqiang';

        // 读取配置文件
        const 应用配置 = await 读取应用配置();

        // 获取主题按钮容器
        const themeContainer = document.getElementById('apply-theme');
        const widgetContainer = document.getElementById('apply-widget');

        if (!themeContainer || !widgetContainer) {
            console.error('未找到按钮容器');
            return;
        }

        // 如果容器为空，则创建按钮
        if (!themeContainer.hasChildNodes()) {
            // 添加"全部"按钮，初始状态设为隐藏
            const allButton = 创建按钮('应用全部', [], false);
            allButton.onclick = () => {
                if (!window.cacheDir) {
                    messageError('请拖入主题文件');
                    return;
                }

                // 动态导入主题模块
                应用主题(window.cacheDir);
            };
            // 确保初始状态是隐藏的
            allButton.classList.add('button_hidden');
            allButton.classList.remove('button_show');
            themeContainer.appendChild(allButton);

            // 生成主题按钮
            for (const [ name, files ] of Object.entries(应用配置.主题)) {
                // 如果是"字体"按钮且用户不是特权用户，则跳过
                if (name === '字体' && !isPrivilegedUser) {
                    continue;
                }
                const button = 创建按钮(name, files, false);
                themeContainer.appendChild(button);
            }
        }

        if (!widgetContainer.hasChildNodes()) {
            // 生成小部件按钮
            for (const [ name, files ] of Object.entries(应用配置.小部件)) {
                const button = 创建按钮(name, files, true);
                widgetContainer.appendChild(button);
            }
        }

        // 先更新小部件按钮的状态
        const widgetButtons = Array.from(widgetContainer.children) as HTMLButtonElement[];
        let hasVisibleWidget = false;
        for (const button of widgetButtons) {
            if (button instanceof HTMLButtonElement) {
                const name = button.textContent || '';
                const files = 应用配置.小部件[ name ] || [];
                await 更新按钮显示状态(button, cacheDir, files);
                if (button.classList.contains('button_show')) {
                    hasVisibleWidget = true;
                }
            }
        }

        // 更新主题按钮的状态
        const themeButtons = Array.from(themeContainer.children) as HTMLButtonElement[];
        for (const button of themeButtons) {
            if (button instanceof HTMLButtonElement) {
                const name = button.textContent || '';
                if (name === '应用全部') {
                    // 只有在没有可见的小部件时才可能显示全部按钮
                    if (!hasVisibleWidget && cacheDir) {
                        button.classList.remove('button_hidden');
                        button.classList.add('button_show');
                    } else {
                        button.classList.add('button_hidden');
                        button.classList.remove('button_show');
                    }
                } else {
                    const files = 应用配置.主题[ name ] || [];
                    await 更新按钮显示状态(button, cacheDir, files);
                }
            }
        }

    } catch (error) {
        messageError(`生成应用按钮失败: ${error}`);
    }
}