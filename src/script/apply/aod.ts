import { invoke } from '@tauri-apps/api/core';
import { messageSuccess, messageError, messageLoading } from '../prompt_message';
import { adb_all, adb, getDevices } from '../commands';
import { buttonCooldowns, disabledDevices } from './utils';
import { 预编译 } from '../precompile';
import { ThemeParams, FileInfo } from './types';

/**
 * 应用AOD
 * @param filePath AOD文件路径
 * @param includeFiles 需要包含的文件列表
 * @param name AOD名称
 * @param button 按钮元素
 * @returns 无返回值
 */
export async function 应用AOD(_filePath: string, includeFiles?: FileInfo[], name?: string, button?: HTMLButtonElement) {
    const errors: string[] = [];
    let outMtzPath: string = '';
    const totalStartTime = performance.now();

    // 获取系统版本
    try {
        const result = await adb_all([ 'shell', 'getprop', 'ro.bootimage.build.version.incremental' ]);
        console.log(result, '********************************************************');
    } catch (error) {
        errors.push(`获取系统版本失败: ${error}`);
    }

    try {
        messageLoading(`正在应用${name}`);

        // 缓存文件
        const cacheStartTime = performance.now();
        const cacheDir = await invoke('缓存文件', {
            sourcePath: window.dragFiles
        }) as string;
        const cacheEndTime = performance.now();
        console.log(`缓存文件耗时: ${(cacheEndTime - cacheStartTime).toFixed(2)}ms`);

        // 预编译
        const precompileStartTime = performance.now();
        try {
            await 预编译(cacheDir);
        } catch (error) {
            errors.push(`预编译失败: ${error}`);
        }
        const precompileEndTime = performance.now();
        console.log(`预编译耗时: ${(precompileEndTime - precompileStartTime).toFixed(2)}ms`);

        const params: ThemeParams = {
            inputPath: cacheDir,
            outputPath: await invoke('获取缓存目录'),
        };

        if (includeFiles && includeFiles.length > 0) {
            params.includeFiles = includeFiles.map(file => file.name);
        }

        // 停止AOD应用
        const stopStartTime = performance.now();
        try {
            await adb_all([ 'shell', 'am', 'force-stop', 'com.miui.aoddemo' ]);
        } catch (error) {
            errors.push(`停止AOD应用失败: ${error}`);
        }
        const stopEndTime = performance.now();
        console.log(`停止AOD应用耗时: ${(stopEndTime - stopStartTime).toFixed(2)}ms`);

        // 打包AOD
        const packStartTime = performance.now();
        try {
            outMtzPath = await invoke('打包主题', params);
        } catch (error) {
            errors.push(`打包AOD失败: ${error}`);
        }
        const packEndTime = performance.now();
        console.log(`打包AOD耗时: ${(packEndTime - packStartTime).toFixed(2)}ms`);

        // 提取AOD文件
        const extractStartTime = performance.now();
        let aodPath = '';
        try {
            aodPath = await invoke('提取压缩文件', {
                inputPath: outMtzPath,
                fileName: 'aod',
            }) as string;
        } catch (error) {
            errors.push(`提取AOD文件失败: ${error}`);
        }
        const extractEndTime = performance.now();
        console.log(`提取AOD文件耗时: ${(extractEndTime - extractStartTime).toFixed(2)}ms`);

        // 获取所有已连接设备
        let devices;
        try {
            devices = await getDevices();
        } catch (error) {
            errors.push(`获取设备列表失败: ${error}`);
            throw error; // 如果无法获取设备列表，终止后续操作
        }

        // 对每个设备单独处理
        for (const device of devices) {
            console.log(`正在处理设备: ${device.model} (${device.id})`);

            // 检查设备是否被禁用
            if (disabledDevices.has(device.id)) {
                console.log(`设备 ${device.model} (${device.id}) 已被禁用，跳过处理`);
                continue;
            }

            // 获取设备系统版本
            let isOS2System = false;
            try {
                // 检查 ro.bootimage.build.version.incremental 属性
                const versionResult = await adb([ '-s', device.id, 'shell', 'getprop', 'ro.bootimage.build.version.incremental' ]);
                const isOS2FromIncremental = versionResult.toString().includes('OS2');

                // 新增 ro.mi.os.version.name 属性检查
                let isOS2FromVersionName = false;
                try {
                    const versionNameResult = await adb([ '-s', device.id, 'shell', 'getprop', 'ro.mi.os.version.name' ]);
                    // 转换为小写进行比较，实现不区分大小写
                    isOS2FromVersionName = versionNameResult.toString().toLowerCase().includes('os2');
                    console.log(`设备 ${device.model} (${device.id}) 系统版本名称: ${versionNameResult}, 是否包含OS2: ${isOS2FromVersionName}`);
                } catch (error) {
                    console.log(`获取设备 ${device.model} (${device.id}) 系统版本名称失败: ${error}`);
                }

                // 任一检查为true则认为是OS2系统
                isOS2System = isOS2FromIncremental || isOS2FromVersionName;
                console.log(`设备 ${device.model} (${device.id}) 系统版本: ${versionResult}, 最终判断是否为OS2: ${isOS2System}`);
            } catch (error) {
                console.log(`获取设备 ${device.model} (${device.id}) 系统版本失败: ${error}`);
                // 获取版本失败时默认使用非OS2路径
                isOS2System = false;
            }

            // 根据设备系统版本推送AOD文件
            try {
                if (isOS2System) {
                    console.log(`设备 ${device.model} (${device.id}) 使用OS2路径推送AOD文件`);
                    await adb([ '-s', device.id, 'push', aodPath, '/sdcard/Android/data/com.miui.aoddemo/files/aod' ]);
                } else {
                    console.log(`设备 ${device.model} (${device.id}) 使用非OS2路径推送AOD文件`);
                    await adb([ '-s', device.id, 'push', aodPath, '/sdcard/miui/aod/aod' ]);
                }
            } catch (error) {
                errors.push(`推送AOD文件到设备 ${device.model} (${device.id}) 失败: ${error}`);
                continue; // 如果推送失败，继续处理下一个设备
            }

            // 启动AOD预览
            try {
                if (isOS2System) {
                    console.log(`设备 ${device.model} (${device.id}) 使用OS2路径启动AOD预览`);
                    await adb([ '-s', device.id, 'shell', 'am', 'start', '-W', '-a', 'android.intent.action.VIEW', '-d', 'theme://com.miui.aod/demo?path=aod' ]);
                } else {
                    console.log(`设备 ${device.model} (${device.id}) 使用非OS2路径启动AOD预览`);
                    await adb([ '-s', device.id, 'shell', 'am', 'start', '-W', '-a', 'android.intent.action.VIEW', '-d', 'theme://com.miui.aod/demo?path=/sdcard/miui/aod/aod' ]);
                }
            } catch (error) {
                errors.push(`启动设备 ${device.model} (${device.id}) AOD预览失败: ${error}`);
            }
        }

        const totalEndTime = performance.now();
        console.log(`应用AOD总耗时: ${(totalEndTime - totalStartTime).toFixed(2)}ms`);

        if (errors.length > 0) {
            messageError(`应用${name}过程中发生以下错误:\n${errors.join('\n')}`);
        } else {
            messageSuccess(`应用${name}成功`);
        }
        if (button) {
            buttonCooldowns.set(button.textContent || '', false);
            button.style.opacity = '1';
        }
    } catch (error) {
        const totalEndTime = performance.now();
        console.log(`应用AOD失败，总耗时: ${(totalEndTime - totalStartTime).toFixed(2)}ms`);
        messageError(`应用${name}失败:${error}`);
        if (button) {
            buttonCooldowns.set(button.textContent || '', false);
            button.style.opacity = '1';
        }
    }
}