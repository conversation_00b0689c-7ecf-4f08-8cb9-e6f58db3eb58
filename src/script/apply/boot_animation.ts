import { invoke } from '@tauri-apps/api/core';
import { messageSuccess, messageError, messageLoading } from '../prompt_message';
import { adb_all } from '../commands';
import { buttonCooldowns } from './utils';
import { ThemeParams, FileInfo } from './types';

/**
 * 应用开机动画
 * @param filePath 开机动画文件路径
 * @param includeFiles 需要包含的文件列表
 * @param name 开机动画名称
 * @param button 按钮元素
 * @returns 无返回值
 */
export async function 应用开机动画(_filePath: string, includeFiles?: FileInfo[], name?: string, button?: HTMLButtonElement) {
    const errors: string[] = [];
    let outMtzPath: string = '';
    const totalStartTime = performance.now();

    try {
        messageLoading(`正在应用${name || '开机动画'}`);

        // 缓存文件
        const cacheStartTime = performance.now();
        const cacheDir = await invoke('缓存文件', {
            sourcePath: window.dragFiles
        }) as string;
        const cacheEndTime = performance.now();
        console.log(`缓存文件耗时: ${(cacheEndTime - cacheStartTime).toFixed(2)}ms`);

        const params: ThemeParams = {
            inputPath: cacheDir,
            outputPath: await invoke('获取缓存目录'),
        };
        if (includeFiles && includeFiles.length > 0) {
            params.includeFiles = includeFiles.map(file => file.name);
        }

        // 打包主题
        const packStartTime = performance.now();
        try {
            outMtzPath = await invoke('打包主题', params);
        } catch (error) {
            errors.push(`打包主题失败: ${error}`);
        }
        const packEndTime = performance.now();
        console.log(`打包主题耗时: ${(packEndTime - packStartTime).toFixed(2)}ms`);

        // 推送主题文件
        const pushStartTime = performance.now();
        try {
            await adb_all([ 'push', outMtzPath, '/sdcard/Android/data/com.android.thememanager/files/temp.mtz' ], true);
        } catch (error) {
            errors.push(`推送主题文件失败: ${error}`);
        }
        const pushEndTime = performance.now();
        console.log(`推送主题文件耗时: ${(pushEndTime - pushStartTime).toFixed(2)}ms`);

        // 应用主题
        const applyStartTime = performance.now();
        try {
            await adb_all([ 'shell', 'am', 'start', '-n', 'com.android.thememanager/com.android.thememanager.ApplyThemeForScreenshot', '-e', 'theme_file_path', '/sdcard/Android/data/com.android.thememanager/files/temp.mtz', '-e', 'api_called_from', 'ThemeEditor' ]);
        } catch (error) {
            errors.push(`应用主题失败: ${error}`);
        }
        const applyEndTime = performance.now();
        console.log(`应用主题耗时: ${(applyEndTime - applyStartTime).toFixed(2)}ms`);

        // 重启手机
        // try {
        //     await new Promise(resolve => setTimeout(resolve, 3000));
        //     await adb_all([ 'reboot' ]);
        // } catch (error) {
        //     errors.push(`重启手机失败: ${error}`);
        // }

        const totalEndTime = performance.now();
        console.log(`应用开机动画总耗时: ${(totalEndTime - totalStartTime).toFixed(2)}ms`);

        if (errors.length > 0) {
            messageError(`应用${name || '开机动画'}过程中发生以下错误:\n${errors.join('\n')}`);
        } else {
            messageSuccess(`应用${name || '开机动画'}成功，请手动重启观察动画`);
        }
        if (button) {
            buttonCooldowns.set(button.textContent || '', false);
            button.style.opacity = '1';
        }
    } catch (error) {
        const totalEndTime = performance.now();
        console.log(`应用开机动画失败，总耗时: ${(totalEndTime - totalStartTime).toFixed(2)}ms`);
        messageError(`应用${name || '开机动画'}失败:${error}`);
        if (button) {
            buttonCooldowns.set(button.textContent || '', false);
            button.style.opacity = '1';
        }
    }
}