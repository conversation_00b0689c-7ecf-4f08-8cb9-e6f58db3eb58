import { invoke } from '@tauri-apps/api/core';
import { messageSuccess, messageError, messageLoading } from '../prompt_message';
import { adb_all } from '../commands';
import { buttonCooldowns } from './utils';
import { 预编译 } from '../precompile';
import { ThemeParams, FileInfo } from './types';

/**
 * 应用字体
 * @param filePath 字体文件路径
 * @param includeFiles 需要包含的文件列表
 * @param name 字体名称
 * @param button 按钮元素
 * @returns 无返回值
 */
export async function 应用字体(_filePath: string, includeFiles?: FileInfo[], name?: string, button?: HTMLButtonElement) {
    const errors: string[] = [];
    let outMtzPath: string = '';
    const totalStartTime = performance.now();

    try {
        messageLoading(`正在应用${name || '字体'}`);

        // 缓存文件
        const cacheStartTime = performance.now();
        const cacheDir = await invoke('缓存文件', {
            sourcePath: window.dragFiles
        }) as string;
        const cacheEndTime = performance.now();
        console.log(`缓存文件耗时: ${(cacheEndTime - cacheStartTime).toFixed(2)}ms`);

        // 预编译
        const precompileStartTime = performance.now();
        try {
            await 预编译(cacheDir);
        } catch (error) {
            errors.push(`预编译失败: ${error}`);
        }
        const precompileEndTime = performance.now();
        console.log(`预编译耗时: ${(precompileEndTime - precompileStartTime).toFixed(2)}ms`);

        const params: ThemeParams = {
            inputPath: cacheDir,
            outputPath: await invoke('获取缓存目录'),
        };
        if (includeFiles && includeFiles.length > 0) {
            params.includeFiles = includeFiles.map(file => file.name);
        }

        // 打包主题
        const packStartTime = performance.now();
        try {
            outMtzPath = await invoke('打包主题', params);
        } catch (error) {
            errors.push(`打包字体失败: ${error}`);
        }
        const packEndTime = performance.now();
        console.log(`打包字体耗时: ${(packEndTime - packStartTime).toFixed(2)}ms`);

        // 推送主题文件
        const pushStartTime = performance.now();
        try {
            await adb_all([ 'push', outMtzPath, '/sdcard/Android/data/com.android.thememanager/files/temp.mtz' ], true);
        } catch (error) {
            errors.push(`推送字体文件失败: ${error}`);
        }
        const pushEndTime = performance.now();
        console.log(`推送字体文件耗时: ${(pushEndTime - pushStartTime).toFixed(2)}ms`);

        // 应用字体
        const applyStartTime = performance.now();
        try {
            await adb_all([ 'shell', 'am', 'start', '-n', 'com.android.thememanager/com.android.thememanager.ApplyThemeForScreenshot', '-e', 'theme_file_path', '/sdcard/Android/data/com.android.thememanager/files/temp.mtz', '-e', 'api_called_from', 'web', '-e', 'theme_apply_flags', '16' ]);
        } catch (error) {
            errors.push(`应用字体失败: ${error}`);
        }
        const applyEndTime = performance.now();
        console.log(`应用字体耗时: ${(applyEndTime - applyStartTime).toFixed(2)}ms`);

        if (errors.length > 0) {
            messageError(`应用${name || '字体'}过程中发生以下错误:\n${errors.join('\n')}`);
        } else {
            messageSuccess(`应用${name || '字体'}成功 `);
        }

        // 返回桌面
        await new Promise(resolve => setTimeout(resolve, 2000));
        const homeStartTime = performance.now();
        try {
            await adb_all([ 'shell', 'am', 'start', '-n', 'com.miui.home/.launcher.Launcher' ]);
        } catch (error) {
            try {
                await adb_all([ 'shell', 'input', 'keyevent', '3' ]);
            } catch (error) {
                errors.push(`返回桌面失败: ${error}`);
            }
        }
        const homeEndTime = performance.now();
        console.log(`返回桌面耗时: ${(homeEndTime - homeStartTime).toFixed(2)}ms`);

        const totalEndTime = performance.now();
        console.log(`应用字体总耗时: ${(totalEndTime - totalStartTime).toFixed(2)}ms`);
        if (button) {
            buttonCooldowns.set(button.textContent || '', false);
            button.style.opacity = '1';
        }
    } catch (error) {
        const totalEndTime = performance.now();
        console.log(`应用字体失败，总耗时: ${(totalEndTime - totalStartTime).toFixed(2)}ms`);
        messageError(`应用${name || '字体'}失败:${error}`);
        // 出错时立即解除冷却
        if (button) {
            buttonCooldowns.set(button.textContent || '', false);
            button.style.opacity = '1';
        }
    }
}