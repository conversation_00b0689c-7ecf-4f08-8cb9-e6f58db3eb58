import { invoke } from '@tauri-apps/api/core';
import { messageError } from '../prompt_message';
import { join } from '@tauri-apps/api/path';
import JSON5 from 'json5';
import { 应用配置, FileInfo } from './types';
import { readTextFile } from '@tauri-apps/plugin-fs';

// 用于存储被禁用的设备列表
export const disabledDevices = new Set<string>();

// 防抖标志
export const buttonCooldowns = new Map<string, boolean>();

/**
 * 读取应用配置文件
 * @returns 返回应用配置
 */
export async function 读取应用配置(): Promise<应用配置> {
    try {
        const configPath = await invoke<string>('获取配置文件路径', { fileName: 'apply_config.json5' });
        // 使用 readTextFile 读取文件内容
        const content = await readTextFile(configPath);
        const config = JSON5.parse(content);
        return config;
    } catch (error) {
        messageError(`读取应用配置失败: ${error}`);
        return {
            主题: {},
            小部件: {}
        };
    }
}

/**
 * 检查目录是否包含指定文件
 * @param cacheDir 缓存目录
 * @param files 文件列表
 * @returns 返回检查结果
 */
export async function 检查目录包含文件(cacheDir: string | null, files: FileInfo[]): Promise<boolean> {
    try {
        if (!cacheDir) {
            return false;
        }

        // 按orGroup分组
        const orGroups = new Map<string, FileInfo[]>();
        const normalFiles = files.filter(file => !file.orGroup);

        // 收集所有带orGroup的文件
        files.forEach(file => {
            if (file.orGroup) {
                if (!orGroups.has(file.orGroup)) {
                    orGroups.set(file.orGroup, []);
                }
                orGroups.get(file.orGroup)?.push({
                    name: file.name,
                    required: file.required
                });
            }
        });

        // 检查必需的普通文件
        const requiredFiles = normalFiles.filter(file => file.required);
        for (const file of requiredFiles) {
            try {
                const filePath = await join(cacheDir, file.name);
                const exists = await invoke<boolean>('检查路径是否存在', { path: filePath });
                if (!exists) {
                    return false;
                }
            } catch (error) {
                console.error(`检查文件 ${file.name} 失败:`, error);
                return false;
            }
        }

        // 检查orGroup文件，只要组内有一个文件存在即可
        for (const [ , groupFiles ] of orGroups) {
            let groupExists = false;
            for (const file of groupFiles) {
                try {
                    const filePath = await join(cacheDir, file.name);
                    const exists = await invoke<boolean>('检查路径是否存在', { path: filePath });
                    if (exists) {
                        groupExists = true;
                        break;
                    }
                } catch (error) {
                    console.error(`检查文件 ${file.name} 失败:`, error);
                }
            }

            // 如果组内所有文件都不存在，且有必需文件，则返回false
            if (!groupExists && groupFiles.some(file => file.required)) {
                return false;
            }
        }

        // 检查可选的普通文件
        const optionalFiles = normalFiles.filter(file => !file.required);
        for (const file of optionalFiles) {
            try {
                const filePath = await join(cacheDir, file.name);
                await invoke<boolean>('检查路径是否存在', { path: filePath });
            } catch (error) {
                console.error(`检查文件 ${file.name} 失败:`, error);
            }
        }

        return true;
    } catch (error) {
        console.error('检查目录失败:', error);
        return false;
    }
}

/**
 * 更新按钮显示状态
 * @param button 按钮元素
 * @param cacheDir 缓存目录
 * @param files 文件列表
 * @returns 无返回值
 */
export async function 更新按钮显示状态(button: HTMLButtonElement, cacheDir: string | null, files: FileInfo[]) {
    if (cacheDir) {
        const hasFiles = await 检查目录包含文件(cacheDir, files);
        if (hasFiles) {
            button.classList.add('button_show');
            button.classList.remove('button_hidden');
        } else {
            button.classList.add('button_hidden');
            button.classList.remove('button_show');
        }
    } else {
        button.classList.add('button_hidden');
        button.classList.remove('button_show');
    }
}

/**
 * 创建按钮元素
 * @param name 按钮名称
 * @param files 文件列表
 * @param isWidget 是否为小部件按钮
 * @returns 返回创建的按钮元素
 */
export function 创建按钮(name: string, files: FileInfo[], isWidget: boolean = false): HTMLButtonElement {
    const button = document.createElement('button');
    button.className = isWidget ? 'widget-button' : 'button';
    button.textContent = name;
    button.classList.add('button_hidden');

    button.onclick = async () => {
        // 检查按钮是否在冷却中
        if (buttonCooldowns.get(name)) {
            messageError('操作进行中，请稍后再试');
            return;
        }

        const cacheDir = window.cacheDir;
        if (!cacheDir) {
            messageError('请拖入主题文件');
            return;
        }

        // 设置按钮冷却
        buttonCooldowns.set(name, true);
        button.style.opacity = '0.5'; // 视觉反馈

        // 动态导入相应的功能模块
        try {
            if (name === '息屏' || name === 'AOD' || name === 'aod') {
                const { 应用AOD } = await import('./aod');
                await 应用AOD(cacheDir, files, name, button);
            } else if (name === '开机动画') {
                const { 应用开机动画 } = await import('./boot_animation');
                await 应用开机动画(cacheDir, files, name, button);
            } else if (name === '清除调试效果') {
                const { 清除调试效果 } = await import('./clear_theme');
                await 清除调试效果(button);
            } else if (name === '字体') {
                const { 应用字体 } = await import('./font');
                await 应用字体(cacheDir, files, name, button);
            } else if (name === '1x2' || name === '2x1' || name === '2x2' || name === '2x3' || name === '4x1' || name === '4x2' || name === '4x3' || name === '4x4') {
                const { 应用小部件 } = await import('./widgets');
                await 应用小部件(cacheDir, files, name, button);
            } else {
                const { 应用主题 } = await import('./theme');
                await 应用主题(cacheDir, files, name, button);
            }
        } catch (error) {
            console.error(`加载或执行模块失败: ${error}`);
            messageError(`执行失败: ${error}`);
            // 出错时立即解除冷却
            buttonCooldowns.set(name, false);
            button.style.opacity = '1';
        }
    };

    return button;
}