import { open_uri } from './file_operations';

// 添加动画状态标志
let isAnimating = false;

// 控制帮助内容显示和隐藏的函数
export async function toggleHelpContent(): Promise<void> {
    // 如果动画正在进行中，直接返回
    if (isAnimating) {
        return;
    }
    isAnimating = true;

    // 获取帮助内容元素
    const helpContent = document.querySelector('#help-content, .help-content') as HTMLElement;
    if (!helpContent) {
        console.error('未找到帮助内容元素');
        return;
    }

    // 获取所有链接
    const links = helpContent.querySelectorAll('a');

    // 获取当前显示状态
    const isVisible = helpContent.style.display !== 'none';

    // 创建或获取已存在的圆形背景
    let circle = document.querySelector('.help-circle') as HTMLElement;
    const clickX = window.innerWidth / 2;
    // 根据显示状态设置不同的 clickY 值
    let clickY = isVisible ? 0 : window.innerHeight;

    if (!circle) {
        // 先创建一个全屏遮罩
        const overlay = document.createElement('div');
        overlay.className = 'help-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: transparent;
            z-index: 99998;
        `;
        document.body.appendChild(overlay);

        // 然后创建圆形
        circle = document.createElement('div');
        circle.className = 'help-circle';
        circle.style.cssText = `
            position: fixed;
            top: ${clickY}px;
            left: ${clickX}px;
            z-index: 99999;
        `;
        document.body.appendChild(circle);
    } else {
        // 更新现有圆形的位置
        circle.style.top = `${clickY}px`;
    }

    // 计算需要覆盖整个屏幕的圆的直径
    const diameter = Math.max(
        Math.max(
            clickX,
            window.innerWidth - clickX
        ) * 2.5,
        Math.max(
            clickY,
            window.innerHeight - clickY
        ) * 2.5
    );

    if (isVisible) {
        // 关闭时添加过渡效果
        helpContent.style.transition = 'all 0.2s ease-out';
        links.forEach(link => {
            const linkElement = link as HTMLElement;
            linkElement.style.transition = `
                transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1)
            `;
        });

        // 关闭动画
        clickY = 0;
        // 优化链接消失动画
        const linksArray = Array.from(links).reverse();
        linksArray.forEach((link, index) => {
            const linkElement = link as HTMLElement;
            // 减少动画时间从0.5s到0.3s，减少延迟时间
            linkElement.style.transition = `
                transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) ${index * 0.02}s,
                opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1) ${index * 0.02}s
            `;
            setTimeout(() => {
                linkElement.style.opacity = '0';
                linkElement.style.transform = 'translateY(-20px)';
            }, index * 20); // 从30ms减少到20ms
        });

        // 减少圆形收缩动画的延迟
        setTimeout(() => {
            requestAnimationFrame(() => {
                // 减少动画时间从0.6s到0.4s
                circle.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                circle.style.width = '0px';
                circle.style.height = '0px';
            });
        }, 50); // 从100ms减少到50ms

        // 减少整体动画完成时间
        setTimeout(() => {
            helpContent.style.display = 'none';
            // 移除圆形和遮罩
            const overlay = document.querySelector('.help-overlay');
            if (overlay?.parentNode) {
                document.body.removeChild(overlay);
            }
            if (circle.parentNode) {
                document.body.removeChild(circle);
            }
            isAnimating = false;

            // 动画结束后移除过渡效果
            setTimeout(() => {
                helpContent.style.transition = '';
                links.forEach(link => {
                    (link as HTMLElement).style.transition = '';
                });
            }, Math.max(400, links.length * 40 + 300));
        }, Math.max(400, links.length * 40 + 300)); // 从600ms减少到400ms，链接动画时间也相应减少

    } else {
        // 打开时添加过渡效果
        helpContent.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        links.forEach(link => {
            const linkElement = link as HTMLElement;
            linkElement.style.transition = `
                transform 0.6s cubic-bezier(0.2, 0.8, 0.2, 1),
                opacity 0.6s ease-out
            `;
        });

        // 立即显示内容，但保持透明
        helpContent.style.display = 'block';
        helpContent.style.opacity = '0';

        // 获取并设置 help-container 的透明度
        const helpContainer = document.querySelector('.help-container') as HTMLElement;
        if (helpContainer) {
            helpContainer.style.opacity = '1';
        }

        // 同时开始圆形展开和内容显示
        requestAnimationFrame(() => {
            // 展开圆形
            circle.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            circle.style.width = `${diameter}px`;
            circle.style.height = `${diameter}px`;

            // 同时显示内容
            helpContent.style.opacity = '1';
            helpContent.style.transform = 'scale(1)';

            // 立即开始链接动画
            const linksArray = Array.from(links);
            linksArray.forEach((link, index) => {
                const linkElement = link as HTMLElement;

                // 重置初始状态
                linkElement.style.transition = 'none';
                linkElement.style.opacity = '0';
                linkElement.style.transform = 'translateY(40px)';

                // 强制重排
                linkElement.offsetHeight;

                requestAnimationFrame(() => {
                    // 设置过渡动画
                    linkElement.style.transition = `
                        transform 0.6s cubic-bezier(0.2, 0.8, 0.2, 1) ${index * 0.08}s,
                        opacity 0.6s ease-out ${index * 0.08}s
                    `;
                    linkElement.style.opacity = '1';
                    linkElement.style.transform = 'translateY(0)';
                });
            });

            // 在所有动画完成后重置状态
            setTimeout(() => {
                helpContent.style.transition = '';
                links.forEach(link => {
                    (link as HTMLElement).style.transition = '';
                });
                isAnimating = false;
            }, 600 + links.length * 80);
        });
    }
}

// 初始化事件监听
document.addEventListener('DOMContentLoaded', () => {
    try {
        // 使用更具体的选择器，并立即输出所有匹配的元素
        const allContents = document.querySelectorAll('#help-content, .help-content');

        const helpContent = allContents[ 0 ] as HTMLElement;

        // 只设置初始状态，不设置过渡效果
        helpContent.style.opacity = '0';
        helpContent.style.transform = 'scale(0.95)';
        helpContent.style.display = 'none';

        // 初始化所有链接为隐藏状态
        const links = helpContent.querySelectorAll('a');

        links.forEach((link) => {
            const linkElement = link as HTMLElement;
            linkElement.style.opacity = '0';
            linkElement.style.transform = 'translateY(80px)';
            linkElement.style.display = 'block';
            linkElement.style.willChange = 'transform, opacity';
            linkElement.style.pointerEvents = 'auto';

            // 移除默认的 href 行为
            // link.removeAttribute('target');

            // 修改为只使用一个点击事件处理程序
            link.addEventListener('click', async (e: Event) => {
                e.preventDefault();
                e.stopPropagation();
                const href = link.getAttribute('href');
                if (href) {
                    try {
                        // 添加一个标志来防止重复打开
                        if (!link.hasAttribute('data-opening')) {
                            link.setAttribute('data-opening', 'true');
                            console.log('打开链接:', href);
                            await open_uri(href);
                            await toggleHelpContent();
                            // 操作完成后移除标志
                            link.removeAttribute('data-opening');
                        }
                    } catch (fallbackError) {
                        console.error('打开链接失败:', fallbackError);
                        // 发生错误时也要移除标志
                        link.removeAttribute('data-opening');
                    }
                }
            });

        });


    } catch (error) {
        console.error('帮助功能初始化失败:', error);
    }
});
