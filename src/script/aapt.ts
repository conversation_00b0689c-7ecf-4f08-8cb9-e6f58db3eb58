import { Command } from 'tauri-plugin-shellx-api';
import { platform } from '@tauri-apps/plugin-os';
import { invoke } from '@tauri-apps/api/core';

/**
 * 获取正确的可执行文件路径
 * @param fileName 可执行文件名
 * @returns 完整的可执行文件路径
 */
async function getExecutablePath(fileName: string): Promise<string> {
    try {
        // 开发环境下使用相对路径，生产环境下使用绝对路径
        const isDev = import.meta.env.DEV;
        const currentPlatform = await platform();

        console.log(`[aapt.getExecutablePath] 文件名: ${fileName}, 开发环境: ${isDev}, 平台: ${currentPlatform}`);

        if (isDev) {
            // 开发环境：使用Command.sidecar默认查找机制
            // 在开发环境下，Tauri会自动查找src-tauri/binary目录
            const devPath = currentPlatform === 'windows' ? `${fileName}.exe` : fileName;
            console.log(`[aapt.getExecutablePath] 开发环境路径: ${devPath}`);
            return devPath;
        } else {
            // 生产环境：根据平台使用不同的路径策略
            try {
                const appPath = await invoke('get_app_path') as string;
                console.log(`[aapt.getExecutablePath] 获取到的应用路径: ${appPath}`);

                let fullPath: string;

                if (currentPlatform === 'macos') {
                    // macOS平台：appPath 已经是 MacOS 目录
                    fullPath = `${appPath}/${fileName}`;
                } else if (currentPlatform === 'windows') {
                    // Windows平台：可执行文件与主程序在同一目录下
                    fullPath = `${appPath}/${fileName}.exe`;
                } else {
                    // Linux平台：可执行文件与主程序在同一目录下
                    fullPath = `${appPath}/${fileName}`;
                }

                console.log(`[aapt.getExecutablePath] 生产环境完整路径: ${fullPath}`);
                return fullPath;
            } catch (invokeError) {
                console.warn(`[aapt.getExecutablePath] 调用get_app_path失败: ${invokeError}`);
                // 如果获取应用路径失败，使用Command.sidecar的默认机制
                const fallbackPath = currentPlatform === 'windows' ? `${fileName}.exe` : fileName;
                console.log(`[aapt.getExecutablePath] 使用Command.sidecar默认机制: ${fallbackPath}`);
                return fallbackPath;
            }
        }
    } catch (error) {
        console.error('获取可执行文件路径失败:', error);
        // 最终降级方案：使用Command.sidecar的默认机制
        const currentPlatform = await platform();
        const fallbackPath = currentPlatform === 'windows' ? `${fileName}.exe` : fileName;
        console.log(`[aapt.getExecutablePath] 最终降级方案路径: ${fallbackPath}`);
        return fallbackPath;
    }
}

/**
 * 编译点9图
 * @param file 需要编译的文件路径
 * @returns 返回编译结果
 */
export async function aapt(file: string) {
    try {
        const aaptPath = await getExecutablePath('aapt');
        const command = Command.sidecar(aaptPath, [ "s", "-i", file, "-o", file ]);
        const result = await command.execute();
        return result;
    } catch (error) {
        console.error('aapt命令执行失败:', error);
        throw error;
    }
}