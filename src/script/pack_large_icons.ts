import { messageSuccess, messageError, messageLoading, closeLoading } from './prompt_message';
import { invoke } from '@tauri-apps/api/core';
import { 打开目录或文件 } from './file_operations';
import { setContainerAni, setDeviceAni, setBigIconAni } from './ui_animation';
import { exists } from '@tauri-apps/plugin-fs';
import { join } from '@tauri-apps/api/path';

/**
 * 检测是否为大图标目录
 * @param dirPath 目录路径
 * @returns 是否为大图标目录
 */
async function 检测大图标目录(dirPath: string): Promise<boolean> {
    try {
        const pickerPath = await join(dirPath, 'Picker所需资源');
        const desktopPath = await join(dirPath, '桌面所需资源');

        const hasPicker = await exists(pickerPath);
        const hasDesktop = await exists(desktopPath);

        return hasPicker && hasDesktop;
    } catch (error) {
        console.error('检测大图标目录失败:', error);
        return false;
    }
}

/**
 * 检测拖入路径类型（只检测，不处理）
 * @param draggedPaths 拖入的路径数组
 * @returns 检测结果信息
 */
async function 检测拖入路径类型(draggedPaths: string[]): Promise<{
    type: 'single_parent' | 'single_largeicon' | 'multiple_largeicons';
    validLargeIconDirs: string[];
    message: string;
}> {
    if (draggedPaths.length === 1) {
        const singlePath = draggedPaths[ 0 ];

        // 检查是否为单个大图标目录
        if (await 检测大图标目录(singlePath)) {
            return {
                type: 'single_largeicon',
                validLargeIconDirs: [ singlePath ],
                message: `检测到单个大图标目录: ${singlePath.split(/[/\\]/).pop()}`
            };
        } else {
            return {
                type: 'single_parent',
                validLargeIconDirs: [],
                message: '检测到包含大图标的父目录'
            };
        }
    } else {
        // 多个路径，检测哪些是大图标目录
        const validLargeIconDirs: string[] = [];

        for (const path of draggedPaths) {
            if (await 检测大图标目录(path)) {
                validLargeIconDirs.push(path);
            }
        }

        if (validLargeIconDirs.length === 0) {
            throw new Error('未检测到有效的大图标目录。大图标目录应包含"Picker所需资源"和"桌面所需资源"文件夹。');
        }

        return {
            type: 'multiple_largeicons',
            validLargeIconDirs,
            message: `检测到 ${validLargeIconDirs.length} 个大图标目录`
        };
    }
}

/**
 * 智能处理拖入路径（用户确认后才执行）
 * @param detectionResult 检测结果
 * @returns 处理后的路径和输出基础路径
 */
async function 执行路径处理(detectionResult: {
    type: 'single_parent' | 'single_largeicon' | 'multiple_largeicons';
    validLargeIconDirs: string[];
    message: string;
}, originalPaths: string[]): Promise<{ processedPath: string, outputBasePath: string }> {
    try {
        if (detectionResult.type === 'single_parent') {
            // 使用现有逻辑，直接返回原路径
            return {
                processedPath: originalPaths[ 0 ],
                outputBasePath: originalPaths[ 0 ]
            };
        } else if (detectionResult.type === 'single_largeicon') {
            // 单个大图标目录，创建临时父目录结构
            const singlePath = detectionResult.validLargeIconDirs[ 0 ];
            const tempDir = await invoke<string>('创建临时目录', { prefix: 'single_large_icon' });
            const dirName = singlePath.split(/[/\\]/).pop() || 'largeicon';
            const targetDir = await join(tempDir, dirName);
            await invoke('复制目录', { from: singlePath, to: targetDir });

            // 返回临时目录作为处理路径，但输出基础路径使用原始目录的父目录
            const originalParent = singlePath.split(/[/\\]/).slice(0, -1).join('/') || singlePath;
            return {
                processedPath: tempDir,
                outputBasePath: originalParent
            };
        } else {
            // 多个大图标目录，创建临时整合目录
            const tempDir = await invoke<string>('创建临时目录', { prefix: 'multiple_large_icons' });

            for (const dirPath of detectionResult.validLargeIconDirs) {
                const dirName = dirPath.split(/[/\\]/).pop() || 'unknown';
                const targetDir = await join(tempDir, dirName);
                await invoke('复制目录', { from: dirPath, to: targetDir });
            }

            // 使用第一个目录的父目录作为输出基础路径
            const firstPath = detectionResult.validLargeIconDirs[ 0 ];
            const firstParent = firstPath.split(/[/\\]/).slice(0, -1).join('/') || firstPath;
            return {
                processedPath: tempDir,
                outputBasePath: firstParent
            };
        }
    } catch (error) {
        console.error('执行路径处理失败:', error);
        throw error;
    }
}

/**
 * 大图标打包
 * @param draggedPaths 拖入的路径数组
 * @returns 无返回值,打包结果通过消息提示显示
 */
export default async function 打包大图标(draggedPaths: string[]) {
    let detectionResult: any = null;

    try {
        // 第一步：检测拖入路径类型（不执行任何文件操作）
        detectionResult = await 检测拖入路径类型(draggedPaths);

        // 显示检测结果，3秒后自动消失
        messageLoading(detectionResult.message, 3000);

        // 获取对话框元素
        const dialog = document.getElementById('bigIconPackageDialog');
        if (!dialog) {
            throw new Error('找不到对话框元素');
        }

        // 从 localStorage 获取上次的输入值
        const lastInputs = {
            designer: localStorage.getItem('bigicon_designer') || '',
            title: localStorage.getItem('bigicon_title') || '',
            description: localStorage.getItem('bigicon_description') || '',
            uiVersion: localStorage.getItem('bigicon_uiVersion') || '14',
            roundedCorner: localStorage.getItem('bigicon_roundedCorner') !== 'false',
            officialIcons: localStorage.getItem('bigicon_officialIcons') === 'true'
        };

        // 设置上次的输入值
        (document.getElementById('designer') as HTMLInputElement).value = lastInputs.designer;
        (document.getElementById('title') as HTMLInputElement).value = lastInputs.title;
        (document.getElementById('description') as HTMLTextAreaElement).value = lastInputs.description;
        (document.getElementById('uiVersion') as HTMLInputElement).value = lastInputs.uiVersion;
        (document.getElementById('roundedCorner') as HTMLInputElement).checked = lastInputs.roundedCorner;
        (document.getElementById('officialIcons') as HTMLInputElement).checked = lastInputs.officialIcons;

        // 绑定按钮事件
        const cancelBtn = document.getElementById('cancelBtn') as HTMLButtonElement;
        const confirmBtn = document.getElementById('confirmBtn') as HTMLButtonElement;

        // 取消 - 不需要清理临时文件，因为还没创建
        cancelBtn.onclick = () => {
            // 关闭可能存在的 loading 消息
            closeLoading();
            // 显示container
            setContainerAni(1, 1);
            // 显示设备容器
            setDeviceAni(1)
            // 隐藏大图标容器
            setBigIconAni(0.9, 0)
        };

        // 确认
        confirmBtn.onclick = async () => {
            let tempDir: string | null = null;

            try {
                // 关闭之前的检测消息
                closeLoading();
                messageLoading('正在处理大图标...')

                const designer = (document.getElementById('designer') as HTMLInputElement).value || '小米主题';
                const title = (document.getElementById('title') as HTMLInputElement).value || '未命名套装';
                const description = (document.getElementById('description') as HTMLTextAreaElement).value || '暂无介绍';
                const uiVersion = (document.getElementById('uiVersion') as HTMLInputElement).value || '14';
                const roundedCorner = ((document.getElementById('roundedCorner') as HTMLInputElement).checked).toString();
                const officialIcons = ((document.getElementById('officialIcons') as HTMLInputElement).checked).toString();

                // 保存输入值到 localStorage
                localStorage.setItem('bigicon_designer', designer);
                localStorage.setItem('bigicon_title', title);
                localStorage.setItem('bigicon_description', description);
                localStorage.setItem('bigicon_uiVersion', uiVersion);
                localStorage.setItem('bigicon_roundedCorner', roundedCorner);
                localStorage.setItem('bigicon_officialIcons', officialIcons);

                // 第二步：用户确认后才执行文件处理
                const { processedPath, outputBasePath } = await 执行路径处理(detectionResult, draggedPaths);

                // 如果创建了临时目录，记录下来以便后续清理
                if (processedPath !== draggedPaths[ 0 ]) {
                    tempDir = processedPath;
                }

                messageLoading('正在打包大图标...')

                const outputDir = await invoke<string>('打包大图标', {
                    pathfile: processedPath,
                    designer,
                    title,
                    description,
                    uiVersion,
                    roundedCorner,
                    officialIcons
                });

                // 如果使用了临时目录，需要将输出移动到正确位置
                let finalOutputDir = outputDir;
                if (tempDir && outputBasePath !== processedPath) {
                    const tempOutputName = outputDir.split(/[/\\]/).pop() || '🚚MTZ';
                    finalOutputDir = await join(outputBasePath, tempOutputName);

                    try {
                        await invoke('复制目录', { from: outputDir, to: finalOutputDir });
                    } catch (error) {
                        console.error('移动输出目录失败:', error);
                        // 如果移动失败，使用原始输出目录
                        finalOutputDir = outputDir;
                    }
                }

                // 打开输出目录并显示详细信息
                try {
                    await 打开目录或文件(finalOutputDir);
                    messageSuccess(`大图标打包成功！`, 2000);
                } catch (error) {
                    messageSuccess(`大图标打包成功！\n文件保存在: ${finalOutputDir}`, 3000);
                }

                // 显示container
                setContainerAni(1, 1);
                // 显示设备容器
                setDeviceAni(1)
                // 隐藏大图标容器
                setBigIconAni(0.8, 0)

            } catch (error) {
                closeLoading();
                messageError(`大图标打包失败: ${error}`);
            } finally {
                // 清理临时目录
                if (tempDir) {
                    invoke('删除目录文件夹', { path: tempDir }).catch(console.error);
                }
            }
        };

    } catch (error) {
        closeLoading();
        messageError(`大图标打包失败: ${error}`)
    }
}
