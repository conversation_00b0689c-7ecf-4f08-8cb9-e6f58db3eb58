
import { Command } from 'tauri-plugin-shellx-api';
import { handleTauriError } from './tauri_error_handler';

/**
 * 自定义中断错误类
 */
export class AbortError extends Error {
    constructor(message: string = 'Operation aborted') {
        super(message);
        this.name = 'AbortError';
    }
}

/**
 * 全局命令管理器，用于处理页面重新加载时的清理
 */
class CommandManager {
    private activeCommands: Set<Command<string>> = new Set();
    private isCleaningUp: boolean = false;

    constructor() {
        // 监听页面卸载事件
        if (typeof window !== 'undefined') {
            window.addEventListener('beforeunload', () => {
                this.cleanupAll();
            });

            // 监听页面隐藏事件（移动端）
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    this.cleanupAll();
                }
            });
        }
    }

    addCommand(command: Command<string>): void {
        this.activeCommands.add(command);
    }

    removeCommand(command: Command<string>): void {
        this.activeCommands.delete(command);
    }

    cleanupAll(): void {
        if (this.isCleaningUp) return;
        this.isCleaningUp = true;

        console.log('清理所有活动命令...');
        this.activeCommands.forEach(command => {
            try {
                command.removeAllListeners();
            } catch (error) {
                // 忽略清理时的错误
                console.warn('清理命令时出错:', error);
            }
        });
        this.activeCommands.clear();
        this.isCleaningUp = false;
    }
}

// 全局命令管理器实例
const commandManager = new CommandManager();

/**
 * 根据平台执行命令行脚本
 * @param script 要执行的脚本内容
 * @param signal 可选的中断信号
 * @param onOutput 可选的实时输出回调函数
 * @returns 返回命令执行结果
 */
export async function executeCommand(
    script: string,
    signal?: AbortSignal,
    onOutput?: (output: string) => void
): Promise<string> {
    try {
        // 检查是否已中断
        if (signal?.aborted) {
            throw new AbortError();
        }

        let command: Command<string>;

        // 根据平台创建对应的Command对象
        switch (window.platform) {
            case 'windows':
                // 使用powershell以隐藏窗口方式执行命令
                command = new Command('powershell.exe', [ '-WindowStyle', 'Hidden', '-Command', script ]);
                break;
            case 'macos':
            case 'linux':
                command = new Command('bash', [ '-c', script ]);
                break;
            default:
                throw new Error('不支持的操作系统平台');
        }

        // 如果有中断信号或需要实时输出，使用spawn方式执行
        if (signal || onOutput) {
            return await executeCommandWithAbort(command, script, signal, onOutput);
        } else {
            // 没有中断信号且不需要实时输出，使用原来的execute方式
            // console.log('执行命令:', script);
            const output = await command.execute();
            // console.log('命令执行结果:', output);
            return output.stdout.trim();
        }
    } catch (error) {
        // 区分中断和真正的错误
        if (error instanceof AbortError) {
            console.log('命令执行被用户中断');
        } else {
            console.error('命令执行失败:', error);
        }
        throw error;
    }
}

/**
 * 支持中断的命令执行
 * @param command 命令对象
 * @param script 脚本内容（用于日志）
 * @param signal 中断信号
 * @param onOutput 可选的实时输出回调函数
 * @returns 命令输出
 */
async function executeCommandWithAbort(
    command: Command<string>,
    script: string,
    signal?: AbortSignal,
    onOutput?: (output: string) => void
): Promise<string> {
    return new Promise(async (resolve, reject) => {
        try {
            // 检查是否已中断
            if (signal?.aborted) {
                reject(new AbortError());
                return;
            }

            let stdout = '';
            let stderr = '';
            let child: any = null;

            // 监听中断信号
            const abortHandler = async () => {
                console.log('收到中断信号，正在终止命令:', script);
                if (child) {
                    try {
                        // 尝试优雅终止
                        await child.kill();
                        console.log('命令已终止:', script);

                        // 等待一小段时间确保进程真正终止
                        await new Promise(resolve => setTimeout(resolve, 100));

                    } catch (error) {
                        console.error('终止命令失败:', error);

                        // 如果优雅终止失败，尝试强制终止
                        try {
                            await child.kill('SIGKILL');
                            console.log('强制终止命令成功:', script);
                        } catch (forceError) {
                            console.error('强制终止命令也失败:', forceError);
                        }
                    }
                }
                reject(new AbortError());
            };

            if (signal) {
                signal.addEventListener('abort', abortHandler);
            }

            // 将命令添加到管理器
            commandManager.addCommand(command);

            // 清理函数
            const cleanup = () => {
                if (signal) {
                    signal.removeEventListener('abort', abortHandler);
                }
                // 从管理器中移除命令
                commandManager.removeCommand(command);
                // 移除所有事件监听器，防止内存泄漏
                try {
                    command.removeAllListeners();
                } catch (e) {
                    // 忽略清理时的错误
                    console.warn('清理命令监听器时出错:', e);
                }
            };

            // 设置事件监听器
            command.on('close', (data) => {
                cleanup();
                if (signal?.aborted) {
                    // 如果是中断导致的关闭，不要报告为错误
                    reject(new AbortError());
                } else if (data.code === 0) {
                    resolve(stdout.trim());
                } else {
                    reject(new Error(`命令执行失败，退出码: ${data.code}, 错误输出: ${stderr}`));
                }
            });

            command.on('error', (error) => {
                cleanup();
                if (signal?.aborted) {
                    // 如果是中断导致的错误，不要报告为执行错误
                    reject(new AbortError());
                } else {
                    // 使用专门的 Tauri 错误处理器
                    const shouldIgnore = handleTauriError(error, 'command execution');
                    if (shouldIgnore) {
                        console.warn('检测到可忽略的 Tauri 错误，当作中断处理');
                        reject(new AbortError()); // 当作中断处理
                    } else {
                        reject(new Error(`命令执行错误: ${error}`));
                    }
                }
            });

            command.stdout.on('data', (line) => {
                stdout += line;
                // 如果有实时输出回调，调用它
                if (onOutput) {
                    onOutput(line);
                }
            });

            command.stderr.on('data', (line) => {
                stderr += line;
            });

            // 启动命令
            // console.log('执行命令（支持中断）:', script);
            child = await command.spawn();

        } catch (error) {
            // 清理资源
            if (signal) {
                try {
                    // 移除所有可能的监听器
                    signal.removeEventListener('abort', () => { });
                } catch (e) {
                    // 忽略清理时的错误
                }
            }
            try {
                if (command) {
                    commandManager.removeCommand(command);
                    command.removeAllListeners();
                }
            } catch (cleanupError) {
                console.warn('清理命令对象时出错:', cleanupError);
            }

            // 使用专门的 Tauri 错误处理器
            const shouldIgnore = handleTauriError(error, 'command spawn');
            if (shouldIgnore) {
                console.warn('检测到可忽略的 Tauri 错误，当作中断处理');
                reject(new AbortError());
            } else {
                reject(error);
            }
        }
    });
}

