import { messageError, messageSuccess, messageLoading } from './prompt_message';
import { invoke } from '@tauri-apps/api/core';
import { 打开目录或文件 } from './file_operations';
export async function 图片裁切(filePath: string) {
    try {
        messageLoading('图片裁切中...');
        const result = await invoke<string>('图片裁切', { imagePath: filePath });
        console.log(result, '-----------******************');
        打开目录或文件(result);
        messageSuccess('图片裁切成功');
    } catch (error) {
        console.error('图片裁切失败:', error);
        messageError(`${error}`);
    }
}

