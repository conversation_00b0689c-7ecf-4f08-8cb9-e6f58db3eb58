import { messageSuccess, messageError, messageLoading } from './prompt_message';
import { getDevices } from './commands';
import { executeCommand } from './terminal_commands';
import { open_uri } from './file_operations';
import { invoke } from "@tauri-apps/api/core";

declare global {
    interface Window {
        投屏: () => Promise<void>;
    }
}
/**
* 查找apktool路径
*/
async function findScrcpyPath(): Promise<string> {
    try {
        const path = await invoke<string>('find_tool_path', { toolName: 'scrcpy' });
        return path;
    } catch (error) {
        console.error('查找apktool路径失败:', error);
        return '';
    }
}
export async function 投屏() {
    try {
        messageLoading('正在启动投屏...');
        let scrcpyPath: string = '';
        try {
            scrcpyPath = await findScrcpyPath()
            if (!scrcpyPath) {
                open_uri('https://github.com/Genymobile/scrcpy');
                messageError('scrcpy 未安装，请先安装 scrcpy。如果你知道什么是包管理器，建议通过包管理器进行安装。');
                return;
            }
        } catch (error) {
            messageError('获取 scrcpy 路径失败');
            return;
        }

        try {
            const devices = await getDevices();
            // 收集所有设备的投屏结果
            const results = await Promise.all(devices.map(async (device) => {
                try {
                    // 执行命令并获取结果
                    executeCommand(`${scrcpyPath} -s ${device.id}`);
                    return {
                        success: true,
                        device,
                        message: `设备 ${device.model} (${device.id}) 投屏成功`
                    };
                } catch (error) {
                    console.error(`设备 ${device.model} (${device.id}) 投屏失败:`, error);
                    return {
                        success: false,
                        device,
                        message: `设备 ${device.model} (${device.id}) 投屏失败：${error}`
                    };
                }
            }));

            await new Promise(resolve => setTimeout(resolve, 500));

            // 统计成功和失败数量
            const successCount = results.filter(r => r.success).length;
            const failCount = results.filter(r => !r.success).length;

            // 打印成功结果
            results.filter(r => r.success).forEach(r => {
                console.log(r.message);
            });

            // 打印失败结果
            results.filter(r => !r.success).forEach(r => {
                console.error(r.message);
            });

            // 显示总体结果
            if (failCount === 0) {
                messageSuccess(`投屏成功`);
            } else if (successCount === 0) {
                messageError(`投屏失败`);
            } else {
                messageSuccess(`${successCount}个设备投屏成功，${failCount}个设备失败`);
            }

        } catch (error) {
            messageError(`投屏启动失败: ${error}`);
        }
    } catch (error) {
        console.error('投屏过程发生错误:', error);
        messageError(`投屏失败: ${error}`);
    }
}

// 将函数暴露给全局
window.投屏 = 投屏;