import { messageSuccess, messageError, messageLoading, messageWarning } from './prompt_message';
import { checkAndSelectDeviceGeneral } from './commands';
import { 打开目录或文件 } from './file_operations';
import { homeDir, join } from '@tauri-apps/api/path';
import { invoke } from '@tauri-apps/api/core';
import { 提取apk } from './extract_apk';

declare global {
    interface Window {
        提取启动项: () => Promise<void>;
    }
}

// 使用 invoke 调用 Rust 端创建目录
async function ensureDir(path: string) {
    await invoke('创建目录', { path });
}

/**
 * 提取启动项
 * 从AndroidManifest.xml中提取应用的包名和所有Activity及activity-alias类名
 * 包括:
 * 1. 提取apk并反编译
 * 2. 读取AndroidManifest.xml文件
 * 3. 提取包名和所有Activity及activity-alias类名
 * 4. 处理类名格式(补全包名)
 * @returns 无返回值,提取结果保存到桌面启动项文件夹，并在提取完成后打开文件夹
 */

export async function 提取启动项() {

    try {
        messageLoading('正在提取启动项...');

        // 设备检查和选择
        const selectedDevice = await checkAndSelectDeviceGeneral('选择设备进行启动项提取');
        if (!selectedDevice) {
            messageWarning('未选择设备，操作已取消');
            return;
        }

        // 解析 AndroidManifest.xml 获取包名和类名
        const apkPath = await 提取apk(true);
        if (!apkPath) {
            throw new Error('该 apk 反编译失败');
        }
        messageLoading('正在提取启动项...');

        // 确保apkPath是字符串类型
        const apkPathString = Array.isArray(apkPath) ? apkPath[ 0 ] : apkPath;
        const manifestPath = await join(apkPathString.replace('.apk', ''), 'AndroidManifest.xml');
        const manifestContent = await invoke<string>('read_file', { path: manifestPath });

        // 提取包名
        const packageMatch = manifestContent.match(/package="([^"]+)"/);
        if (!packageMatch) {
            throw new Error('未找到包名');
        }
        const packageName = packageMatch[ 1 ];
        console.log('提取的包名:', packageName);

        // 提取所有Activity和activity-alias
        const classNames: string[] = [];

        // 提取所有Activity
        const activityMatches = manifestContent.matchAll(/<activity[^>]*?android:name\s*=\s*"([^"]+)"[^>]*?>/g);
        for (const match of activityMatches) {
            let className = match[ 1 ];
            if (className.startsWith('.')) {
                className = packageName + className;
            } else if (!className.includes('.')) {
                className = packageName + '.' + className;
            }
            classNames.push(className);
        }

        // 提取所有activity-alias
        const aliasMatches = manifestContent.matchAll(/<activity-alias[^>]*?android:name\s*=\s*"([^"]+)"[^>]*?>/g);
        for (const match of aliasMatches) {
            let className = match[ 1 ];
            if (className.startsWith('.')) {
                className = packageName + className;
            } else if (!className.includes('.')) {
                className = packageName + '.' + className;
            }
            classNames.push(className);
        }

        if (classNames.length === 0) {
            throw new Error('未找到任何Activity');
        }

        console.log('提取的Activity列表:', classNames);
        // 将截图拉取到本地桌面
        const homeDirPath = await homeDir();
        const desktopPath = await join(homeDirPath, 'Desktop');
        const startupPath = await join(desktopPath, '启动项');
        const pngName = '/sdcard/.miniEditorScreenCap.0';
        // 确保目录存在,如果存在则清空
        await ensureDir(startupPath);

        // 调用 adb 命令遍历启动项
        const { adb } = await import('./commands');
        for (const className of classNames) {
            try {
                // 尝试启动界面
                const startResult = await adb([ '-s', selectedDevice.id, 'shell', 'am', 'start', '-n', `${packageName}/${className}` ]);

                // 检查启动结果
                const isStartSuccess = !startResult.includes('Error') && !startResult.includes('Exception');

                // 如果启动失败则跳过
                if (!isStartSuccess) {
                    console.log(`${className} 启动失败，跳过截图`);
                    continue;
                }

                // 等待界面加载
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 只对成功启动的界面截图
                await adb([ '-s', selectedDevice.id, 'shell', 'screencap', '-p', pngName ]);

                const localPath = await join(startupPath, `${packageName},${className}.png`);
                await adb([ '-s', selectedDevice.id, 'pull', pngName, localPath ]);

                console.log(`${className} 启动成功，已保存截图: ${localPath}`);

            } catch (err) {
                console.error(`处理 ${className} 时发生错误:`, err);
                continue;
            }
        }
        messageSuccess(`提取启动项成功`);
        await 打开目录或文件(startupPath);
    } catch (error) {
        console.error('解析 AndroidManifest.xml 失败:', error);
        messageError(`提取启动项失败: ${error}`);
        return;
    }

}

// 将函数暴露给全局
window.提取启动项 = 提取启动项;
