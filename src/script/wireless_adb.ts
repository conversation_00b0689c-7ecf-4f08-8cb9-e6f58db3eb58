declare global {
    interface Window {
        无线adb: () => Promise<void>;
    }
}

import { messageSuccess, messageError, messageLoading } from './prompt_message';
import { adb_all } from './commands';

type AdbResult = { [ key: string ]: unknown };

async function extractIpAddress(result: AdbResult): Promise<string> {
    // 获取第一个设备的输出
    const firstDevice = Object.values(result)[ 0 ] as string;
    const ipRegex = /\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/;
    const match = firstDevice.match(ipRegex);
    if (match) return match[ 0 ];
    throw ('设备WiFi未开启');
}

/**
 * 验证设备是否已连接
 * @param ip 设备IP地址
 */
async function verifyConnection(ip: string): Promise<boolean> {
    try {
        const devices = await adb_all([ 'devices' ], true);
        const deviceList = Object.values(devices)[ 0 ] as string;
        return deviceList.includes(`${ip}:5555`);
    } catch (error) {
        return false;
    }
}

/**
 * 从adb命令输出中提取IP地址，并开启无线调试
 * @throws 如果设备WiFi未开启,则抛出错误
 */
export default async function 无线adb() {
    messageLoading('正在获取设备IP地址...');
    let result: AdbResult;
    const MAX_RETRIES = 3;
    let retryCount = 0;

    // 获取设备IP地址
    try {
        result = await adb_all([ '-d', 'shell', 'ip', 'addr', 'show', 'wlan0' ], true);
        if (!result) {
            throw new Error('获取IP地址返回结果为空');
        }
    } catch (error) {
        messageError('获取设备IP地址失败，请确保设备连接同一WIFI');
        return;
    }

    try {
        const ip = await extractIpAddress(result);

        while (retryCount < MAX_RETRIES) {
            console.log(`尝试开启无线调试，第 ${retryCount + 1} 次尝试`);
            await adb_all([ '-d', 'tcpip', '5555' ]);
            await adb_all([ '-d', 'connect', `${ip}:5555` ]);

            // 等待连接建立
            await new Promise(resolve => setTimeout(resolve, 1000));

            if (await verifyConnection(ip)) {
                messageSuccess(`成功开启无线调试，设备IP: ${ip}`);
                return;
            }

            retryCount++;
            if (retryCount < MAX_RETRIES) {
                console.log('连接验证失败，准备重试...');
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }

        throw new Error(`多次尝试后仍未能成功连接设备`);
    } catch (error) {
        messageError(`连接设备失败: ${error instanceof Error ? error.message : String(error)}`);
    }
}

window.无线adb = 无线adb;
