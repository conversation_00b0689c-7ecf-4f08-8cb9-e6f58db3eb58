/**
    在mac os系统中添加环境变量，具体步骤如下
    1.检查当前终端使用的是哪个环境
    2.检查对应环境的配置文件中是否已经存在环境变量配置，如果存在，则直接使用
    3.如果不存在，则创建一个
    4.环境文件存储在 应用的/Applications/mini-editor-pro.app/Contents/Resources/Terminal 目录下
    5.重启终端 source ~/.zshrc 或者 source ~/.bash_profile 或者 ~/.bashrc
    6.检查环境变量是否配置成功
 */

import { executeCommand } from './terminal_commands';
import { invoke } from '@tauri-apps/api/core';

// 环境变量配置类型
interface EnvironmentConfig {
    shell: 'zsh' | 'bash' | 'unknown';
    configFile: string;
    terminalPath: string;
    isConfigured: boolean;
}

// 动态获取应用的工具路径（不再使用固定路径）

/**
 * 动态获取当前应用的工具路径
 * @returns 包含终端工具和可执行文件的路径数组
 */
async function getToolsPaths(): Promise<{ terminalPath: string; executablePath: string; allPaths: string[] }> {
    try {

        // 使用 Tauri API 获取当前应用路径
        const appPath = await invoke('get_app_path') as string;

        let terminalPath: string;
        let executablePath: string;

        // 根据平台构建正确的工具路径
        if (appPath.includes('.app/Contents/MacOS')) {
            // macOS 应用包结构: xxx.app/Contents/MacOS/mini-editor-pro
            // 终端工具在: xxx.app/Contents/Resources/Terminal
            // 可执行文件在: xxx.app/Contents/MacOS (adb, aapt)
            terminalPath = appPath.replace('/Contents/MacOS', '/Contents/Resources/Terminal');
            executablePath = appPath; // MacOS目录本身
        } else if (appPath.endsWith('.app')) {
            // 如果返回的是 .app 目录本身
            terminalPath = `${appPath}/Contents/Resources/Terminal`;
            executablePath = `${appPath}/Contents/MacOS`;
        } else {
            // 开发环境或其他情况
            terminalPath = `${appPath}/Terminal`;
            executablePath = appPath;
        }

        const allPaths = [ terminalPath, executablePath ];


        // 检查路径是否存在
        for (const path of allPaths) {
            try {
                await executeCommand(`test -d "${path}"`);
            } catch {
                console.warn(`[终端安装器] 警告: 工具目录不存在: ${path}`);
                console.warn('[终端安装器] 将使用此路径进行配置，但可能需要构建应用后才能正常工作');
            }
        }

        return { terminalPath, executablePath, allPaths };
    } catch (error) {
        console.error('[终端安装器] 获取应用路径失败:', error);
        const defaultTerminalPath = '/Applications/mini-editor-pro.app/Contents/Resources/Terminal';
        const defaultExecutablePath = '/Applications/mini-editor-pro.app/Contents/MacOS';
        return {
            terminalPath: defaultTerminalPath,
            executablePath: defaultExecutablePath,
            allPaths: [ defaultTerminalPath, defaultExecutablePath ]
        };
    }
}

/**
 * 步骤1：检查当前终端使用的是哪个环境
 * @returns 当前使用的shell类型
 */
async function detectShellEnvironment(): Promise<'zsh' | 'bash' | 'unknown'> {
    try {

        // 首先尝试通过 $SHELL 环境变量检测
        const shellPath = await executeCommand('echo $SHELL');

        if (shellPath.includes('zsh')) {
            return 'zsh';
        } else if (shellPath.includes('bash')) {
            return 'bash';
        }

        // 如果 $SHELL 检测失败，尝试其他方法
        try {
            await executeCommand('echo $ZSH_VERSION');
            return 'zsh';
        } catch {
            // ZSH_VERSION不存在，继续检查bash
        }

        try {
            await executeCommand('echo $BASH_VERSION');
            return 'bash';
        } catch {
            // 都检测不到
        }

        console.warn('[终端安装器] 无法检测到已知的shell环境');
        return 'unknown';
    } catch (error) {
        console.error('[终端安装器] 检测shell环境时发生错误:', error);
        return 'unknown';
    }
}

/**
 * 步骤2：根据shell类型确定对应的配置文件路径
 * @param shell shell类型
 * @returns 配置文件路径
 */
function getConfigFilePath(shell: 'zsh' | 'bash' | 'unknown'): string {
    switch (shell) {
        case 'zsh':
            return '~/.zshrc';
        case 'bash':
            // bash优先使用.bash_profile，如果不存在则使用.bashrc
            return '~/.bash_profile';
        default:
            // 未知环境默认尝试.zshrc（macOS默认）
            return '~/.zshrc';
    }
}

/**
 * 检查bash的备用配置文件
 * @returns 实际存在的bash配置文件路径
 */
async function getBashConfigFile(): Promise<string> {
    try {
        // 检查.bash_profile是否存在
        await executeCommand('test -f ~/.bash_profile');
        return '~/.bash_profile';
    } catch {
        // .bash_profile不存在，检查.bashrc
        try {
            await executeCommand('test -f ~/.bashrc');
            return '~/.bashrc';
        } catch {
            // 都不存在，返回.bash_profile（将会被创建）
            return '~/.bash_profile';
        }
    }
}

/**
 * 步骤3：检查配置文件中是否已经存在环境变量配置
 * @param configFile 配置文件路径
 * @param allPaths 所有需要检查的路径
 * @returns 是否已配置
 */
async function checkEnvironmentConfiguration(configFile: string, allPaths: string[]): Promise<boolean> {
    try {

        // 检查配置文件是否存在
        try {
            await executeCommand(`test -f ${configFile}`);
        } catch {
            return false;
        }

        // 读取配置文件内容并检查是否包含所有路径
        const fileContent = await executeCommand(`cat ${configFile}`);

        let allConfigured = true;
        for (const path of allPaths) {
            if (!fileContent.includes(path)) {
                allConfigured = false;
            } else {
            }
        }

        if (allConfigured) {
            return true;
        } else {
            return false;
        }
    } catch (error) {
        console.error('[终端安装器] 检查环境变量配置时发生错误:', error);
        return false;
    }
}

/**
 * 步骤4：在配置文件中添加环境变量配置
 * @param configFile 配置文件路径
 * @param allPaths 所有需要添加的路径
 * @returns 是否添加成功
 */
async function addEnvironmentConfiguration(configFile: string, allPaths: string[]): Promise<boolean> {
    try {

        // 构建所有路径的配置内容
        const pathsString = allPaths.join(':');
        const configContent = `\n# mini-editor-pro 工具路径 (终端脚本 + adb/aapt)\nexport PATH="$PATH:${pathsString}"\n`;


        // 使用echo命令追加配置到文件
        await executeCommand(`echo '${configContent}' >> ${configFile}`);

        return true;
    } catch (error) {
        console.error('[终端安装器] 添加环境变量配置时发生错误:', error);
        return false;
    }
}

/**
 * 步骤5：重新加载配置文件使环境变量生效
 * @param configFile 配置文件路径
 * @returns 是否重载成功
 */
async function reloadConfiguration(configFile: string): Promise<boolean> {
    try {

        // 执行source命令重载配置
        await executeCommand(`source ${configFile}`);

        return true;
    } catch (error) {
        console.error('[终端安装器] 重载配置文件时发生错误:', error);
        // source命令可能在当前执行环境中不生效，但配置已写入文件
        return true;
    }
}

/**
 * 步骤6：验证环境变量是否配置成功
 * @returns 验证结果
 */
async function verifyInstallation(): Promise<{
    themetools: boolean;
    videowallpaper: boolean;
    adb: boolean;
    aapt: boolean;
}> {

    const result = {
        themetools: false,
        videowallpaper: false,
        adb: false,
        aapt: false
    };

    const tools = [
        { name: 'themetools', key: 'themetools' as keyof typeof result },
        { name: 'videowallpaper', key: 'videowallpaper' as keyof typeof result },
        { name: 'adb', key: 'adb' as keyof typeof result },
        { name: 'aapt', key: 'aapt' as keyof typeof result }
    ];

    for (const tool of tools) {
        try {
            await executeCommand(`which ${tool.name}`);
            result[ tool.key ] = true;
        } catch {
        }
    }

    return result;
}

/**
 * 获取完整的环境配置信息
 * @returns 环境配置对象
 */
export async function getEnvironmentConfig(): Promise<EnvironmentConfig> {

    const shell = await detectShellEnvironment();
    let configFile = getConfigFilePath(shell);

    // 如果是bash，需要进一步确定实际配置文件
    if (shell === 'bash') {
        configFile = await getBashConfigFile();
    }

    const { terminalPath, allPaths } = await getToolsPaths();
    const isConfigured = await checkEnvironmentConfiguration(configFile, allPaths);

    const config: EnvironmentConfig = {
        shell,
        configFile,
        terminalPath,
        isConfigured
    };

    return config;
}

/**
 * 主安装函数：完整的环境变量安装流程
 * @returns 安装结果
 */
export async function installTerminalEnvironment(): Promise<{
    success: boolean;
    message: string;
    config: EnvironmentConfig;
    verification?: { themetools: boolean; videowallpaper: boolean; adb: boolean; aapt: boolean };
}> {

    try {
        // 获取环境配置
        const config = await getEnvironmentConfig();
        const { allPaths } = await getToolsPaths();

        // 如果已经配置，直接返回
        if (config.isConfigured) {
            const verification = await verifyInstallation();
            return {
                success: true,
                message: '环境变量已配置，无需重复安装',
                config,
                verification
            };
        }

        // 执行安装步骤

        // 添加环境变量配置
        const addSuccess = await addEnvironmentConfiguration(config.configFile, allPaths);
        if (!addSuccess) {
            return {
                success: false,
                message: '添加环境变量配置失败',
                config
            };
        }

        // 重载配置文件
        await reloadConfiguration(config.configFile);

        // 验证安装结果
        const verification = await verifyInstallation();

        // 更新配置状态
        config.isConfigured = true;

        const allToolsFound = verification.themetools && verification.videowallpaper && verification.adb && verification.aapt;
        const message = allToolsFound
            ? '环境变量安装成功，终端工具已可用'
            : '环境变量已添加到配置文件，请重启终端使其完全生效';


        return {
            success: true,
            message,
            config,
            verification
        };

    } catch (error) {
        console.error('[终端安装器] 安装过程中发生错误:', error);

        const defaultPath = await getToolsPaths();
        return {
            success: false,
            message: `安装失败: ${error}`,
            config: {
                shell: 'unknown',
                configFile: '',
                terminalPath: defaultPath.terminalPath,
                isConfigured: false
            }
        };
    }
}

/**
 * 检查环境变量状态
 * @returns 当前状态信息
 */
export async function checkTerminalEnvironmentStatus(): Promise<{
    isInstalled: boolean;
    config: EnvironmentConfig;
    verification?: { themetools: boolean; videowallpaper: boolean; adb: boolean; aapt: boolean };
}> {

    const config = await getEnvironmentConfig();
    let verification;

    if (config.isConfigured) {
        verification = await verifyInstallation();
    }

    return {
        isInstalled: config.isConfigured,
        config,
        verification
    };
}

/**
 * 测试函数：快速测试终端环境变量安装功能
 */
export async function testTerminalInstaller(): Promise<void> {

    try {
        // 测试状态检查
        await checkTerminalEnvironmentStatus();

        // 测试安装功能
        await installTerminalEnvironment();

        // 再次检查状态
        await checkTerminalEnvironmentStatus();

    } catch (error) {
        console.error('[测试] 测试过程中发生错误:', error);
    }

}

// 全局暴露测试函数，方便在浏览器控制台中调用
declare global {
    interface Window {
        testTerminalInstaller: () => Promise<void>;
        installTerminalEnvironment: () => Promise<{
            success: boolean;
            message: string;
            config: EnvironmentConfig;
            verification?: { themetools: boolean; videowallpaper: boolean; adb: boolean; aapt: boolean };
        }>;
        checkTerminalEnvironmentStatus: () => Promise<{
            isInstalled: boolean;
            config: EnvironmentConfig;
            verification?: { themetools: boolean; videowallpaper: boolean; adb: boolean; aapt: boolean };
        }>;
    }
}

// 在浏览器环境中暴露函数到全局对象
if (typeof window !== 'undefined') {
    window.testTerminalInstaller = testTerminalInstaller;
    window.installTerminalEnvironment = installTerminalEnvironment;
    window.checkTerminalEnvironmentStatus = checkTerminalEnvironmentStatus;

    // 添加初始化日志

    // 应用加载完成后自动执行一次环境变量检查和安装
    window.addEventListener('DOMContentLoaded', async () => {

        try {
            // 延迟2秒执行，确保其他模块都加载完成
            setTimeout(async () => {
                await installTerminalEnvironment();
            }, 2000);
        } catch (error) {
            console.error('❌ [终端安装器] 自动安装过程中发生错误:', error);
        }
    });
}

