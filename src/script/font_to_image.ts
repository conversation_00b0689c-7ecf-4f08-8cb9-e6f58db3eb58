import { messageError, messageLoading, messageSuccess, messageWarning } from './prompt_message';
import { setContainerAni } from './ui_animation';
import { 打开目录或文件 } from './file_operations';
import { readFile } from '@tauri-apps/plugin-fs';
import { invoke } from '@tauri-apps/api/core';

/**
 * 更改字体生成图片容器的字体样式
 */
export async function 字体生成图片(filePath: string) {
    console.log('开始设置字体样式:', filePath);

    try {
        // 使用 Tauri API 读取字体文件
        const fontData = await readFile(filePath);

        // 直接使用 ArrayBuffer 创建字体对象
        const fontFace = new FontFace('CustomFont', fontData);

        // 加载字体
        const loadedFont = await fontFace.load();

        // 将字体添加到document.fonts中
        document.fonts.add(loadedFont);

        // 获取预览文本容器
        const previewText = document.getElementById('font-to-img');
        if (!previewText) {
            console.error('未找到预览文本容器');
            return;
        }

        // 设置字体样式
        previewText.style.fontFamily = 'CustomFont';

    } catch (error) {
        console.error('字体加载失败:', error);
        messageError('字体加载失败');
        return;
    }


    try {
        // 获取字体生成图片容器
        const fontToImg = document.getElementById('font-to-img');
        if (!fontToImg) {
            messageError('未找到字体生成图片容器');
            return;
        }

        // 获取输入框元素
        const inputs = document.querySelectorAll('.font-input');
        if (inputs.length !== 5) {
            console.error('未找到所有输入框元素');
            return;
        }

        // 设置默认值（但不显示在输入框中）
        let color = '#000000FF';  // 默认黑色
        let cnSize = 42;          // 默认42号字体
        let enSize = 42;
        let numSizeA = 42;
        let numSizeB = 42;

        // 重置所有输入框为空，但保留内部默认值
        const resetInputs = () => {
            // 清空所有输入框显示
            inputs.forEach((input) => {
                (input as HTMLInputElement).value = '';
            });

            // 重置内部变量为默认值
            color = '#000000FF';
            cnSize = 42;
            enSize = 42;
            numSizeA = 42;
            numSizeB = 42;
        };

        // 修改颜色输入事件
        inputs[ 0 ].addEventListener('input', (e) => {
            const input = e.target as HTMLInputElement;
            const colorRegex = /^#([0-9A-Fa-f]{6}|[0-9A-Fa-f]{8})$/;
            if (colorRegex.test(input.value)) {
                color = input.value.toUpperCase();
            } else if (input.value === '') {
                color = '#000000FF';  // 输入为空时使用默认值
            }
        });

        // 修改数字输入处理函数
        const handleNumberInput = (input: HTMLInputElement, defaultValue: number): number => {
            if (input.value === '') {
                return defaultValue;  // 输入为空时返回默认值
            }
            const value = parseInt(input.value);
            return !isNaN(value) && value > 0 ? value : defaultValue;
        };

        // 修改各个大小输入事件
        inputs[ 1 ].addEventListener('input', (e) => {
            cnSize = handleNumberInput(e.target as HTMLInputElement, 42);
        });

        inputs[ 2 ].addEventListener('input', (e) => {
            enSize = handleNumberInput(e.target as HTMLInputElement, 42);
        });

        inputs[ 3 ].addEventListener('input', (e) => {
            numSizeA = handleNumberInput(e.target as HTMLInputElement, 42);
        });

        inputs[ 4 ].addEventListener('input', (e) => {
            numSizeB = handleNumberInput(e.target as HTMLInputElement, 42);
        });

        // 修改容器样式,显示界面
        fontToImg.style.top = '0';
        setContainerAni(0.8, 0);

        // 获取编辑按钮
        const editButton = document.getElementById('font-to-img-edit');
        if (!editButton) {
            console.error('未找到编辑按钮');
            return;
        }
        // 获取取消按钮
        const cancelButton = document.getElementById('font-to-img-cancel');
        if (!cancelButton) {
            console.error('未找到取消按钮');
            return;
        }

        // 获取确定按钮
        const okButton = document.getElementById('font-to-img-ok');
        if (!okButton) {
            console.error('未找到确定按钮');
            return;
        }

        // 修改确定按钮的点击事件
        okButton.onclick = async () => {
            await generateFontImages(filePath, color, cnSize, enSize, numSizeA, numSizeB);
            fontToImg.style.top = '100vh';
            setContainerAni(1, 1);
            resetInputs();
        };

        // 点击取消按钮关闭界面
        cancelButton.onclick = () => {
            fontToImg.style.top = '100vh';
            setContainerAni(1, 1);
            resetInputs(); // 关闭时重置所有值
            messageWarning('已取消');
        };

        // 点击编辑按钮
        editButton.onclick = async () => {
            try {
                const result = await invoke<string>('获取配置文件路径', {
                    fileName: 'font_to_img_config.json5'
                });
                await 打开目录或文件(result as string);
            } catch (error) {
                console.error('读取字体配置文件失败:', error);
                messageError(`读取字体配置文件失败: ${error}`);
            }
        };

        // 记录日志
        console.log('字体生成图片界面已显示');

    } catch (error) {
        console.error('显示字体生成图片界面时发生错误:', error);
        messageError('显示字体生成图片界面失败');
    }
}

/**
 * 生成字体图片
 * @param fontPath 字体文件路径
 * @param color 字体颜色（十六进制，例如：#FF0000 或 #FF0000FF）
 * @param cnSize 中文字体大小
 * @param enSize 英文字体大小
 * @param numSizeA A组数字大小
 * @param numSizeB B组数字大小
 * @returns Promise<string> 返回生成结果信息
 */
export async function generateFontImages(
    fontPath: string,
    color: string,
    cnSize: number,
    enSize: number,
    numSizeA: number,
    numSizeB: number
): Promise<string> {
    console.log('开始生成字体图片', fontPath, color, cnSize, enSize, numSizeA, numSizeB);
    try {
        messageLoading('正在生成字体图片...');
        const result = await invoke('字体生成图片', {
            fontPath,
            color,
            cnSize,
            enSize,
            numSizeA,
            numSizeB
        });
        await 打开目录或文件(result as string);
        messageSuccess('字体图片生成成功');
        return result as string;
    } catch (error) {
        messageError('生成字体图片失败');
        console.error('生成字体图片失败:', error);
        throw error;
    }
}