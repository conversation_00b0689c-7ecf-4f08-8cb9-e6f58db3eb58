import { Window } from '@tauri-apps/api/window'
import { toggleHelpContent } from './help';

// 每个窗口运行此脚本时都会获取自己的窗口实例
const appWindow = Window.getCurrent();

/**
 * 关闭所有相关子窗口的函数
 */
async function closeAllChildWindows() {
    if (appWindow.label !== 'main') return;

    console.log('关闭主窗口，同时关闭所有相关子窗口');

    try {
        // 关闭颜色搜索窗口
        const searchColorWindow = await Window.getByLabel('search_color');
        if (searchColorWindow) {
            console.log('正在关闭颜色搜索窗口');
            await searchColorWindow.destroy().catch(err =>
                console.error('关闭颜色搜索窗口失败:', err)
            );
        }
    } catch (err) {
        console.log('获取颜色搜索窗口失败或窗口不存在');
    }

    try {
        // 关闭编辑配置窗口
        const editConfigWindow = await Window.getByLabel('edit_config');
        if (editConfigWindow) {
            console.log('正在关闭编辑配置窗口');
            await editConfigWindow.destroy().catch(err =>
                console.error('关闭编辑配置窗口失败:', err)
            );
        }
    } catch (err) {
        console.log('获取编辑配置窗口失败或窗口不存在');
    }
}

// 获取当前窗口的标签，用于调试
(async () => {
    try {
        // 获取当前窗口的标签用于日志输出
        const label = appWindow.label;


        // 监听窗口关闭事件
        if (label === 'main') {
            appWindow.listen('tauri://close-requested', async () => {

                await closeAllChildWindows();
                // 关闭事件会继续处理，无需手动关闭窗口
            });
        }

        // 获取图标元素
        const minimizeIcon = document.getElementById('minimize-icon') as HTMLImageElement;
        const maximizeIcon = document.getElementById('maximize-icon') as HTMLImageElement;
        const closeIcon = document.getElementById('close-icon') as HTMLImageElement;

        // 定义获取资源路径的函数
        const getAssetPath = (filename: string) => {
            return new URL(`../assets/${filename}`, import.meta.url).href;
        };

        // 窗口失去焦点时
        window.addEventListener('blur', () => {
            minimizeIcon.src = getAssetPath('nofocus.svg');
            maximizeIcon.src = getAssetPath('nofocus.svg');
            closeIcon.src = getAssetPath('nofocus.svg');
        });

        // 窗口有焦点时
        window.addEventListener('focus', () => {
            minimizeIcon.src = getAssetPath('minimize-normal.svg');
            maximizeIcon.src = getAssetPath('maximize-normal.svg');
            closeIcon.src = getAssetPath('close-normal.svg');
        });

        // 获取控制按钮容器
        const titleBarControls = document.getElementById('title-bar-controls');

        // 悬停时
        titleBarControls?.addEventListener('mouseover', () => {
            minimizeIcon.src = getAssetPath('minimize-hover.svg');
            maximizeIcon.src = getAssetPath('maximize-hover.svg');
            closeIcon.src = getAssetPath('close-hover.svg');
        });

        // 没有悬停时
        titleBarControls?.addEventListener('mouseout', () => {
            minimizeIcon.src = getAssetPath('minimize-normal.svg');
            maximizeIcon.src = getAssetPath('maximize-normal.svg');
            closeIcon.src = getAssetPath('close-normal.svg');
        });

        // 修改最小化按钮事件处理
        minimizeIcon.addEventListener('click', async (e) => {
            e.preventDefault();
            e.stopPropagation();
            if (e.button !== 0) return; // 只处理左键点击

            try {
                minimizeIcon.src = getAssetPath('minimize-press.svg');
                // 确保操作的是当前窗口
                await appWindow.minimize();
                console.log(`窗口 ${appWindow.label} 已最小化`);
            } catch (error) {
                console.error('最小化窗口失败:', error);
            } finally {
                minimizeIcon.src = getAssetPath('minimize-normal.svg');
            }
        });

        // 修改最大化按钮事件处理
        maximizeIcon.addEventListener('click', async (e) => {
            e.preventDefault();
            e.stopPropagation();
            if (e.button !== 0) return; // 只处理左键点击

            try {
                // 确保在当前窗口中切换帮助内容
                toggleHelpContent();
                console.log(`窗口 ${appWindow.label} 切换了帮助内容`);
            } catch (error) {
                console.error('切换帮助页面失败:', error);
            }
        });

        // 修改关闭按钮事件处理
        closeIcon.addEventListener('click', async (e) => {
            e.preventDefault();
            e.stopPropagation();
            if (e.button !== 0) return; // 只处理左键点击

            try {
                closeIcon.src = getAssetPath('close-press.svg');
                console.log(`窗口 ${appWindow.label} 即将关闭`);

                // 如果是主窗口，先关闭其他所有相关窗口
                if (appWindow.label === 'main') {
                    await closeAllChildWindows();
                }

                // 关闭当前窗口
                await appWindow.destroy(); // 使用destroy()强制关闭窗口
            } catch (error) {
                console.error('关闭窗口失败:', error);
            } finally {
                closeIcon.src = getAssetPath('close-normal.svg');
            }
        });

        // 禁用鼠标右键功能
        document.addEventListener('contextmenu', (event) => {
            // 阻止默认的右键菜单
            event.preventDefault();
        });

        // 禁用搜索功能
        document.addEventListener('keydown', (event) => {
            // 检查是否按下 Ctrl + F 组合键
            if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
                // 阻止默认的搜索行为
                event.preventDefault();
            }
        });

    } catch (error) {
        console.error('标题栏模块初始化失败:', error);
    }
})();
