import { join, dirname } from '@tauri-apps/api/path';
import { exists } from '@tauri-apps/plugin-fs';
import { maml_scrcpt_main } from './maml_scrcpt/maml_scrcpt_main';
import { listen } from '@tauri-apps/api/event';
import { emit } from '@tauri-apps/api/event';
import { getCachedUsername } from './user_name';
// 测试阶段，暂时不外发
// 实现的功能：https://xiaomi.f.mioffice.cn/docx/doxk4zJQzlNgM3f6Xenb6zbxOyf，没有经过反复测试不确定是否有问题。

// 设置超时时间(毫秒)
const TIMEOUT = 30000;

interface ProcessResult {
    success: boolean;
    error?: {
        path: string;
        error: string;
        timestamp: string;
    };
    processingTime: number;
    message?: string;
}

interface ManifestFileEvent {
    payload: {
        xmlPath: string;
        eventId: string;
    };
}

/**
 * 处理单个manifest文件
 * @param xmlPath manifest.xml的完整路径
 * @returns 处理结果对象
 */
async function processManifestFile(xmlPath: string): Promise<ProcessResult> {
    try {
        // console.log('**********************************************************开始处理manifest文件:', xmlPath);
        // 获取用户名但不进行前置权限检查
        const username = await getCachedUsername();

        // console.log(`-----------------正在更新xml文件: ${xmlPath}-----------------`);
        const startTime = Date.now();

        // 获取文件所在目录
        const dirPath = await dirname(xmlPath);

        // 检查相关配置文件是否存在
        const [ varConfigExists, unlockConfigExists ] = await Promise.all([
            exists(await join(dirPath, 'var_config.xml')),
            exists(await join(dirPath, 'config.xml'))
        ]);

        // 处理文件，带超时控制，传递用户名
        const result = await Promise.race([
            maml_scrcpt_main(xmlPath, varConfigExists, unlockConfigExists, username),
            new Promise<never>((_, reject) =>
                setTimeout(() => reject(new Error('处理超时')), TIMEOUT)
            )
        ]);

        const processingTime = (Date.now() - startTime) / 1000;

        // 根据处理结果判断是否成功
        if (!result.success) {
            return {
                success: false,
                processingTime,
                message: result.errors.join('; ')
            };
        }

        // console.log(`成功处理文件: ${xmlPath} (耗时: ${processingTime.toFixed(2)}秒)`);

        return {
            success: true,
            processingTime
        };

    } catch (error) {
        const errorInfo = {
            path: xmlPath,
            error: error instanceof Error ? error.message : String(error),
            timestamp: new Date().toISOString()
        };
        console.error(`处理文件失败 (${xmlPath}):`, error);

        return {
            success: false,
            error: errorInfo,
            processingTime: 0
        };
    }
}

// 监听后端发来的处理请求
(async () => {
    await listen('processManifestFile', async (event: ManifestFileEvent) => {
        const { xmlPath, eventId } = event.payload;

        try {
            // 处理 manifest 文件
            const result = await processManifestFile(xmlPath);

            // 处理完成后，发送完成事件给后端
            await emit(`manifestProcessed_${eventId}`, { success: result.success, message: result.message });
        } catch (error) {
            console.error('处理 manifest 文件失败:', error);
            // 发送错误信息
            await emit(`manifestProcessed_${eventId}`, {
                success: false,
                message: error instanceof Error ? error.message : String(error)
            });
        }
    });
})();

export { processManifestFile };