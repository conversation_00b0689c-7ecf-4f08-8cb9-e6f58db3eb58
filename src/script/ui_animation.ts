/**
 * 设置container缩放和透明度，并根据透明度设置是否响应鼠标事件
 * @param scale 缩放比例
 * @param opacity 透明度
 */
export function setContainerAni(scale: number, opacity: number) {
    const containerElement = document.querySelector('.container') as HTMLElement;
    if (containerElement) {
        containerElement.style.transform = `scale(${scale})`;
        containerElement.style.transformOrigin = 'center bottom';
        containerElement.style.opacity = `${opacity}`;
        containerElement.style.transition = 'transform 0.15s ease-out, opacity 0.15s ease-out';
        // 根据透明度设置pointer-events
        containerElement.style.pointerEvents = opacity === 0 ? 'none' : 'auto';
    }
}

/**
 * 设置设备容器透明度，并根据透明度设置是否响应鼠标事件
 * @param opacity 透明度
 */
export function setDeviceAni(opacity: number) {
    const deviceContainer = document.querySelector('.device-container') as HTMLElement;
    if (deviceContainer) {
        deviceContainer.style.opacity = `${opacity}`;
        // 根据透明度设置pointer-events
        deviceContainer.style.pointerEvents = opacity === 0 ? 'none' : 'auto';
    }
}

/**
 * 设置大图标容器缩放和透明度，并根据透明度设置是否响应鼠标事件
 * @param scale 缩放比例
 * @param opacity 透明度
 */
export function setBigIconAni(scale: number, opacity: number) {
    const bigIconDialog = document.querySelector('.big-icon-dialog') as HTMLElement;
    if (bigIconDialog) {
        bigIconDialog.style.transform = `scale(${scale})`;
        bigIconDialog.style.opacity = `${opacity}`;
        bigIconDialog.style.transition = 'transform 0.15s ease-out, opacity 0.15s ease-out';

        // 根据透明度设置pointer-events
        bigIconDialog.style.pointerEvents = opacity === 0 ? 'none' : 'auto';
    }
}