import { invoke } from "@tauri-apps/api/core";
import { messageSuccess, messageError, messageLoading } from './prompt_message';

declare global {
    interface Window {
        打开目录或文件: (path: string) => Promise<unknown>;
        open_uri: (uri: string) => Promise<unknown>;
        压缩目录: (from: string, to: string) => Promise<unknown>;
    }
}

window.打开目录或文件 = 打开目录或文件;
window.open_uri = open_uri;
window.压缩目录 = 压缩目录;

/**
 * 打开目录或文件
 * @param path 需要打开的目录路径
 * @returns 返回打开结果
 */
export async function 打开目录或文件(path: string) {
    try {
        const result = await invoke('打开目录或文件', { path });
        return result;
    } catch (error) {
        messageError(`路径不存在: ${error}`);
        return null;
    }
}

/**
 * 打开uri
 * @param uri 需要打开的uri
 * @returns 返回打开结果
 */
export async function open_uri(uri: string) {
    try {
        const result = await invoke('open_uri', { uri });
        return result;
    } catch (error) {
        messageError('路径不存在');
        return null;
    }
}

/**
 * 压缩目录
 * @param from 需要压缩的目录路径
 * @param to 压缩后的文件路径
 * @returns 返回压缩结果
 */
export async function 压缩目录(from: string, to: string) {
    try {
        messageLoading('打包中...');
        const result = await invoke('压缩目录', { from, to });
        messageSuccess('打包完成');
        return result;
    } catch (error) {
        messageError('打包失败');
        return null;
    }
}
