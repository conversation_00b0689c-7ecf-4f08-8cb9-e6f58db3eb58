/**
 * 进度显示组件
 * 专门用于显示 tt 命令的执行进度
 */

import { messageLoading, messageSuccess, messageError } from './prompt_message';

/**
 * 进度信息接口
 */
interface ProgressInfo {
    current: number;
    total: number;
    status: string;
    item?: string;
}

/**
 * 进度显示管理器
 */
export class ProgressDisplay {
    private static instance: ProgressDisplay;
    private progressContainer: HTMLElement | null = null;

    private constructor() {
        this.createProgressContainer();
    }

    public static getInstance(): ProgressDisplay {
        if (!ProgressDisplay.instance) {
            ProgressDisplay.instance = new ProgressDisplay();
        }
        return ProgressDisplay.instance;
    }

    /**
     * 创建进度显示容器
     */
    private createProgressContainer(): void {
        // 检查是否已存在
        let container = document.getElementById('tt-progress-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'tt-progress-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(0, 0, 0, 0.9);
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                font-family: 'Courier New', monospace;
                font-size: 14px;
                z-index: 10000;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                border: 1px solid #333;
                display: none;
            `;
            document.body.appendChild(container);
        }
        this.progressContainer = container;
    }

    /**
     * 解析 tt 命令的输出行，提取进度信息
     */
    public parseProgressLine(line: string): ProgressInfo | null {
        const trimmedLine = line.trim();

        // 匹配格式: (1/4) 首次: 包含所有值
        // 匹配格式: (2/4) 完成: color1
        // 匹配格式: (3/4) 失败: color2
        const progressMatch = trimmedLine.match(/\((\d+)\/(\d+)\)\s*([^:]+)(?::\s*(.+))?/);

        if (progressMatch) {
            const current = parseInt(progressMatch[ 1 ]);
            const total = parseInt(progressMatch[ 2 ]);
            const status = progressMatch[ 3 ].trim();
            const item = progressMatch[ 4 ]?.trim();

            return {
                current,
                total,
                status,
                item
            };
        }

        // 匹配数组模式开始信息: 数组模式: 3个项目，首次间隔1秒，其余间隔1秒
        const arrayModeMatch = trimmedLine.match(/数组模式:\s*(\d+)个项目/);
        if (arrayModeMatch) {
            const total = parseInt(arrayModeMatch[ 1 ]) + 1; // N+1 操作
            return {
                current: 0,
                total,
                status: '准备开始',
                item: `${arrayModeMatch[ 1 ]}个项目`
            };
        }

        return null;
    }

    /**
     * 更新进度显示
     */
    public updateProgress(progressInfo: ProgressInfo): void {

        if (!this.progressContainer) {
            this.createProgressContainer();
        }

        const percentage = progressInfo.total > 0 ? Math.round((progressInfo.current / progressInfo.total) * 100) : 0;

        // 构建显示内容
        let displayText = `TT 命令执行进度\n`;
        displayText += `━━━━━━━━━━━━━━━━━━━━\n`;
        displayText += `进度: ${progressInfo.current}/${progressInfo.total} (${percentage}%)\n`;
        displayText += `状态: ${progressInfo.status}\n`;

        if (progressInfo.item) {
            displayText += `项目: ${progressInfo.item}\n`;
        }

        // 创建进度条
        const barLength = 20;
        const filledLength = Math.round((progressInfo.current / progressInfo.total) * barLength);
        const progressBar = '█'.repeat(filledLength) + '░'.repeat(barLength - filledLength);
        displayText += `[${progressBar}]\n`;

        if (this.progressContainer) {
            this.progressContainer.innerHTML = `<pre style="margin: 0; white-space: pre-wrap;">${displayText}</pre>`;
            this.progressContainer.style.display = 'block';
        }

        // 同时更新消息提示
        const messageText = progressInfo.item
            ? `(${progressInfo.current}/${progressInfo.total}) ${progressInfo.status}: ${progressInfo.item}`
            : `(${progressInfo.current}/${progressInfo.total}) ${progressInfo.status}`;

        messageLoading(messageText);

        // 在控制台也输出，方便调试
        console.log(`🎯 TT进度: ${messageText}`);
    }

    /**
     * 显示完成状态
     */
    public showCompleted(success: boolean, message?: string): void {
        if (this.progressContainer) {
            if (success) {
                this.progressContainer.style.background = 'rgba(0, 128, 0, 0.9)';
                this.progressContainer.innerHTML = `<pre style="margin: 0;">✅ 操作完成\n${message || '所有项目已处理'}</pre>`;
                messageSuccess(message || 'TT 命令执行完成！');
            } else {
                this.progressContainer.style.background = 'rgba(128, 0, 0, 0.9)';
                this.progressContainer.innerHTML = `<pre style="margin: 0;">❌ 操作失败\n${message || '执行过程中出现错误'}</pre>`;
                messageError(message || 'TT 命令执行失败');
            }

            // 3秒后自动隐藏
            setTimeout(() => {
                this.hideProgress();
            }, 3000);
        }
    }

    /**
     * 显示中断状态
     */
    public showAborted(): void {
        if (this.progressContainer) {
            this.progressContainer.style.background = 'rgba(128, 128, 0, 0.9)';
            this.progressContainer.innerHTML = `<pre style="margin: 0;">⚠️ 操作已中断\n用户手动停止了执行</pre>`;
            messageError('TT 命令执行已中断');

            // 2秒后自动隐藏
            setTimeout(() => {
                this.hideProgress();
            }, 2000);
        }
    }

    /**
     * 隐藏进度显示
     */
    public hideProgress(): void {
        if (this.progressContainer) {
            this.progressContainer.style.display = 'none';
            this.progressContainer.style.background = 'rgba(0, 0, 0, 0.9)'; // 重置背景色
        }
    }

    /**
     * 处理 tt 命令的输出行
     */
    public handleTtOutput(outputLine: string): void {
        const progressInfo = this.parseProgressLine(outputLine);
        if (progressInfo) {
            this.updateProgress(progressInfo);
        } else {
            // 即使不是进度行，也在控制台输出，方便调试
            const trimmed = outputLine.trim();
            if (trimmed) {
                console.log('TT输出:', trimmed);
            }
        }
    }

    /**
     * 开始监控 tt 命令执行
     */
    public startMonitoring(): void {
        this.hideProgress(); // 清除之前的显示
        console.log('🚀 开始监控 TT 命令执行进度...');
    }
}

// 导出单例实例
export const progressDisplay = ProgressDisplay.getInstance();

// 导出便捷函数
export function handleTtProgress(outputLine: string): void {
    progressDisplay.handleTtOutput(outputLine);
}

export function startTtMonitoring(): void {
    progressDisplay.startMonitoring();
}

export function completeTtOperation(success: boolean, message?: string): void {
    if (success) {
        progressDisplay.showCompleted(true, message);
    } else {
        progressDisplay.showCompleted(false, message);
    }
}

export function abortTtOperation(): void {
    progressDisplay.showAborted();
}
