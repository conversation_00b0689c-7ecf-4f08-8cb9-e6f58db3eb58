/**
 * 更新拖拽文件名显示(不含后缀名)
 * @param filePath 文件路径
 */
export async function 更新输入框(filePath: string) {
    try {
        // console.log('更新输入框显示:', filePath);
        // 获取文件名并去除后缀
        // 更新显示
        const dragFileNameElement = document.querySelector('.mtz-name-text');
        if (dragFileNameElement) {
            dragFileNameElement.textContent = filePath || '拖入主题文件';
        }
    } catch (error) {
        console.error('================更新输入框显示失败:', error);
    }
}

