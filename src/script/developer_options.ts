import { messageSuccess, messageError } from './prompt_message';
import { adb_all } from './commands';

// 绑定developer-options，点击后打开开发者选项
document.getElementById('developer-options')?.addEventListener('click', () => {
    try {
        adb_all([ 'shell', 'am', 'start', '-a', 'android.settings.APPLICATION_DEVELOPMENT_SETTINGS' ]);
        messageSuccess('开发者选项已打开');
    } catch (error) {
        messageError('开发者选项打开失败');
    }
});
