import { attachConsole } from '@tauri-apps/plugin-log';

/**
 * 初始化日志系统
 */
async function initLogger() {
    try {
        // 启用浏览器控制台日志
        await attachConsole();

        // 使用原生console方法拦截特殊格式日志
        const originalConsoleLog = console.log;
        const originalConsoleInfo = console.info;
        const originalConsoleWarn = console.warn;
        const originalConsoleError = console.error;
        const originalConsoleDebug = console.debug;

        // 处理特殊格式日志的函数
        const processSpecialLog = (originalFn: typeof console.log) => {
            return function (message: any, ...args: any[]) {
                if (typeof message === 'string' && message.includes('CONSOLE_OBJECT:')) {
                    try {
                        const parts = message.split('CONSOLE_OBJECT:');
                        if (parts.length > 1) {
                            const jsonPart = parts[ 1 ].trim();
                            const jsonData = JSON.parse(jsonPart);
                            originalFn('Rust日志对象:', jsonData);
                            return;
                        }
                    } catch (err) {
                        // 如果解析失败，回退到原始行为
                    }
                }
                originalFn(message, ...args);
            };
        };

        // 替换console方法
        console.log = processSpecialLog(originalConsoleLog);
        console.info = processSpecialLog(originalConsoleInfo);
        console.warn = processSpecialLog(originalConsoleWarn);
        console.error = processSpecialLog(originalConsoleError);
        console.debug = processSpecialLog(originalConsoleDebug);
    } catch (err) {
        console.error('日志系统初始化失败:', err);
    }
}

// 执行初始化
initLogger();