import { invoke } from "@tauri-apps/api/core";
import { messageError, messageLoading, messageSuccess } from './prompt_message';
import { 生成应用按钮 } from './apply/buttons';

declare global {
    interface Window {
        dragFiles: string;
        parentDir: string;
        cacheDir: string;
        清理缓存: () => Promise<void>;
    }
}

/**
 * 清理缓存目录下的所有资源
 * @returns 无返回值,清理结果通过消息提示显示
 */
async function 清理缓存() {
    console.log('开始清理缓存目录');

    try {
        await messageLoading('正在清理缓存...');

        // 调用后端清理缓存命令
        await invoke('清理缓存');
        const inputPath = ''
        await invoke<Record<string, Uint8Array>>('预编译', {
            inputPath
        });
        // 清空主题文件名显示
        const mtzNameText = document.getElementById('mtz-name-text');
        if (mtzNameText) {
            mtzNameText.textContent = '拖入主题文件';
        }

        // 将三个全局变量设置为空字符串
        window.dragFiles = '';
        window.parentDir = '';
        window.cacheDir = '';
        await 生成应用按钮(window.dragFiles);
        await messageSuccess('缓存清理完成', 1000);

        console.log('缓存清理完成');
    } catch (error) {
        console.error('清理缓存失败:', error);
        await messageError(`清理缓存失败: ${error}`);
    }
}

// 将函数暴露给全局
window.清理缓存 = 清理缓存;
