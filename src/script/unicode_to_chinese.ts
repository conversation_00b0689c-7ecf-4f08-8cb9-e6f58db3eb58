import { readTextFile, writeTextFile } from '@tauri-apps/plugin-fs';
import { messageSuccess, messageError } from './prompt_message';

/**
 * 将文件中的Unicode编码转换为中文
 * @param filePaths 需要处理的文件路径数组
 */
export async function unicode转中文(filePaths: string[]) {
    // 遍历所有文件进行unicode转中文处理
    for (const filePath of filePaths) {
        try {
            // 读取文件内容
            const fileContent = await readTextFile(filePath);
            console.log('读取文件内容:', fileContent);

            // 将unicode转换为中文
            const chineseContent = fileContent.replace(/\\u[\dA-F]{4}/gi, match => {
                return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16));
            });

            // 写入转换后的内容
            await writeTextFile(filePath, chineseContent);
            await messageSuccess(`文件 ${filePath.split(/[/\\]/).pop()} Unicode转换中文成功`);
            console.log('转换后的内容:', chineseContent);
        } catch (error) {
            console.error(`文件 ${filePath} Unicode转换中文失败:`, error);
            await messageError(`文件 ${filePath} Unicode转换失败: ${error}`);
        }
    }
}