import { invoke } from '@tauri-apps/api/core';
import { messageSuccess, messageError, messageLoading } from './prompt_message';
import { dirname } from '@tauri-apps/api/path';
import { 预编译 } from './precompile';

declare global {
    interface Window {
        打包主题: (filePath: string, outputPath?: string, skipCache?: boolean) => Promise<string | void>;
        dragFiles: string;
    }
}

/**
 * 打包主题
 * @param filePath 主题文件路径
 * @param outputPath 输出路径,可选参数,默认为当前工作目录
 * @param skipCache 是否跳过缓存,可选参数,默认为false
 * @returns Promise<void> 成功时返回undefined，失败时返回错误信息
 */
export default async function 打包主题(filePath: string, _outputPath?: string, skipCache: boolean = false): Promise<string | void> {

    try {
        if (!filePath) {
            messageError('请先拖入主题文件');
            return '请先拖入主题文件';
        }

        messageLoading('打包主题中...');
        // 确保使用一致的源路径
        const sourcePath = !skipCache ? window.dragFiles : filePath;

        let cacheDir: string;
        try {
            // 缓存文件函数现在返回完整的缓存目录路径
            cacheDir = await invoke('缓存文件', {
                sourcePath: sourcePath // 始终使用相同的源路径
            }) as string;
        } catch (error) {
            console.error('缓存文件失败:', error);
            throw new Error(`缓存文件失败: ${error}`);
        }

        // 使用缓存文件函数返回的路径进行预编译和打包
        // 获取源文件的父目录作为输出目录
        const parentDir = await dirname(sourcePath);

        // 提取原始主题名称（文件夹名）
        const originalThemeName = sourcePath.split(/[\/\\]/).pop() || '';

        // 输出缓存目录路径信息

        // 预编译缓存目录
        await 预编译(cacheDir);

        // 拼接打包参数，增加原始主题名称参数和源路径参数
        const params = {
            inputPath: cacheDir,
            outputPath: parentDir,
            originalThemeName: originalThemeName, // 传递原始主题名称
            sourcePath: sourcePath, // 传递源路径，用于正确构建缓存路径
        };

        await invoke('打包主题', params);

        messageSuccess('打包主题成功');
    } catch (error) {
        const errorMessage = `打包主题失败:${error}`;
        messageError(errorMessage);
        throw error;
    }
}

// 将函数暴露给全局
window.打包主题 = 打包主题;
