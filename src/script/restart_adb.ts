declare global {
    interface Window {
        重启adb: () => Promise<void>;
    }
}

import { messageSuccess, messageError, messageLoading } from './prompt_message';
import { adb } from './commands';
/**
 * 重启adb服务
 * @returns 无返回值
 */
export default async function 重启adb() {

    let hasError = false;
    let errorMessage = '';

    messageLoading('正在重启adb服务');

    // 断开所有连接
    try {
        await adb([ 'disconnect' ]);
        console.log('成功断开所有设备连接');
    } catch (error) {
        hasError = true;
        errorMessage += `断开连接失败: ${error}\n`;
        console.error('断开连接失败:', error);
    }

    // 关闭adb服务
    try {
        await adb([ 'kill-server' ]);
        console.log('成功关闭adb服务');
    } catch (error) {
        hasError = true;
        errorMessage += `关闭adb服务失败: ${error}\n`;
        console.error('关闭adb服务失败:', error);
    }

    // 启动adb服务
    // try {
    //     await adb([ 'start-server' ]);
    //     console.log('成功启动adb服务');
    // } catch (error) {
    //     hasError = true;
    //     errorMessage += `启动adb服务失败: ${error}\n`;
    //     console.error('启动adb服务失败:', error);
    // }

    if (hasError) {
        messageError(`${errorMessage}`);
    } else {
        messageSuccess('成功重启adb服务');
    }
}

window.重启adb = 重启adb;
