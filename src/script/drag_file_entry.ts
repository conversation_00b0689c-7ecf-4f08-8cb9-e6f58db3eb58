import 打包大图标 from './pack_large_icons';
import { getCurrentWebview } from "@tauri-apps/api/webview";
import { normalize } from '@tauri-apps/api/path';
import { setContainerAni, setDeviceAni, setBigIconAni } from './ui_animation';
import { handleBubble1Drop } from './drag_file_handler';
import { pushFile } from './push_file';
// 泡泡相关的类型定义
interface DragDropPayload {
    paths: string[];
}

// 处理泡泡1 - 推送到手机
async function handleBubble2Drop(normalizedPaths: string[]) {
    pushFile(normalizedPaths);
}

// 处理泡泡2 - 打包大图标
async function handleBubble3Drop(draggedPaths: string[]) {
    await 打包大图标(draggedPaths);
}

// 隐藏泡泡
function hideBubble() {
    const bubbleContainer = document.querySelector('.bubble-container') as HTMLElement;
    if (bubbleContainer) {
        bubbleContainer.style.opacity = '0';
        bubbleContainer.style.transform = 'scale(1.2)';
    }
}



// 监听文件拖放事件
async function listenDragDrop() {
    let currentBubble = 0;
    let overOn = 0;
    const webview = await getCurrentWebview();

    await webview.onDragDropEvent(async (event) => {
        // 文件拖入窗口时
        if (event.event === 'tauri://drag-enter') {
            overOn = 1;
            // 显示泡泡
            const bubbleContainer = document.querySelector('.bubble-container') as HTMLElement;
            if (bubbleContainer) {
                bubbleContainer.style.opacity = '1';
                bubbleContainer.style.transform = 'scale(1)';
            }
            // 隐藏设备容器
            setDeviceAni(0);
            // 隐藏 container
            setContainerAni(0.9, 0);
            setBigIconAni(0.8, 0);
        }

        // 只在动画完成后才处理拖拽经过事件
        if (event.event === 'tauri://drag-over' && overOn) {
            const payload = event.payload as { position: { x: number; y: number } };
            const { position } = payload;

            const scale = window.devicePixelRatio || 1;
            let domX = position.x;
            let domY = position.y;

            // 将物理像素转换为 CSS 像素
            if (window.platform == "windows") {
                domX = position.x / scale;
                domY = position.y / scale;
            }

            const bubble1 = document.getElementById('bubble1');
            const bubble2 = document.getElementById('bubble2');
            const bubble3 = document.getElementById('bubble3');

            if (bubble1 && bubble2 && bubble3) {
                // 获取气泡在屏幕中的位置（CSS 像素）
                const rect1 = bubble1.getBoundingClientRect();
                const rect2 = bubble2.getBoundingClientRect();
                const rect3 = bubble3.getBoundingClientRect();

                // 判断鼠标是否在泡泡上方（包含margin区域）
                const marginSize = 15; // 与CSS中设置的margin或伪元素扩展值相同

                const isOverBubble1 =
                    domX >= (rect1.left - marginSize) && domX <= (rect1.right + marginSize) &&
                    domY >= (rect1.top - marginSize) && domY <= (rect1.bottom + marginSize);
                const isOverBubble2 =
                    domX >= (rect2.left - marginSize) && domX <= (rect2.right + marginSize) &&
                    domY >= (rect2.top - marginSize) && domY <= (rect2.bottom + marginSize);
                const isOverBubble3 =
                    domX >= (rect3.left - marginSize) && domX <= (rect3.right + marginSize) &&
                    domY >= (rect3.top - marginSize) && domY <= (rect3.bottom + marginSize);

                // 高亮当前悬停的泡泡
                bubble1.style.backgroundColor = isOverBubble1 ? '#4169e1' : '';
                bubble1.style.color = isOverBubble1 ? '#fff' : '';

                bubble2.style.backgroundColor = isOverBubble2 ? '#4169e1' : '';
                bubble2.style.color = isOverBubble2 ? '#fff' : '';

                bubble3.style.backgroundColor = isOverBubble3 ? '#4169e1' : '';
                bubble3.style.color = isOverBubble3 ? '#fff' : '';

                // 记录当前悬停的泡泡
                currentBubble = isOverBubble1 ? 1 : isOverBubble2 ? 2 : isOverBubble3 ? 3 : 0;
            }
        }

        // 离开时
        if (event.event === 'tauri://drag-leave') {
            overOn = 0;
            hideBubble();
            // 显示 container
            setContainerAni(1, 1);
            // 显示设备容器
            setDeviceAni(1);
            return;
        }

        // 放入文件
        if (event.event === 'tauri://drag-drop') {
            // 拖放文件列表
            const payload = event.payload as DragDropPayload;
            const draggedPaths = payload.paths;

            // 标准化所有拖放的文件路径
            const normalizedPaths = await Promise.all(
                draggedPaths.map((path) => normalize(path))
            );

            overOn = 0;

            if (currentBubble !== 3) {
                hideBubble();
                // 显示container
                setContainerAni(1, 1);
                // 显示设备容器
                setDeviceAni(1);
            }

            // 根据悬停的泡泡来处理
            if (currentBubble === 1) {
                handleBubble1Drop(normalizedPaths);
            } else if (currentBubble === 2) {
                handleBubble2Drop(normalizedPaths);
            } else if (currentBubble === 3) {
                handleBubble3Drop(normalizedPaths);
                hideBubble();
                // 显示大图标容器
                setBigIconAni(1, 1);
            }
        }
    });
}


listenDragDrop();
