/**
 * 路径处理工具 - 确保在不同环境下资源路径的一致性
 */

// 声明全局类型
declare global {
    interface Window {
        __TAURI__?: any;
    }

    interface ImportMeta {
        env: {
            DEV: boolean;
            PROD: boolean;
            MODE: string;
        };
    }
}

/**
 * 获取资源文件的URL
 * @param fileName 资源文件名
 * @returns 资源文件的完整URL
 */
export function getAssetUrl(fileName: string): string {
    // 尝试所有可能的路径并缓存结果
    if (!fileName) {
        console.error('文件名不能为空');
        return '';
    }

    // 移除路径中可能包含的路径部分，只保留文件名
    const basename = fileName.split('/').pop()?.split('\\').pop();
    if (!basename) {
        console.error(`无法从 ${fileName} 中提取文件名`);
        return fileName;
    }

    try {
        // 首先尝试从公共资源目录加载
        const publicPath = new URL(`/assets/${basename}`, window.location.origin).href;

        // 在开发环境中，可能需要特殊处理
        if (import.meta.env.DEV) {
            // 在开发环境中可能有特殊的资源路径
            // 尝试检测是否在Tauri应用中
            const isTauri = window.__TAURI__ !== undefined;
            if (isTauri) {
                // Tauri应用中使用相对路径
                return `./assets/${basename}`;
            }
        }

        return publicPath;
    } catch (error) {
        console.error(`生成资源URL失败: ${fileName}`, error);
        // 回退到相对路径
        return `./assets/${basename}`;
    }
}

/**
 * 资源路径处理函数 - 用于img标签的src属性
 * 自动尝试修复图片路径问题
 * @param element 图片元素
 */
export function ensureImageSrc(element: HTMLImageElement): void {
    if (!element) return;

    // 保存原始路径以便报错
    const originalSrc = element.src;

    // 提取文件名
    let fileName = '';
    try {
        // 尝试从URL中提取
        const url = new URL(originalSrc);
        fileName = url.pathname.split('/').pop() || '';
    } catch (e) {
        // 不是有效URL，尝试直接从路径字符串提取
        fileName = originalSrc.split('/').pop()?.split('\\').pop() || '';
    }

    if (!fileName) {
        console.error(`无法从路径中提取文件名: ${originalSrc}`);
        return;
    }

    // 设置正确的资源URL
    const correctUrl = getAssetUrl(fileName);
    if (originalSrc !== correctUrl) {
        console.log(`修正图片路径: ${originalSrc} -> ${correctUrl}`);
        element.src = correctUrl;
    }
}

/**
 * 为页面中所有图片元素自动修复路径
 * 可以在页面加载完成后调用
 */
export function fixAllImagePaths(): void {
    console.log('开始修复页面中所有图片路径');
    const images = document.querySelectorAll('img') as NodeListOf<HTMLImageElement>;

    console.log(`找到 ${images.length} 个图片元素`);
    images.forEach((img, index) => {
        console.log(`处理图片 [${index}]: ${img.src}`);
        ensureImageSrc(img);

        // 添加错误处理逻辑
        img.onerror = () => {
            console.error(`图片加载失败: ${img.src}`);
            // 获取文件名
            const fileName = img.src.split('/').pop()?.split('\\').pop() || '';

            // 构建备选路径并尝试
            const fallbackPaths = [
                `./assets/${fileName}`,
                `/assets/${fileName}`,
                `../assets/${fileName}`,
                `../../assets/${fileName}`,
                `/src/assets/${fileName}`,
                `./src/assets/${fileName}`,
                `src/assets/${fileName}`
            ];

            // 尝试备选路径
            let tried = 0;
            const tryNextPath = () => {
                if (tried >= fallbackPaths.length) {
                    console.error(`所有备选路径尝试失败: ${fileName}`);
                    return;
                }

                const nextPath = fallbackPaths[ tried++ ];
                console.log(`尝试备选路径 [${tried}/${fallbackPaths.length}]: ${nextPath}`);

                // 保存原始错误处理器
                const originalErrorHandler = img.onerror;

                // 设置新的错误处理器
                img.onerror = () => {
                    // 恢复原始错误处理器
                    img.onerror = originalErrorHandler;
                    // 尝试下一个路径
                    setTimeout(tryNextPath, 0);
                };

                // 尝试新路径
                img.src = nextPath;
            };

            // 开始尝试备选路径
            tryNextPath();
        };
    });
} 