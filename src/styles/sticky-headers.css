/* 列表滚动固定标题栏样式 */

/* 模块卡片标题栏固定 */
.module-card-header {
    position: sticky !important;
    top: 0 !important;
    z-index: 10 !important;
    background-color: var(--hover-color) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

/* 表格标题固定 */
.list-header {
    position: sticky !important;
    top: 0 !important;
    z-index: 10 !important;
    background-color: #f0f2f5 !important;
}

/* 配置表头固定 */
.config-table thead,
.config-table th {
    position: sticky !important;
    top: 0 !important;
    z-index: 10 !important;
    background-color: #f5f7fa !important;
}

/* 修改模块卡片，确保列表在卡片内可滚动 */
.module-card {
    max-height: 600px !important;
    overflow-y: auto !important;
    display: flex !important;
    flex-direction: column !important;
}

/* 确保项目列表可以撑满内容区域并可滚动 */
.item-list {
    flex: 1 !important;
    overflow-y: auto !important;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    .module-card-header {
        background-color: #333333 !important;
    }

    .list-header {
        background-color: #2d2d2d !important;
    }

    .config-table thead,
    .config-table th {
        background-color: #2d2d2d !important;
    }
}