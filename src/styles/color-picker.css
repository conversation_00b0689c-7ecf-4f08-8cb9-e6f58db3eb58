/* ================================
   专业取色器样式
   ================================ */

/* 外层定位容器 - 无动画，纯定位 */
.color-picker-position-container {
    position: fixed;
    width: 0;
    height: 0;
    pointer-events: none;
    z-index: 2147483647;
    transform: translate(-50%, -50%);
    /* 不添加任何transition或animation，确保位置变化立即生效 */
}

/* 内层放大镜容器 */
.color-picker-magnifier {
    position: absolute;
    border-radius: 50%;
    border: 2px solid #ffffff;
    box-shadow:
        0 0 0 1px rgba(0, 0, 0, 0.3),
        0 4px 20px rgba(0, 0, 0, 0.2);
    pointer-events: none;
    overflow: hidden;
    transform: translate(-50%, -50%) translateZ(0);
    transition: width 0.25s cubic-bezier(0.4, 0.0, 0.2, 1),
        height 0.25s cubic-bezier(0.4, 0.0, 0.2, 1),
        margin-left 0.25s cubic-bezier(0.4, 0.0, 0.2, 1),
        margin-top 0.25s cubic-bezier(0.4, 0.0, 0.2, 1);
    will-change: width, height, margin-left, margin-top;
    backface-visibility: hidden;
    transform-style: preserve-3d;
    /* 移除初始动画，避免闪烁 */
}

/* 放大镜Canvas */
.color-picker-magnifier canvas {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
}

/* 指示器 */
.color-picker-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 1;
    transition: border-color 0.1s ease, box-shadow 0.1s ease;
    border-radius: 0;
    box-sizing: border-box;
}

/* 颜色信息显示 */
.color-picker-info {
    position: fixed;
    background: rgba(0, 0, 0, 0.85);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
    font-size: 12px;
    line-height: 1.4;
    pointer-events: none;
    z-index: 2147483647;
    transform: translateX(-50%);
    white-space: pre-line;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 动画效果 */
@keyframes colorPickerFadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }

    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .color-picker-magnifier {
        border-width: 1px;
    }

    .color-picker-info {
        font-size: 11px;
        padding: 6px 10px;
    }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    .color-picker-magnifier {
        border-color: #ffffff;
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.3),
            0 4px 20px rgba(0, 0, 0, 0.4);
    }

    .color-picker-info {
        background: rgba(20, 20, 20, 0.9);
        border-color: rgba(255, 255, 255, 0.2);
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .color-picker-magnifier {
        border-width: 3px;
        border-color: #000000;
        box-shadow:
            0 0 0 1px #ffffff,
            0 0 0 4px #000000;
    }

    .color-picker-indicator {
        border-width: 2px !important;
    }

    .color-picker-info {
        background: #000000;
        border: 2px solid #ffffff;
        color: #ffffff;
    }
}

/* 禁用文本选择 */
.color-picker-magnifier,
.color-picker-indicator,
.color-picker-info {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}