/* 这个文件包含了修复表格对齐的样式 */

/* 值列样式 - 左对齐 */
.config-table th:nth-child(1),
.config-table td:nth-child(1) {
    width: 70%;
    text-align: left;
}

/* 状态列样式 - 居中对齐 */
.config-table th:nth-child(2),
.config-table td:nth-child(2) {
    width: 30%;
    text-align: center !important;
}

/* 状态列标题和单元格 */
.config-table-header-cell-action,
.config-table-cell-action {
    text-align: center !important;
}

/* 表格基础样式 */
.config-table td {
    vertical-align: middle;
}

/* 删除之前定义的按钮样式，可能会有冲突 */
/* 重新定义按钮样式 */
.config-table-cell-action .delete-btn,
.config-table-cell-action .add-btn {
    display: inline-block;
    margin: 0 auto;
}

/* 开关容器样式 */
.config-table .toggle-switch-container {
    margin: 0 auto;
    text-align: center;
    width: 100%;
    display: block;
}