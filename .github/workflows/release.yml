name: 'publish'

# 触发工作流的事件
on:
  push:
    tags:
      - 'v*'  # 当推送以'v'开头的tag时触发，例如 v1.0.0
  workflow_dispatch:  # 允许手动触发工作流
    inputs:
      app_name:
        description: '应用程序基础名称（不含平台后缀）'
        required: false
        default: 'mini-editor'
        type: string
      version:
        description: '应用程序版本号 (例如: 1.0.0)'
        required: false
        default: '0.0.0'
        type: string

jobs:
  publish-tauri:
    # 设置权限
    permissions:
      contents: write  # 允许工作流写入仓库内容

    # 策略配置
    strategy:
      fail-fast: false  # 某个平台构建失败时不影响其他平台继续构建
      matrix:
        include:
          - platform: 'macos-latest'  # MacOS ARM架构构建配置（M1及以上芯片）
            args: '--verbose --target aarch64-apple-darwin'
            platform_suffix: '-macos-M系列芯片'
          - platform: 'macos-latest'  # MacOS Intel架构构建配置
            args: '--verbose --target x86_64-apple-darwin'
            platform_suffix: '-macos-英特尔芯片'
          - platform: 'windows-latest'  # Windows构建配置
            args: '--verbose'
            platform_suffix: '-windows'
          # - platform: 'ubuntu-22.04'  # Ubuntu构建配置
          #   args: '--verbose'
          #   platform_suffix: '-Linux'

    runs-on: ${{ matrix.platform }}  # 在指定平台上运行工作流

    steps:
      - uses: actions/checkout@v4  # 检出代码
        with:
          fetch-depth: 0  # 获取完整历史以确保能够正确读取标签

      - name: Auto increment version
        id: get_version
        shell: pwsh
        run: |
          # 如果是手动触发并提供了版本号，则使用输入的版本号
          if ("${{ github.event_name }}" -eq "workflow_dispatch" -and "${{ github.event.inputs.version }}" -ne "") {
            $NEW_VERSION = "${{ github.event.inputs.version }}"
            Write-Host "使用手动指定版本号: $NEW_VERSION"
          } elseif ("${{ github.event_name }}" -eq "push" -and "${{ github.ref }}" -like "refs/tags/v*") {
            # 如果是通过标签触发，则使用标签中的版本号
            $NEW_VERSION = "${{ github.ref }}".Replace('refs/tags/v', '')
            Write-Host "使用标签版本号: $NEW_VERSION"
          } else {
            # 自动生成版本号：读取package.json基础版本 + Git提交数量
            $PACKAGE_JSON = Get-Content "package.json" | ConvertFrom-Json
            $BASE_VERSION = $PACKAGE_JSON.version

            # 解析基础版本号（主版本.次版本）
            $VERSION_PARTS = $BASE_VERSION -split '\.'
            $MAJOR = $VERSION_PARTS[0]
            $MINOR = $VERSION_PARTS[1]

            # 获取Git提交数量作为补丁版本号
            $COMMIT_COUNT = git rev-list --count HEAD

            $NEW_VERSION = "$MAJOR.$MINOR.$COMMIT_COUNT"
            Write-Host "自动生成版本号: $NEW_VERSION (基础版本: $BASE_VERSION, 提交数量: $COMMIT_COUNT)"
          }

          # 设置输出变量
          "new_version=$NEW_VERSION" >> $env:GITHUB_OUTPUT
          "new_tag=v$NEW_VERSION" >> $env:GITHUB_OUTPUT

      - name: setup node  # 设置Node.js环境
        uses: actions/setup-node@v4
        with:
          node-version: lts/*  # 使用最新的LTS版本
          cache: 'yarn'  # 添加缓存配置提高效率

      - name: install Rust stable  # 安装Rust稳定版
        uses: dtolnay/rust-toolchain@stable
        with:
          # 确保同时添加 aarch64 和 x86_64 构建目标
          targets: ${{ matrix.platform == 'macos-latest' && 'aarch64-apple-darwin,x86_64-apple-darwin' || '' }}
          # 添加 toolchain 组件
          components: rust-src

      - name: Update package.json version
        run: |
          # 使用Node.js更新package.json中的版本号
          node -e "
            const fs = require('fs');
            const newVersion = '${{ steps.get_version.outputs.new_version }}';

            // 更新package.json
            const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
            packageJson.version = newVersion;
            fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2) + '\n');
            console.log('已更新package.json版本号为: ' + packageJson.version);

            // 更新package-lock.json
            if (fs.existsSync('package-lock.json')) {
              const packageLock = JSON.parse(fs.readFileSync('package-lock.json', 'utf8'));
              packageLock.version = newVersion;
              if (packageLock.packages && packageLock.packages['']) {
                packageLock.packages[''].version = newVersion;
              }
              fs.writeFileSync('package-lock.json', JSON.stringify(packageLock, null, 2) + '\n');
              console.log('已更新package-lock.json版本号为: ' + packageLock.version);
            }
          "

      - name: install frontend dependencies
        run: yarn install

      - name: Commit version update
        if: ${{ github.event_name == 'workflow_dispatch' && github.event.inputs.version == '' }}
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add package.json package-lock.json
          git diff --staged --quiet || git commit -m "🔖 自动递增版本号至 v${{ steps.get_version.outputs.new_version }}"
          git push

      - name: Create custom Tauri config  # 创建自定义配置文件
        run: |
          echo '{
            "productName": "${{ github.event.inputs.app_name != '' && github.event.inputs.app_name || 'mini-editor-pro' }}",
            "version": "${{ steps.get_version.outputs.new_version }}"
          }' > custom_config.json
          cat custom_config.json

      - name: Set Windows encoding  # 设置Windows环境的编码
        if: matrix.platform == 'windows-latest'
        shell: pwsh
        run: |
          [Console]::OutputEncoding = [System.Text.Encoding]::UTF8
          [Console]::InputEncoding = [System.Text.Encoding]::UTF8
          $env:PYTHONUTF8=1
          $env:PYTHONIOENCODING="utf-8"

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: './src-tauri -> target'

      - name: Build Tauri App  # 执行Tauri构建和发布
        id: build_app
        uses: tauri-apps/tauri-action@v0.5.20  # 更新到最新版本 v0.5.20
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}  # GitHub Token用于发布release
          CHCP: "65001"  # 设置代码页为UTF-8
        with:
          tagName: v${{ steps.get_version.outputs.new_version }}
          releaseName: "v${{ steps.get_version.outputs.new_version }}"
          releaseBody: "请查看CHANGELOG.md了解详细更新内容"
          prerelease: false
          args: ${{ matrix.args }} --config custom_config.json
          # Tauri 2.0 推荐配置
          updaterJsonPreferNsis: true  # 对于 Tauri v2 项目推荐设为 true
          includeUpdaterJson: true     # 是否上传更新器 JSON 文件

      - name: Download and rename release assets  # 下载并重命名Release文件
        shell: pwsh
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          # 等待一段时间确保Release创建完成
          Start-Sleep -Seconds 30

          Write-Host "开始下载并重命名Release文件..."

          # 获取应用名称和版本号
          $APP_NAME = "mini-editor-pro"
          $VERSION = "v${{ steps.get_version.outputs.new_version }}"

          # 根据平台设置架构和平台名称
          if ("${{ matrix.platform }}" -eq "windows-latest") {
            $PLATFORM_NAME = "Windows"
            if ("${{ matrix.args }}" -like "*aarch64*") {
              $ARCH = "ARM64"
            } elseif ("${{ matrix.args }}" -like "*i686*") {
              $ARCH = "32位"
            } else {
              $ARCH = "64位"
            }
          } elseif ("${{ matrix.platform }}" -eq "macos-latest") {
            $PLATFORM_NAME = "macOS"
            if ("${{ matrix.args }}" -like "*aarch64*") {
              $ARCH = "Apple芯片"
            } else {
              $ARCH = "Intel芯片"
            }
          } elseif ("${{ matrix.platform }}" -eq "ubuntu-22.04") {
            $PLATFORM_NAME = "Linux"
            $ARCH = "64位"
          }

          # 构建新的文件名前缀
          $NEW_PREFIX = "$APP_NAME-$VERSION-$PLATFORM_NAME-$ARCH"

          Write-Host "使用重命名前缀: $NEW_PREFIX"

          # 获取Release信息
          try {
            $RELEASE_INFO = gh api repos/${{ github.repository }}/releases/tags/v${{ steps.get_version.outputs.new_version }} | ConvertFrom-Json
            $RELEASE_ID = $RELEASE_INFO.id

            Write-Host "Release ID: $RELEASE_ID"

            # 获取Release中的文件列表
            $ASSETS = $RELEASE_INFO.assets

            # 为当前平台筛选相关文件并重命名
            foreach ($asset in $ASSETS) {
              $assetName = $asset.name
              $downloadUrl = $asset.browser_download_url

              # 检查是否是当前平台的文件（简单的文件名匹配）
              $shouldRename = $false
              if ("${{ matrix.platform }}" -eq "windows-latest" -and ($assetName -like "*.exe" -or $assetName -like "*.msi")) {
                $shouldRename = $true
              } elseif ("${{ matrix.platform }}" -eq "macos-latest" -and ($assetName -like "*.dmg" -or $assetName -like "*.app")) {
                $shouldRename = $true
              } elseif ("${{ matrix.platform }}" -eq "ubuntu-22.04" -and ($assetName -like "*.AppImage" -or $assetName -like "*.deb" -or $assetName -like "*.rpm")) {
                $shouldRename = $true
              }

              if ($shouldRename) {
                Write-Host "处理文件: $assetName"

                # 确定新文件名
                $extension = [System.IO.Path]::GetExtension($assetName)
                $newFileName = "$NEW_PREFIX$extension"

                Write-Host "重命名: $assetName -> $newFileName"

                # 下载原文件
                Invoke-WebRequest -Uri $downloadUrl -OutFile $assetName

                # 重命名文件
                Rename-Item -Path $assetName -NewName $newFileName

                # 删除原文件并上传重命名后的文件
                gh release delete-asset v${{ steps.get_version.outputs.new_version }} $assetName --repo ${{ github.repository }} --yes
                gh release upload v${{ steps.get_version.outputs.new_version }} $newFileName --repo ${{ github.repository }}

                # 清理本地文件
                Remove-Item -Path $newFileName -Force

                Write-Host "完成重命名: $newFileName"
              }
            }

            Write-Host "文件重命名完成"
          } catch {
            Write-Host "重命名过程中出现错误: $_"
            Write-Host "跳过重命名步骤"
          }

      # 仅在一个平台上创建新标签以避免冲突
      - name: Create new tag
        if: ${{ matrix.platform == 'macos-latest' && github.event_name == 'workflow_dispatch' }}
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git tag -a ${{ steps.get_version.outputs.new_tag }} -m "Release ${{ steps.get_version.outputs.new_tag }}"
          git push origin ${{ steps.get_version.outputs.new_tag }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}