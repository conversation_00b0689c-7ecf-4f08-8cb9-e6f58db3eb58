{
    "identifier": "com.mini-editor-pro.app",
    "windows": [
        "main",
        "search_color",
        "edit_config"
    ],
    "permissions": [
        "clipboard-manager:default",
        "clipboard-manager:allow-clear",
        "clipboard-manager:allow-read-image",
        "clipboard-manager:allow-read-text",
        "clipboard-manager:allow-write-html",
        "clipboard-manager:allow-write-image",
        "clipboard-manager:allow-write-text",
        "core:default",
        "core:app:default",
        "core:app:allow-app-hide",
        "core:app:allow-app-show",
        "core:app:allow-default-window-icon",
        "core:app:allow-name",
        "core:app:allow-set-app-theme",
        "core:app:allow-tauri-version",
        "core:app:allow-version",
        "core:event:default",
        "core:event:allow-emit",
        "core:event:allow-emit-to",
        "core:event:allow-listen",
        "core:event:allow-unlisten",
        "core:image:default",
        "core:image:allow-from-bytes",
        "core:image:allow-from-path",
        "core:image:allow-new",
        "core:image:allow-rgba",
        "core:image:allow-size",
        "core:menu:default",
        "core:menu:allow-append",
        "core:menu:allow-create-default",
        "core:menu:allow-get",
        "core:menu:allow-insert",
        "core:menu:allow-is-checked",
        "core:menu:allow-is-enabled",
        "core:menu:allow-items",
        "core:menu:allow-new",
        "core:menu:allow-popup",
        "core:menu:allow-prepend",
        "core:menu:allow-remove",
        "core:menu:allow-remove-at",
        "core:menu:allow-set-accelerator",
        "core:menu:allow-set-as-app-menu",
        "core:menu:allow-set-as-help-menu-for-nsapp",
        "core:menu:allow-set-as-window-menu",
        "core:menu:allow-set-as-windows-menu-for-nsapp",
        "core:menu:allow-set-checked",
        "core:menu:allow-set-enabled",
        "core:menu:allow-set-icon",
        "core:menu:allow-set-text",
        "core:menu:allow-text",
        "core:path:default",
        "core:path:allow-basename",
        "core:path:allow-dirname",
        "core:path:allow-extname",
        "core:path:allow-is-absolute",
        "core:path:allow-join",
        "core:path:allow-normalize",
        "core:path:allow-resolve",
        "core:path:allow-resolve-directory",
        "core:resources:default",
        "core:resources:allow-close",
        "core:tray:default",
        "core:tray:allow-get-by-id",
        "core:tray:allow-new",
        "core:tray:allow-remove-by-id",
        "core:tray:allow-set-icon",
        "core:tray:allow-set-icon-as-template",
        "core:tray:allow-set-menu",
        "core:tray:allow-set-show-menu-on-left-click",
        "core:tray:allow-set-temp-dir-path",
        "core:tray:allow-set-title",
        "core:tray:allow-set-tooltip",
        "core:tray:allow-set-visible",
        "core:webview:default",
        "core:webview:allow-clear-all-browsing-data",
        "core:webview:allow-create-webview",
        "core:webview:allow-create-webview-window",
        "core:webview:allow-get-all-webviews",
        "core:webview:allow-internal-toggle-devtools",
        "core:webview:allow-print",
        "core:webview:allow-reparent",
        "core:webview:allow-set-webview-focus",
        "core:webview:allow-set-webview-position",
        "core:webview:allow-set-webview-size",
        "core:webview:allow-set-webview-zoom",
        "core:webview:allow-webview-close",
        "core:webview:allow-webview-hide",
        "core:webview:allow-webview-position",
        "core:webview:allow-webview-show",
        "core:webview:allow-webview-size",
        "core:window:default",
        "core:window:allow-available-monitors",
        "core:window:allow-center",
        "core:window:allow-close",
        "core:window:allow-create",
        "core:window:allow-current-monitor",
        "core:window:allow-cursor-position",
        "core:window:allow-destroy",
        "core:window:allow-get-all-windows",
        "core:window:allow-hide",
        "core:window:allow-inner-position",
        "core:window:allow-inner-size",
        "core:window:allow-internal-toggle-maximize",
        "core:window:allow-is-closable",
        "core:window:allow-is-decorated",
        "core:window:allow-is-enabled",
        "core:window:allow-is-focused",
        "core:window:allow-is-fullscreen",
        "core:window:allow-is-maximizable",
        "core:window:allow-is-maximized",
        "core:window:allow-is-minimizable",
        "core:window:allow-is-minimized",
        "core:window:allow-is-resizable",
        "core:window:allow-is-visible",
        "core:window:allow-maximize",
        "core:window:allow-minimize",
        "core:window:allow-monitor-from-point",
        "core:window:allow-outer-position",
        "core:window:allow-outer-size",
        "core:window:allow-primary-monitor",
        "core:window:allow-request-user-attention",
        "core:window:allow-scale-factor",
        "core:window:allow-set-always-on-bottom",
        "core:window:allow-set-always-on-top",
        "core:window:allow-set-closable",
        "core:window:allow-set-content-protected",
        "core:window:allow-set-cursor-grab",
        "core:window:allow-set-cursor-icon",
        "core:window:allow-set-cursor-position",
        "core:window:allow-set-cursor-visible",
        "core:window:allow-set-decorations",
        "core:window:allow-set-effects",
        "core:window:allow-set-enabled",
        "core:window:allow-set-focus",
        "core:window:allow-set-fullscreen",
        "core:window:allow-set-icon",
        "core:window:allow-set-ignore-cursor-events",
        "core:window:allow-set-max-size",
        "core:window:allow-set-maximizable",
        "core:window:allow-set-min-size",
        "core:window:allow-set-minimizable",
        "core:window:allow-set-position",
        "core:window:allow-set-progress-bar",
        "core:window:allow-set-resizable",
        "core:window:allow-set-shadow",
        "core:window:allow-set-size",
        "core:window:allow-set-size-constraints",
        "core:window:allow-set-skip-taskbar",
        "core:window:allow-set-theme",
        "core:window:allow-set-title",
        "core:window:allow-set-title-bar-style",
        "core:window:allow-set-visible-on-all-workspaces",
        "core:window:allow-show",
        "core:window:allow-start-dragging",
        "core:window:allow-start-resize-dragging",
        "core:window:allow-theme",
        "core:window:allow-title",
        "core:window:allow-toggle-maximize",
        "core:window:allow-unmaximize",
        "core:window:allow-unminimize",
        "dialog:default",
        "dialog:allow-ask",
        "dialog:allow-confirm",
        "dialog:allow-message",
        "dialog:allow-open",
        "dialog:allow-save",
        "fs:default",
        "fs:allow-copy-file",
        "fs:allow-create",
        "fs:allow-exists",
        "fs:allow-fstat",
        "fs:allow-ftruncate",
        "fs:allow-lstat",
        "fs:allow-mkdir",
        "fs:allow-open",
        "fs:allow-read",
        "fs:allow-read-dir",
        "fs:allow-read-file",
        "fs:allow-read-text-file",
        "fs:allow-read-text-file-lines",
        "fs:allow-read-text-file-lines-next",
        "fs:allow-remove",
        "fs:allow-rename",
        "fs:allow-seek",
        "fs:allow-size",
        "fs:allow-stat",
        "fs:allow-truncate",
        "fs:allow-unwatch",
        "fs:allow-watch",
        "fs:allow-write",
        "fs:allow-write-file",
        "fs:allow-write-text-file",
        "fs:create-app-specific-dirs",
        "fs:read-all",
        "fs:read-app-specific-dirs-recursive",
        "fs:read-dirs",
        "fs:read-files",
        "fs:read-meta",
        "fs:scope",
        "fs:scope-app",
        "fs:scope-app-index",
        "fs:scope-app-recursive",
        "fs:scope-appcache",
        "fs:scope-appcache-index",
        "fs:scope-appcache-recursive",
        "fs:scope-appconfig",
        "fs:scope-appconfig-index",
        "fs:scope-appconfig-recursive",
        "fs:scope-appdata",
        "fs:scope-appdata-index",
        "fs:scope-appdata-recursive",
        "fs:scope-applocaldata",
        "fs:scope-applocaldata-index",
        "fs:scope-applocaldata-recursive",
        "fs:scope-applog",
        "fs:scope-applog-index",
        "fs:scope-applog-recursive",
        "fs:scope-audio",
        "fs:scope-audio-index",
        "fs:scope-audio-recursive",
        "fs:scope-cache",
        "fs:scope-cache-index",
        "fs:scope-cache-recursive",
        "fs:scope-config",
        "fs:scope-config-index",
        "fs:scope-config-recursive",
        "fs:scope-data",
        "fs:scope-data-index",
        "fs:scope-data-recursive",
        "fs:scope-desktop",
        "fs:scope-desktop-index",
        "fs:scope-desktop-recursive",
        "fs:scope-document",
        "fs:scope-document-index",
        "fs:scope-document-recursive",
        "fs:scope-download",
        "fs:scope-download-index",
        "fs:scope-download-recursive",
        "fs:scope-exe",
        "fs:scope-exe-index",
        "fs:scope-exe-recursive",
        "fs:scope-font",
        "fs:scope-font-index",
        "fs:scope-font-recursive",
        "fs:scope-home",
        "fs:scope-home-index",
        "fs:scope-home-recursive",
        "fs:scope-localdata",
        "fs:scope-localdata-index",
        "fs:scope-localdata-recursive",
        "fs:scope-log",
        "fs:scope-log-index",
        "fs:scope-log-recursive",
        "fs:scope-picture",
        "fs:scope-picture-index",
        "fs:scope-picture-recursive",
        "fs:scope-public",
        "fs:scope-public-index",
        "fs:scope-public-recursive",
        "fs:scope-resource",
        "fs:scope-resource-index",
        "fs:scope-resource-recursive",
        "fs:scope-runtime",
        "fs:scope-runtime-index",
        "fs:scope-runtime-recursive",
        "fs:scope-temp",
        "fs:scope-temp-index",
        "fs:scope-temp-recursive",
        "fs:scope-template",
        "fs:scope-template-index",
        "fs:scope-template-recursive",
        "fs:scope-video",
        "fs:scope-video-index",
        "fs:scope-video-recursive",
        "fs:write-all",
        "fs:write-files",
        "fs:allow-app-meta",
        "fs:allow-app-meta-recursive",
        "fs:allow-app-read",
        "fs:allow-app-read-recursive",
        "fs:allow-app-write",
        "fs:allow-app-write-recursive",
        "fs:allow-appcache-meta",
        "fs:allow-appcache-meta-recursive",
        "fs:allow-appcache-read",
        "fs:allow-appcache-read-recursive",
        "fs:allow-appcache-write",
        "fs:allow-appcache-write-recursive",
        "fs:allow-appconfig-meta",
        "fs:allow-appconfig-meta-recursive",
        "fs:allow-appconfig-read",
        "fs:allow-appconfig-read-recursive",
        "fs:allow-appconfig-write",
        "fs:allow-appconfig-write-recursive",
        "fs:allow-appdata-meta",
        "fs:allow-appdata-meta-recursive",
        "fs:allow-appdata-read",
        "fs:allow-appdata-read-recursive",
        "fs:allow-appdata-write",
        "fs:allow-appdata-write-recursive",
        "fs:allow-applocaldata-meta",
        "fs:allow-applocaldata-meta-recursive",
        "fs:allow-applocaldata-read",
        "fs:allow-applocaldata-read-recursive",
        "fs:allow-applocaldata-write",
        "fs:allow-applocaldata-write-recursive",
        "fs:allow-applog-meta",
        "fs:allow-applog-meta-recursive",
        "fs:allow-applog-read",
        "fs:allow-applog-read-recursive",
        "fs:allow-applog-write",
        "fs:allow-applog-write-recursive",
        "fs:allow-audio-meta",
        "fs:allow-audio-meta-recursive",
        "fs:allow-audio-read",
        "fs:allow-audio-read-recursive",
        "fs:allow-audio-write",
        "fs:allow-audio-write-recursive",
        "fs:allow-cache-meta",
        "fs:allow-cache-meta-recursive",
        "fs:allow-cache-read",
        "fs:allow-cache-read-recursive",
        "fs:allow-cache-write",
        "fs:allow-cache-write-recursive",
        "fs:allow-config-meta",
        "fs:allow-config-meta-recursive",
        "fs:allow-config-read",
        "fs:allow-config-read-recursive",
        "fs:allow-config-write",
        "fs:allow-config-write-recursive",
        "fs:allow-data-meta",
        "fs:allow-data-meta-recursive",
        "fs:allow-data-read",
        "fs:allow-data-read-recursive",
        "fs:allow-data-write",
        "fs:allow-data-write-recursive",
        "fs:allow-desktop-meta",
        "fs:allow-desktop-meta-recursive",
        "fs:allow-desktop-read",
        "fs:allow-desktop-read-recursive",
        "fs:allow-desktop-write",
        "fs:allow-desktop-write-recursive",
        "fs:allow-document-meta",
        "fs:allow-document-meta-recursive",
        "fs:allow-document-read",
        "fs:allow-document-read-recursive",
        "fs:allow-document-write",
        "fs:allow-document-write-recursive",
        "fs:allow-download-meta",
        "fs:allow-download-meta-recursive",
        "fs:allow-download-read",
        "fs:allow-download-read-recursive",
        "fs:allow-download-write",
        "fs:allow-download-write-recursive",
        "fs:allow-exe-meta",
        "fs:allow-exe-meta-recursive",
        "fs:allow-exe-read",
        "fs:allow-exe-read-recursive",
        "fs:allow-exe-write",
        "fs:allow-exe-write-recursive",
        "fs:allow-font-meta",
        "fs:allow-font-meta-recursive",
        "fs:allow-font-read",
        "fs:allow-font-read-recursive",
        "fs:allow-font-write",
        "fs:allow-font-write-recursive",
        "fs:allow-home-meta",
        "fs:allow-home-meta-recursive",
        "fs:allow-home-read",
        "fs:allow-home-read-recursive",
        "fs:allow-home-write",
        "fs:allow-home-write-recursive",
        "fs:allow-localdata-meta",
        "fs:allow-localdata-meta-recursive",
        "fs:allow-localdata-read",
        "fs:allow-localdata-read-recursive",
        "fs:allow-localdata-write",
        "fs:allow-localdata-write-recursive",
        "fs:allow-log-meta",
        "fs:allow-log-meta-recursive",
        "fs:allow-log-read",
        "fs:allow-log-read-recursive",
        "fs:allow-log-write",
        "fs:allow-log-write-recursive",
        "fs:allow-picture-meta",
        "fs:allow-picture-meta-recursive",
        "fs:allow-picture-read",
        "fs:allow-picture-read-recursive",
        "fs:allow-picture-write",
        "fs:allow-picture-write-recursive",
        "fs:allow-public-meta",
        "fs:allow-public-meta-recursive",
        "fs:allow-public-read",
        "fs:allow-public-read-recursive",
        "fs:allow-public-write",
        "fs:allow-public-write-recursive",
        "fs:allow-resource-meta",
        "fs:allow-resource-meta-recursive",
        "fs:allow-resource-read",
        "fs:allow-resource-read-recursive",
        "fs:allow-resource-write",
        "fs:allow-resource-write-recursive",
        "fs:allow-runtime-meta",
        "fs:allow-runtime-meta-recursive",
        "fs:allow-runtime-read",
        "fs:allow-runtime-read-recursive",
        "fs:allow-runtime-write",
        "fs:allow-runtime-write-recursive",
        "fs:allow-temp-meta",
        "fs:allow-temp-meta-recursive",
        "fs:allow-temp-read",
        "fs:allow-temp-read-recursive",
        "fs:allow-temp-write",
        "fs:allow-temp-write-recursive",
        "fs:allow-template-meta",
        "fs:allow-template-meta-recursive",
        "fs:allow-template-read",
        "fs:allow-template-read-recursive",
        "fs:allow-template-write",
        "fs:allow-template-write-recursive",
        "fs:allow-video-meta",
        "fs:allow-video-meta-recursive",
        "fs:allow-video-read",
        "fs:allow-video-read-recursive",
        "fs:allow-video-write",
        "fs:allow-video-write-recursive",
        "store:default",
        "store:allow-clear",
        "store:allow-delete",
        "store:allow-entries",
        "store:allow-get",
        "store:allow-get-store",
        "store:allow-has",
        "store:allow-keys",
        "store:allow-length",
        "store:allow-load",
        "store:allow-reload",
        "store:allow-reset",
        "store:allow-save",
        "store:allow-set",
        "store:allow-values",
        "os:default",
        "os:allow-platform",
        "os:allow-version",
        "os:allow-os-type",
        "os:allow-arch",
        "shellx:allow-execute",
        "shellx:allow-fix-path-env",
        "log:default",
        "log:allow-log",
        // "window-state:default",
        "opener:allow-open-url",
        "opener:allow-open-path",
        // "window-state:allow-restore-window-state",
        // "window-state:allow-save-window-state",
        "shellx:allow-spawn",
        "shellx:allow-kill",
        "shellx:allow-execute",
        "global-shortcut:allow-register",
        "global-shortcut:allow-is-registered",
        "global-shortcut:allow-unregister",
        "global-shortcut:allow-unregister-all",
        "os:default",
    ]
}