#!/bin/bash

SCRIPT_NAME=$(basename "$0")

# 单个视频转码函数
process_single_video() {
    local input_path="$1"

    if [[ ! -f "$input_path" ]]; then
        echo "❌ 文件不存在: $input_path"
        return 1
    fi

    local dir=$(dirname "$input_path")
    local filename=$(basename "$input_path")
    local ext="${filename##*.}"
    local base="${filename%.*}"
    local temp_file="$dir/${base}_tmp.mp4"
    local final_output="$dir/${base}.mp4"

    echo "🎬 开始处理: $input_path"

    # 如果输入已经是mp4格式，保持原有逻辑
    if [[ "$ext" == "mp4" ]]; then
        ffmpeg -i "$input_path" -c:v libx264 -preset medium \
            -x264-params "num_reorder_frames=2:max_dec_frame_buffering=4" \
            -c:a aac "$temp_file" -y

        if [[ $? -ne 0 ]]; then
            echo "❌ 转码失败: $input_path"
            rm -f "$temp_file"
            return 1
        fi

        mv -f "$temp_file" "$input_path"
        final_output="$input_path"
    else
        # 其他格式转换为mp4
        ffmpeg -i "$input_path" -c:v libx264 -preset medium \
            -x264-params "num_reorder_frames=2:max_dec_frame_buffering=4" \
            -c:a aac "$final_output" -y

        if [[ $? -ne 0 ]]; then
            echo "❌ 转码失败: $input_path"
            rm -f "$final_output"
            return 1
        fi

        # 删除原文件
        rm -f "$input_path"
        echo "📁 格式转换: $filename -> ${base}.mp4"
    fi

    echo "✅ 替换完成，校验参数中..."
    ffmpeg -i "$final_output" -c:v copy -bsf:v trace_headers -f null - 2>&1 | grep -E "num_reorder_frames|max_dec_frame_buffering"

    echo "✅ 完成: $final_output"
    echo "--------------------------------------"
}

# 批量处理目录下的所有视频文件
process_directory_videos() {
    local folder="$1"
    local recursive="$2"

    if [[ ! -d "$folder" ]]; then
        echo "❌ 指定目录不存在: $folder"
        echo "   实际路径: $folder"
        return 1
    fi

    echo "📁 开始扫描目录: $folder"

    # 使用兼容zsh的方式来构建文件数组，支持多种视频格式
    video_files=()
    if [[ "$recursive" == "true" ]]; then
        while IFS= read -r -d '' file; do
            video_files+=("$file")
        done < <(find "$folder" -type f \( -name "*.mp4" -o -name "*.mov" -o -name "*.avi" -o -name "*.mkv" -o -name "*.webm" -o -name "*.m4v" -o -name "*.wmv" -o -name "*.flv" \) -print0)
    else
        while IFS= read -r -d '' file; do
            video_files+=("$file")
        done < <(find "$folder" -maxdepth 1 -type f \( -name "*.mp4" -o -name "*.mov" -o -name "*.avi" -o -name "*.mkv" -o -name "*.webm" -o -name "*.m4v" -o -name "*.wmv" -o -name "*.flv" \) -print0)
    fi

    if [[ ${#video_files[@]} -eq 0 ]]; then
        echo "⚠️ 未找到视频文件"
        echo "   搜索路径: $folder"
        echo "   递归搜索: $recursive"
        echo "   支持格式: mp4, mov, avi, mkv, webm, m4v, wmv, flv"
        return 0
    fi

    echo "📊 找到 ${#video_files[@]} 个视频文件"
    for video in "${video_files[@]}"; do
        process_single_video "$video"
    done

    echo "🎉 所有视频处理完成"
}

# 用法提示
usage() {
    echo "为视频添加num_reorder_frames=2和max_dec_frame_buffering=4参数"
    echo "用法: ${SCRIPT_NAME} <目录路径> [对所有视频操作:true|false]"
    echo "支持格式: mp4, mov, avi, mkv, webm, m4v, wmv, flv"
    echo "输出格式: 所有视频最终统一转换为mp4格式"
    echo "示例: ${SCRIPT_NAME} /path/to/videos true"
}

# 主入口
if [[ "$#" -lt 1 ]]; then
    usage
    exit 1
fi

process_directory_videos "$1" "${2:-false}"
