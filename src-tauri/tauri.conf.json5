{
    productName: "mini-editor-pro",
    version: "0.2.14",
    identifier: "com.mini-editor-pro.app",
    build: {
        devUrl: "http://localhost:3000",
        beforeBuildCommand: "yarn build",
        beforeDevCommand: "yarn dev",
        frontendDist: "../dist"
    },
    app: {
        withGlobalTauri: true,
        macOSPrivateApi: true,
        windows: [
            {
                title: "mini-editor-pro",
                width: 220,
                height: 420,
                maxWidth: 220,
                maxHeight: 420,
                minWidth: 220,
                minHeight: 420,
                backgroundColor: "#00000000",
                decorations: false,
                transparent: true,
                // titleBarStyle: true,
                acceptFirstMouse: true,
                alwaysOnTop: true,
                hiddenTitle: true,
                shadow: false,
                focus: true,
                dragDropEnabled: true,
                fullscreen: false,
                maximized: false,
                visibleOnAllWorkspaces: true,
                visible: false,
                resizable: false
            }
        ],
        security: {
            csp: null
        }
    },
    bundle: {
        active: true,
        targets: "all",
        resources: [
            "./cache/",
            "./random_color_theme/",
            "./config/*",
            "./drawable/",
            "../src/assets/**/*",
            "./Terminal/",
            "../dist/search_color.html"
        ],
        icon: [
            "icons/icon.png",
            "icons/icon.icns",
            "icons/icon.ico"
        ],
        externalBin: [
            "binary/aapt",
            "binary/adb"
        ],
        windows: {
            wix: {
                language: "zh-CN"
            }
        }
    }
}