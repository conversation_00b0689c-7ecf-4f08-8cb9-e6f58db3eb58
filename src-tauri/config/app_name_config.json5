{
    // 说明
    // 微信 = 大图标目录名称
    // com.tencent.mm = 应用的包名或类名
    // 目前只支持这么多，上传的时候服务端会校验应用是否在允许的列表中。
    "app_name": {
        "微信": "com.tencent.mm",
        "电话": "com.android.contacts-com.android.contacts.activities.TwelveKeyDialer",
        "联系人": "com.android.contacts-com.android.contacts.activities.PeopleActivity",
        "短信": "com.android.mms",
        "小米相册": "com.miui.gallery",
        "手机管家": "com.miui.securitycenter",
        "抖音": "com.ss.android.ugc.aweme",
        "时钟": "com.android.deskclock",
        "拼多多": "com.xunmeng.pinduoduo",
        "小米天气": "com.miui.weather2",
        "相机": "com.android.camera",
        "支付宝": "com.eg.android.AlipayGphone",
        "小米浏览器": "com.android.browser",
        "应用商店": "com.xiaomi.market",
        "QQ": "com.tencent.mobileqq",
        "淘宝": "com.taobao.taobao",
        "日历": "com.android.calendar",
        "计算器": "com.miui.calculator",
        "快手极速版": "com.kuaishou.nebula",
        "小米视频": "com.miui.video",
        "抖音极速版": "com.ss.android.ugc.aweme.lite",
        "小米画报": "com.mfashiongallery.emag",
        "快手": "com.smile.gifmaker",
        "百度": "com.baidu.searchbox",
        "高德地图": "com.autonavi.minimap",
        "哔哩哔哩": "tv.danmaku.bili",
        "今日头条": "com.ss.android.article.news",
        "王者荣耀": "com.tencent.tmgp.sgame",
        "京东": "com.jingdong.app.mall",
        "美团": "com.sankuai.meituan",
        "小米笔记": "com.miui.notes",
        "钉钉": "com.alibaba.android.rimet",
        "QQ浏览器": "com.tencent.mtt",
        "腾讯视频": "com.tencent.qqlive",
        "今日头条极速版": "com.ss.android.article.lite",
        "番茄免费小说": "com.dragon.read",
        "微博": "com.sina.weibo",
        "QQ音乐": "com.tencent.qqmusic",
        "酷狗音乐": "com.kugou.android",
        "小红书": "com.xingin.xhs",
        "网易云音乐": "com.netease.cloudmusic",
        "UC浏览器": "com.UCMobile",
        "万能遥控": "com.duokan.phone.remotecontroller",
        "音乐": "com.miui.player",
        "主题壁纸": "com.android.thememanager",
        "小爱同学": "com.miui.voiceassist",
        "文件管理": "com.android.fileexplorer",
        "百度地图": "com.baidu.BaiduMap",
        "夸克": "com.quark.browser",
        "西瓜视频": "com.ss.android.article.video",
        "企业微信": "com.tencent.wework",
        "和平精英": "com.tencent.tmgp.pubgmhd",
        "优酷": "com.youku.phone",
        "爱奇艺": "com.qiyi.video",
        "闲鱼": "com.taobao.idlefish",
        "知乎": "com.zhihu.android",
        "WPS Office": "cn.wps.moffice_eng",
        "米家": "com.xiaomi.smarthome",
        "喜马拉雅": "com.ximalaya.ting.android",
        "游戏中心": "com.xiaomi.gamecenter",
        "下载管理": "com.android.providers.downloads.ui",
        "小米商城": "com.xiaomi.shop",
        "钱包": "com.mipay.wallet",
        "七猫免费小说": "com.kmxs.reader",
        "腾讯新闻": "com.tencent.news",
        "录音机": "com.android.soundrecorder",
        "设置": "com.android.settings",
        "抖音火山版": "com.ss.android.ugc.live",
        "饿了么": "me.ele",
        "百度网盘": "com.baidu.netdisk",
        "小米运动健康": "com.mi.health",
        "多看阅读": "com.duokan.reader",
        "剪映": "com.lemon.lv",
        "百度贴吧": "com.baidu.tieba",
        "美团外卖": "com.sankuai.meituan.takeoutnew",
        "小爱视觉": "com.xiaomi.scanner",
        "番茄畅听": "com.xs.fm",
        "虎牙直播": "com.duowan.kiwi",
        "开心消消乐": "com.happyelements.AndroidAnimal",
        "全球上网": "com.miui.virtualsim",
        "全民K歌": "com.tencent.karaoke",
        "金铲铲之战": "com.tencent.jkchess",
        "墨迹天气": "com.moji.mjweather",
        "米游社": "com.mihoyo.hyperion",
        "英雄联盟手游": "com.tencent.lolm",
        "QQ邮箱": "com.tencent.androidqqmail",
        "服务与反馈": "com.miui.miservice",
        "唯品会": "com.achievo.vipshop",
        "斗鱼": "air.tv.douyu.android",
        "得物": "com.shizhuang.duapp",
        "哈啰": "com.jingyao.easybike",
        "Soul": "cn.soulapp.android",
        "芒果TV": "com.hunantv.imgo.activity",
        "腾讯地图": "com.tencent.map",
        "大众点评": "com.dianping.v1",
        "王者营地": "com.tencent.gamehelper.smoba",
        "美图秀秀": "com.mt.mtxx.mtxx",
        "手机天猫": "com.tmall.wireless",
        "小米社区": "com.xiaomi.vipaccount",
        "TapTap": "com.taptap",
        "腾讯会议": "com.tencent.wemeet.app",
        "好看视频": "com.baidu.haokan",
        "酷我音乐": "cn.kuwo.player",
        "影视大全": "com.le123.ysdq",
        "起点读书": "com.qidian.QDReader",
        "最右": "cn.xiaochuankeji.tieba",
        "Keep": "com.gotokeep.keep",
        "掌上英雄联盟": "com.tencent.qt.qtl",
        "阿里巴巴": "com.alibaba.wireless",
        "暗区突围": "com.tencent.mf.uam",
        "皮皮虾": "com.sup.android.superb",
        "小米有品": "com.xiaomi.youpin",
        "迷你世界": "com.minitech.miniworld.TMobile.mi",
        "火影忍者": "com.tencent.KiHan",
        "携程旅行": "ctrip.android.view",
        "快影": "com.kwai.videoeditor",
        "电子邮件": "com.android.email",
        "穿越火线": "com.tencent.tmgp.cf",
        "指南针": "com.miui.compass",
        "美柚": "com.lingan.seeyou",
        "醒图": "com.xt.retouch",
        "爱奇艺极速版": "com.qiyi.video.lite",
        "微信读书": "com.tencent.weread",
        "网易有道词典": "com.youdao.dict",
        "Zepp Life": "com.xiaomi.hm.health",
        "轻颜相机": "com.gorgeous.lite",
        "我的世界": "com.netease.mc.mi",
        "微视": "com.tencent.weishi",
        "美颜相机": "com.meitu.meiyancamera",
        "使命召唤手游": "com.tencent.tmgp.cod",
        "LOFTER": "com.lofter.android",
        "书旗小说": "com.shuqi.controller",
        "QQ飞车手游": "com.tencent.tmgp.speedmobile",
        "光遇": "com.netease.sky.mi",
        "小米换机": "com.miui.huanji",
        "豆瓣": "com.douban.frodo",
        "原神": "com.miHoYo.ys.mi",
        "和平营地": "com.tencent.gamehelper.pg",
        "晋江小说阅读": "com.jjwxc.reader",
        "快看": "com.kuaikan.comic",
        "哔哩哔哩漫画": "com.bilibili.comic",
        "部落冲突": "com.supercell.clashofclans.mi",
        "腾讯动漫": "com.qq.ac.android",
        "QQ阅读": "com.qq.reader",
        "派派": "com.ifreetalk.ftalk",
        "阴阳师": "com.netease.onmyoji",
        "光与夜之恋": "com.tencent.tmgp.lv",
        "小米办公": "com.ss.android.lark.kami",
        "小米人": "com.mi.oa"
    }
}