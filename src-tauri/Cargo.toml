[package]
name = "mini-editor-pro"
version = "0.2.14"
description = "集成了一系列个性化调试功能"
authors = ["墙高高"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "tauri_mini_editor_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.0.6", features = [] }

[dependencies]
tauri = { version = "2.3.1", features = [ "macos-private-api", "devtools", "config-json5", "image-png"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tauri-plugin-persisted-scope = "2.2.0"
tauri-plugin-fs = "2.2.0"
tauri-plugin-dialog = "2.2.0"
tauri-plugin-store = "2.2.0"
tauri-plugin-shellx = "2.0.16"
tauri-plugin-log = "2.2.3"
tauri-plugin-clipboard-manager = "2.2.1"
tauri-plugin-opener = "2.2.6"
log = "0.4.26"
image = "0.24.7"
screenshots = "0.4.2"
zip = "0.6"
walkdir = "2.5"
tokio = { version = "1.43", features = ["full"] }
uuid = { version = "1.15", features = ["v4"] }
futures = "0.3"
indicatif = "0.17"
winapi = "0.3.9"
dirs = "5.0"
which = "7.0.2"
regex = "1.11.1"
rand = "0.9.0"
fs_extra = "1.3"
dashmap = "5.5.3"
imageproc = "0.23"
wgpu = "0.19"
naga = "0.14"
tokio-stream = "0.1"
futures-util = "0.3"
once_cell = "1.21.1"
chrono = "0.4"
quick-xml = "0.37.2"
lazy_static = "1.5.0"
json5 = "0.4"
mouse_position = "0.1.3"
clipboard = "0.5.0"
base64 = "0.22.0"
filetime = "0.2"
tauri-plugin-os = "2"

[target.'cfg(not(any(target_os = "android", target_os = "ios")))'.dependencies]
json5 = "0.4"
png = "0.17"
rayon = "1.10"
num_cpus = "1.16"
byteorder = "1.5"
crc32fast = "1.4"
delete = "1.0.1"
memmap2 = "0.9.5"
quick-xml = "0.37.2"
lazy_static = "1.5.0"
anyhow = "1.0"
flate2 = "1.1"
crossbeam-deque = "0.8"
whoami = "1.5.2"
bincode = "1.3"
fxhash = "0.2"
rusttype = "0.9"
directories = "5.0"
sha2 = "0.10.8"
tauri-plugin-global-shortcut = "2"

[build]
target-dir = "target"

[target.x86_64-pc-windows-msvc]
rustflags = ["-C", "target-feature=+crt-static"]

[target.'cfg(any(target_os = "macos", windows, target_os = "linux"))'.dependencies]
tauri-plugin-window-state = "2.2.2"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
