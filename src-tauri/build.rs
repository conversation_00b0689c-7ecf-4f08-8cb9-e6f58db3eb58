use std::env;
use std::fs;
use std::path::{Path, PathBuf};
use std::process::Command;

/// 动态获取目标三元组:
/// 1. 优先从环境变量 TARGET 获取 (如果存在)
/// 2. 若无, 尝试从 rustc -Vv 输出中提取 host
/// 3. 若都失败, 根据当前操作系统做一个简单的默认值
fn get_target_triple() -> String {
    // 1. 优先从环境变量获取
    if let Ok(target) = env::var("TARGET") {
        return target;
    }

    // 2. 尝试用 rustc -Vv 获取 host
    if let Ok(output) = Command::new("rustc").args(&["-Vv"]).output() {
        let output_str = String::from_utf8_lossy(&output.stdout);
        for line in output_str.lines() {
            // 形如: "host: x86_64-pc-windows-msvc"
            if line.starts_with("host: ") {
                return line[6..].to_string();
            }
        }
    }

    // 3. 如果都失败了, 就根据当前 OS 做简单默认值
    match env::consts::OS {
        "windows" => "x86_64-pc-windows-msvc".to_string(),
        "macos" => {
            if cfg!(target_arch = "aarch64") {
                "aarch64-apple-darwin".to_string()
            } else {
                "x86_64-apple-darwin".to_string()
            }
        }
        "linux" => "x86_64-unknown-linux-gnu".to_string(),
        _ => "unknown".to_string(),
    }
}

/// 检查指定目录（或文件）的写权限
fn check_directory_permissions(path: &Path) -> std::io::Result<()> {
    println!("cargo:warning=检查目录权限: {}", path.display());

    // 尝试创建临时文件来测试写入权限
    let test_file = path.join(".permission_test");
    match fs::write(&test_file, b"test") {
        Ok(_) => {
            // 清理测试文件
            if let Err(e) = fs::remove_file(&test_file) {
                println!("cargo:warning=清理测试文件失败，但不影响继续执行: {}", e);
            }
            Ok(())
        }
        Err(e) => {
            println!("cargo:warning=目录 {} 权限检查失败: {}", path.display(), e);
            Err(e)
        }
    }
}

/// 递归复制目录和文件，并为文件名添加目标三元组后缀（非DLL文件）
fn copy_directory_with_target(src: &Path, dst: &Path, target_triple: &str) -> std::io::Result<()> {
    println!(
        "cargo:warning=开始复制目录: {} -> {}",
        src.display(),
        dst.display()
    );

    if !src.exists() {
        println!("cargo:warning=源目录不存在: {}", src.display());
        return Ok(());
    }

    // 检查目标目录的权限
    if let Err(e) = check_directory_permissions(dst.parent().unwrap_or(dst)) {
        println!(
            "cargo:warning=目标目录权限检查失败，请以管理员权限运行或检查文件权限: {}",
            e
        );
        return Err(e);
    }

    // 确保目标目录存在
    fs::create_dir_all(dst)?;

    for entry in fs::read_dir(src)? {
        let entry = entry?;
        let src_path = entry.path();
        let file_name = entry.file_name().to_string_lossy().into_owned();

        // 构建目标文件名
        let dst_file_name = if src_path.is_dir() {
            file_name
        } else {
            // 获取文件名和扩展名
            let path = Path::new(&file_name);
            let stem = path
                .file_stem()
                .map(|s| s.to_string_lossy().into_owned())
                .unwrap_or_else(|| file_name.clone());
            let ext = path.extension().map(|e| e.to_string_lossy().into_owned());

            // 如果是 dll 文件则不加三元组
            if let Some(ext) = &ext {
                if ext.to_lowercase() == "dll" {
                    file_name
                } else {
                    format!("{}-{}.{}", stem, target_triple, ext)
                }
            } else {
                format!("{}-{}", stem, target_triple)
            }
        };

        let dst_path = dst.join(dst_file_name);

        if src_path.is_dir() {
            // 递归拷贝子目录
            copy_directory_with_target(&src_path, &dst_path, target_triple)?;
        } else {
            // 先删除已存在的文件 (覆盖)
            if dst_path.exists() {
                println!(
                    "cargo:warning=目标文件已存在，先删除: {}",
                    dst_path.display()
                );
                fs::remove_file(&dst_path)?;
            }

            println!(
                "cargo:warning=复制文件: {} -> {}",
                src_path.display(),
                dst_path.display()
            );
            fs::copy(&src_path, &dst_path)?;
        }
    }
    Ok(())
}

/// 确保指定目录为空，如果已存在则删除后重建
fn ensure_empty_dir(path: &Path) -> std::io::Result<()> {
    println!("cargo:warning=确保目录为空: {}", path.display());

    // 检查父级目录权限
    if let Err(e) = check_directory_permissions(path.parent().unwrap_or(path)) {
        println!(
            "cargo:warning=目录权限检查失败，请以管理员权限运行或检查文件权限: {}",
            e
        );
        return Err(e);
    }

    // 若目录已存在，则删除
    if path.exists() {
        println!("cargo:warning=尝试删除已存在的目录");
        match fs::remove_dir_all(path) {
            Ok(_) => println!("cargo:warning=成功删除目录"),
            Err(e) => {
                println!("cargo:warning=删除目录失败: {}", e);
                return Err(e);
            }
        }
    }

    println!("cargo:warning=创建新目录");
    fs::create_dir_all(path)
}

/// 在 Windows 下复制一些必要的 DLL (如 ADB 的DLL)，其他系统则直接返回 Ok
fn copy_adb_dlls(binary_dir: &Path, manifest_dir: &Path) -> std::io::Result<()> {
    // 仅在 Windows 下复制 DLL
    if env::consts::OS != "windows" {
        return Ok(());
    }

    println!("cargo:warning=开始复制 ADB DLL 文件到 src-tauri 目录");

    // 检查目标目录权限
    if let Err(e) = check_directory_permissions(manifest_dir) {
        println!(
            "cargo:warning=目标目录权限检查失败，请以管理员权限运行或检查文件权限: {}",
            e
        );
        return Err(e);
    }

    let dll_files = ["AdbWinApi.dll", "AdbWinUsbApi.dll"];

    for dll_file in dll_files {
        let source_path = binary_dir.join(dll_file);
        let target_path = manifest_dir.join(dll_file);

        // 如果目标文件已存在，先尝试删除
        if target_path.exists() {
            println!(
                "cargo:warning=目标DLL已存在，先删除: {}",
                target_path.display()
            );
            if let Err(e) = fs::remove_file(&target_path) {
                println!("cargo:warning=删除已存在的DLL文件失败: {}", e);
                continue;
            }
        }

        // 确保目标目录存在
        if let Some(parent) = target_path.parent() {
            fs::create_dir_all(parent)?;
        }

        // (可选) 设置文件权限，仅在 *nix 系统有效
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            if let Ok(metadata) = fs::metadata(&source_path) {
                let mut perms = metadata.permissions();
                perms.set_mode(0o755);
                fs::set_permissions(&source_path, perms)?;
            }
        }

        if source_path.exists() {
            println!(
                "cargo:warning=复制 {} -> {}",
                source_path.display(),
                target_path.display()
            );
            fs::copy(&source_path, &target_path)?;
        } else {
            println!("cargo:warning=源文件不存在: {}", source_path.display());
        }
    }

    Ok(())
}

fn main() {
    println!("cargo:warning===================构建脚本开始==================");
    println!("cargo:warning=当前用户权限检查");

    // 获取必要的路径
    let manifest_dir =
        PathBuf::from(env::var("CARGO_MANIFEST_DIR").expect("无法获取 CARGO_MANIFEST_DIR"));
    let project_root = manifest_dir.parent().expect("无法获取项目根目录");
    let binary_dir = manifest_dir.join("binary");
    let cache_dir = manifest_dir.join("cache");
    let random_color_theme_dir = manifest_dir.join("random_color_theme");
    // 检查关键目录的权限
    if let Err(e) = check_directory_permissions(&manifest_dir) {
        println!(
            "cargo:warning=manifest 目录权限检查失败，请以管理员权限运行或检查文件权限: {}",
            e
        );
        panic!("权限检查失败: {}", e);
    }

    // 获取当前 OS，用于区分要从 /binary 下的哪个目录复制
    let platform_dir = match env::consts::OS {
        "windows" => "windows",
        "macos" => "macos",
        "linux" => "linux",
        os => {
            println!("cargo:warning=不支持的操作系统: {}", os);
            return;
        }
    };

    // 动态获取三元组
    let target_triple = get_target_triple();

    println!("cargo:warning=当前平台: {}", platform_dir);
    println!("cargo:warning=目标三元组: {}", target_triple);
    println!("cargo:warning=项目根目录: {}", project_root.display());
    println!("cargo:warning=binary目录: {}", binary_dir.display());
    println!("cargo:warning=缓存目录: {}", cache_dir.display());

    // 确保缓存目录存在
    if let Err(e) = fs::create_dir_all(&cache_dir) {
        println!("cargo:warning=创建缓存目录失败: {}", e);
    }

    // 确保 random_color_theme 目录存在
    if let Err(e) = fs::create_dir_all(&random_color_theme_dir) {
        println!("cargo:warning=创建 random_color_theme 目录失败: {}", e);
    }

    // 确保 binary 目录为空
    if let Err(e) = ensure_empty_dir(&binary_dir) {
        println!("cargo:warning=准备 binary 目录失败: {}", e);
        return;
    }

    // 复制平台相关文件
    let source_dir = project_root.join("binary").join(platform_dir);
    println!("cargo:warning=源目录: {}", source_dir.display());

    if let Err(e) = copy_directory_with_target(&source_dir, &binary_dir, &target_triple) {
        println!("cargo:warning=复制文件失败: {}", e);
    }

    // 如果是 Windows，就复制ADB DLL到 src-tauri
    if let Err(e) = copy_adb_dlls(&source_dir, &manifest_dir) {
        println!("cargo:warning=复制 ADB DLL 文件失败: {}", e);
    }

    // 执行 Tauri 构建
    println!("cargo:warning=开始执行 Tauri 构建");
    tauri_build::build();
    println!("cargo:warning=Tauri 构建成功完成");

    println!("cargo:warning===================构建脚本完成==================");
}
