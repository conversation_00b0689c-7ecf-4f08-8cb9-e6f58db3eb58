use image::{<PERSON>g<PERSON>, RgbaImage};
use log::{debug, error, info, warn};
use memmap2::Mmap;
use rayon::prelude::*;
use std::fs::File;
use std::io::{self, Read};
use tauri::Error as InvokeError;

#[derive(Debug)]
struct NinePatchData {
    pad_left: i32,
    pad_right: i32,
    pad_top: i32,
    pad_bottom: i32,
    x_divs: Vec<i32>,
    y_divs: Vec<i32>,
}

#[derive(Debug)]
struct OriginSize {
    width: i32,
    height: i32,
}

#[derive(Debug)]
struct MyBuffer {
    buffer: Vec<u8>,
}

impl MyBuffer {
    fn new(data: Vec<u8>) -> Self {
        MyBuffer { buffer: data }
    }

    fn skip_bytes(&mut self, skip: usize) {
        if skip < self.buffer.len() {
            self.buffer = self.buffer[skip..].to_owned();
        }
    }

    fn read_byte(&mut self) -> u8 {
        if !self.buffer.is_empty() {
            let result = self.buffer[0];
            self.skip_bytes(1);
            result
        } else {
            0
        }
    }

    fn read_int(&mut self) -> i32 {
        let ch1 = self.read_byte() as i32;
        let ch2 = self.read_byte() as i32;
        let ch3 = self.read_byte() as i32;
        let ch4 = self.read_byte() as i32;
        (ch1 << 24) + (ch2 << 16) + (ch3 << 8) + ch4
    }

    fn slice(&self, start: usize, end: Option<usize>) -> &[u8] {
        match end {
            Some(e) if e <= self.buffer.len() && start <= e => &self.buffer[start..e],
            None if start <= self.buffer.len() => &self.buffer[start..],
            _ => &[],
        }
    }
}

fn read_file_content(file_path: &str) -> io::Result<Vec<u8>> {
    let file = File::open(file_path)?;
    if file.metadata()?.len() > 1024 * 1024 {
        debug!("使用内存映射读取大文件: {}", file_path);
        let mmap = unsafe { Mmap::map(&file)? };
        Ok(mmap.to_vec())
    } else {
        debug!("使用标准IO读取小文件: {}", file_path);
        let mut buffer = Vec::new();
        File::open(file_path)?.read_to_end(&mut buffer)?;
        Ok(buffer)
    }
}

fn has_nptc_chunk(buffer: &[u8]) -> bool {
    const CHUNK_SIZE: usize = 1024 * 1024;
    if buffer.len() < 4 {
        warn!("文件太小,无法包含npTc块");
        return false;
    }
    if buffer.len() <= CHUNK_SIZE {
        debug!("使用单线程搜索npTc块");
        return buffer.windows(4).any(|window| window == b"npTc");
    }
    debug!("使用并行搜索npTc块");
    buffer.par_chunks(CHUNK_SIZE).any(|chunk| {
        if chunk.len() < 4 {
            return false;
        }
        chunk.windows(4).any(|window| window == b"npTc")
    })
}

fn find_9patch_chunk(mut buffer: MyBuffer) -> MyBuffer {
    debug!("开始查找9patch块");
    buffer.skip_bytes(8);
    let mut i = 1;
    while i < buffer.buffer.len() - 4 {
        if buffer.slice(i, Some(i + 4)) == b"npTc" {
            buffer.skip_bytes(i + 4);
            debug!("找到9patch块");
            break;
        }
        i += 4;
    }
    buffer
}

fn find_ihdr_chunk(buffer: &mut MyBuffer) {
    debug!("开始查找IHDR块");
    buffer.skip_bytes(16);
    let mut i = 1;
    while i < buffer.buffer.len() - 4 {
        if buffer.slice(i, Some(i + 4)) == b"IHDR" {
            buffer.skip_bytes(i + 4);
            debug!("找到IHDR块");
            break;
        }
        i += 4;
    }
}

fn decode_nine_patch_data(file_path: &str) -> io::Result<(NinePatchData, OriginSize)> {
    info!("开始解码9patch数据: {}", file_path);
    let buffer = read_file_content(file_path)?;

    if buffer.len() < 4 || buffer[1..4] != b"PNG"[..] {
        error!("文件不是PNG格式");
        return Err(io::Error::new(io::ErrorKind::InvalidData, "不是PNG文件"));
    }

    let mut ihdr_buffer = MyBuffer::new(buffer.clone());
    find_ihdr_chunk(&mut ihdr_buffer);
    let origin_size = OriginSize {
        width: ihdr_buffer.read_int(),
        height: ihdr_buffer.read_int(),
    };
    debug!("原始尺寸: {}x{}", origin_size.width, origin_size.height);

    let mut nine_patch_data = NinePatchData {
        pad_left: 0,
        pad_right: 0,
        pad_top: 0,
        pad_bottom: 0,
        x_divs: Vec::new(),
        y_divs: Vec::new(),
    };

    if buffer.len() >= 41 && (buffer[37..41] == b"npTc"[..] || buffer[37..41] == b"npOl"[..]) {
        let mut nptc_buffer = MyBuffer::new(buffer);
        nptc_buffer = find_9patch_chunk(nptc_buffer);

        nptc_buffer.skip_bytes(1);
        let num_x_divs = nptc_buffer.read_byte();
        let num_y_divs = nptc_buffer.read_byte();
        debug!("x分割数: {}, y分割数: {}", num_x_divs, num_y_divs);
        nptc_buffer.skip_bytes(9);

        nine_patch_data.pad_left = nptc_buffer.read_int();
        nine_patch_data.pad_right = nptc_buffer.read_int();
        nine_patch_data.pad_top = nptc_buffer.read_int();
        nine_patch_data.pad_bottom = nptc_buffer.read_int();

        nptc_buffer.skip_bytes(4);

        let x_ints: Vec<i32> = (0..num_x_divs).map(|_| nptc_buffer.read_int()).collect();
        let y_ints: Vec<i32> = (0..num_y_divs).map(|_| nptc_buffer.read_int()).collect();

        let (x_divs, y_divs) = rayon::join(
            || x_ints.into_iter().collect(),
            || y_ints.into_iter().collect(),
        );

        nine_patch_data.x_divs = x_divs;
        nine_patch_data.y_divs = y_divs;
    }

    debug!("9patch数据解码完成");
    Ok((nine_patch_data, origin_size))
}

fn draw_border_lines(
    canvas: &mut RgbaImage,
    nine_patch_data: &NinePatchData,
    origin_size: &OriginSize,
) {
    debug!("开始绘制边框线");
    let bottom_y = (origin_size.height + 1) as u32;
    for x in (nine_patch_data.pad_left + 1)..(origin_size.width - nine_patch_data.pad_right + 1) {
        canvas.put_pixel(x as u32, bottom_y, Rgba([0, 0, 0, 255]));
    }

    let right_x = (origin_size.width + 1) as u32;
    for y in (nine_patch_data.pad_top + 1)..(origin_size.height - nine_patch_data.pad_bottom + 1) {
        canvas.put_pixel(right_x, y as u32, Rgba([0, 0, 0, 255]));
    }

    for i in (0..nine_patch_data.y_divs.len()).step_by(2) {
        if i + 1 < nine_patch_data.y_divs.len() {
            let start_y = nine_patch_data.y_divs[i] + 1;
            let end_y = nine_patch_data.y_divs[i + 1] + 1;
            for y in start_y..end_y {
                canvas.put_pixel(0, y as u32, Rgba([0, 0, 0, 255]));
            }
        }
    }

    for i in (0..nine_patch_data.x_divs.len()).step_by(2) {
        if i + 1 < nine_patch_data.x_divs.len() {
            let start_x = nine_patch_data.x_divs[i] + 1;
            let end_x = nine_patch_data.x_divs[i + 1] + 1;
            for x in start_x..end_x {
                canvas.put_pixel(x as u32, 0, Rgba([0, 0, 0, 255]));
            }
        }
    }
    debug!("边框线绘制完成");
}

#[tauri::command]
pub fn 反编译点9图(file_path: &str, output_path: &str) -> Result<(), InvokeError> {
    info!("开始反编译点9图: {}", file_path);
    let buffer = read_file_content(file_path).map_err(|e| {
        error!("读取文件失败: {}", e);
        InvokeError::from(e)
    })?;

    if !has_nptc_chunk(&buffer) {
        error!("文件不包含npTc块");
        return Err(InvokeError::from(io::Error::new(
            io::ErrorKind::InvalidData,
            "文件不包含npTc块",
        )));
    }

    let (nine_patch_data, origin_size) = decode_nine_patch_data(file_path).map_err(|e| {
        error!("解码9patch数据失败: {}", e);
        InvokeError::from(e)
    })?;

    let img = image::open(file_path).map_err(|e| {
        error!("打开图片失败: {}", e);
        InvokeError::from(io::Error::new(io::ErrorKind::Other, e.to_string()))
    })?;

    let mut canvas = RgbaImage::new(
        (origin_size.width + 2) as u32,
        (origin_size.height + 2) as u32,
    );
    image::imageops::overlay(&mut canvas, &img, 1, 1);

    draw_border_lines(&mut canvas, &nine_patch_data, &origin_size);

    canvas.save(output_path).map_err(|e| {
        error!("保存图片失败: {}", e);
        InvokeError::from(io::Error::new(io::ErrorKind::Other, e.to_string()))
    })?;

    info!("点9图反编译完成: {}", output_path);
    Ok(())
}
