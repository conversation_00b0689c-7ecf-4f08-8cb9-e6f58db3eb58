use crate::commands::获取缓存目录;
use futures::future::join_all;
use log::{debug, error};
use std::fs;
use std::sync::Arc;
use tokio::task;

/// 清理缓存目录下的所有资源(异步并发版本)
///
/// # 返回值
/// - `Result<(), String>` - 成功返回 Ok(()), 失败返回错误信息
#[tauri::command(rename_all = "snake_case")]
pub async fn 清理缓存(app_handle: tauri::AppHandle) -> Result<(), String> {
    let errors = Arc::new(tokio::sync::Mutex::new(Vec::new()));

    // 获取缓存目录路径
    let cache_dir = 获取缓存目录(app_handle).await?;

    // 读取缓存目录内容
    let entries = match fs::read_dir(&cache_dir) {
        Ok(entries) => entries,
        Err(e) => {
            error!("无法读取缓存目录: {}", e);
            return Err(format!("无法读取缓存目录: {}", e));
        }
    };

    // 收集所有任务
    let mut tasks = Vec::new();

    // 遍历缓存目录项并创建异步任务
    for entry in entries {
        if let Ok(entry) = entry {
            let path = entry.path();
            let errors = Arc::clone(&errors);

            let task = task::spawn(async move {
                if path.is_file() {
                    if let Err(e) = fs::remove_file(&path) {
                        error!("删除文件失败: {:?}, 错误: {}", path, e);
                        let mut errors = errors.lock().await;
                        errors.push(format!("删除文件 {:?} 失败: {}", path, e));
                    } else {
                        debug!("成功删除文件: {:?}", path);
                    }
                } else if path.is_dir() {
                    if let Err(e) = fs::remove_dir_all(&path) {
                        error!("删除目录失败: {:?}, 错误: {}", path, e);
                        let mut errors = errors.lock().await;
                        errors.push(format!("删除目录 {:?} 失败: {}", path, e));
                    } else {
                        debug!("成功删除目录: {:?}", path);
                    }
                }
            });

            tasks.push(task);
        }
    }

    // 等待所有任务完成
    join_all(tasks).await;

    // 检查是否有错误
    let errors = errors.lock().await;
    if errors.is_empty() {
        Ok(())
    } else {
        Err(errors.join("\n"))
    }
}
