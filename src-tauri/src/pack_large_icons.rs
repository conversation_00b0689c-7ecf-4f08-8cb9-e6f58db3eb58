use crate::commands::获取配置文件路径;
use crate::压缩目录;
use image::io::Reader as ImageReader;
use json5;
use log::error;
use serde_json::Value;
use std::fs::{self, File};
use std::io::{<PERSON><PERSON><PERSON>eader, BufWriter, Write};
use std::path::{Path, PathBuf};
use tauri::command; // 添加json5导入

/// 判断文件或目录是否为隐藏项（跨平台支持）
fn is_hidden(path: &Path) -> bool {
    // 检查文件名是否以"."开头（Unix/Linux/macOS通用规则）
    if let Some(name) = path.file_name() {
        if let Some(name_str) = name.to_str() {
            if name_str.starts_with('.') {
                return true;
            }
        }
    }

    // 在Windows上，检查文件属性中的隐藏标志
    #[cfg(windows)]
    {
        use std::os::windows::fs::MetadataExt;
        if let Ok(metadata) = path.metadata() {
            let attributes = metadata.file_attributes();
            // FILE_ATTRIBUTE_HIDDEN = 0x2
            if (attributes & 0x2) != 0 {
                return true;
            }
        }
    }

    false
}

#[command]
pub async fn 打包大图标(
    app_handle: tauri::AppHandle,
    pathfile: String,
    designer: String,
    title: String,
    description: String,
    ui_version: String,
    rounded_corner: String,
    official_icons: String,
) -> Result<String, String> {
    let mut error_logs: Vec<String> = Vec::new();

    // 使用相对路径获取 app_name_config.json5
    let app_name_json_path = std::path::PathBuf::from(
        获取配置文件路径(app_handle.clone(), "app_name_config.json5".to_string()).unwrap(),
    );

    let mut name_map: Value = serde_json::from_str("{}").unwrap_or_default();
    if app_name_json_path.exists() {
        match File::open(&app_name_json_path) {
            Ok(file) => {
                let reader = BufReader::new(file);
                let content = std::io::read_to_string(reader).unwrap_or_default();
                match json5::from_str(&content) {
                    Ok(json_data) => {
                        name_map = json_data;
                    }
                    Err(e) => {
                        let msg = format!("解析 app_name_config.json5 失败: {}", e);
                        error_logs.push(msg);
                    }
                }
            }
            Err(e) => {
                let msg = format!("打开 app_name_config.json5 失败: {}", e);
                error_logs.push(msg);
            }
        }
    } else {
        let msg = format!(
            "未找到 app_name_config.json5: {}",
            app_name_json_path.display()
        );
        error_logs.push(msg);
    }

    // 准备输出目录：pathfile/🚚MTZ
    let mut output_mtz_dir = PathBuf::from(&pathfile);
    output_mtz_dir.push("🚚MTZ");

    // 如果目录已存在，先清空它
    if output_mtz_dir.exists() {
        match fs::remove_dir_all(&output_mtz_dir) {
            Ok(_) => {}
            Err(e) => {
                let error_message = format!("清空 🚚MTZ 目录失败: {}", e);
                error_logs.push(error_message.clone());
                return Err(error_message);
            }
        }
    }

    // 创建新的 🚚MTZ 目录
    if let Err(e) = fs::create_dir_all(&output_mtz_dir) {
        let msg = format!("创建输出目录失败: {}", e);
        error_logs.push(msg.clone());
        return Err(msg);
    }

    // 3. 获取 pathfile 目录下所有子目录（排除掉🚚MTZ自身和隐藏目录）
    let parent_path = PathBuf::from(&pathfile);
    let mut sub_dirs = Vec::new();
    if let Ok(dir_entries) = fs::read_dir(&parent_path) {
        for entry in dir_entries {
            if let Ok(e) = entry {
                let p = e.path();
                if p.is_dir() {
                    // 跳过🚚MTZ
                    if p.file_name().unwrap_or_default() == "🚚MTZ" {
                        continue;
                    }
                    // 跳过隐藏目录
                    if is_hidden(&p) {
                        continue;
                    }
                    sub_dirs.push(p);
                }
            }
        }
    }

    // 在处理子目录之前

    // 4. 并行处理每个子目录
    // 注意需要考虑到线程数量上限，这里使用 tokio::spawn 进行并发处理
    // 为避免过高并发，可以使用信号量限制并发量，这里示例暂不作限制
    let mut handles = Vec::new();
    for sub_dir in sub_dirs {
        let designer_clone = designer.clone();
        let title_clone = title.clone();
        let description_clone = description.clone();
        let ui_version_clone = ui_version.clone();
        let rounded_corner_clone = rounded_corner.clone();
        let official_icons_clone = official_icons.clone();
        let name_map_clone = name_map.clone();
        let output_mtz_dir_clone = output_mtz_dir.clone();
        let app_handle_clone = app_handle.clone();

        let handle = tokio::spawn(async move {
            process_sub_dir(
                &sub_dir,
                &designer_clone,
                &title_clone,
                &description_clone,
                &ui_version_clone,
                &rounded_corner_clone,
                &official_icons_clone,
                &name_map_clone,
                &output_mtz_dir_clone,
                &app_handle_clone,
            )
            .await
        });
        handles.push(handle);
    }

    // 在并行处理开始前

    // 等待所有子目录处理结束，并汇总错误日志
    for handle in handles {
        match handle.await {
            Ok(result) => {
                if let Some(errs) = result {
                    error_logs.extend(errs);
                }
            }
            Err(e) => {
                let msg = format!("子目录处理任务发生异常: {}", e);
                error_logs.push(msg);
            }
        }
    }

    // 在写入日志文件之前
    if !error_logs.is_empty() {
        let mut log_file_path = output_mtz_dir.clone();
        log_file_path.push("😭largeicons_log.txt");
        match File::create(&log_file_path) {
            Ok(log_file) => {
                let mut writer = BufWriter::new(log_file);
                // 用于分块打印，像题中示例
                let mut current_app_name = String::new();
                for line in &error_logs {
                    // 如果是新的应用块开始
                    if line.starts_with("❌") {
                        let splitted: Vec<&str> = line.split_whitespace().collect();
                        if splitted.len() > 0 {
                            let new_app_name = splitted[0].replace("❌", "");
                            if new_app_name != current_app_name {
                                if !current_app_name.is_empty() {
                                    let _ = writer.write_all(
                                        b"=====================================================\n",
                                    );
                                }
                                current_app_name = new_app_name;
                            }
                        }
                    }
                    let _ = writeln!(writer, "{}", line);
                }
                // 末尾再打印一次分割线
                let _ =
                    writer.write_all(b"=====================================================\n");
            }
            Err(e) => {
                return Err(format!("创建 😭largeicons_log.txt 文件失败: {}", e));
            }
        }
    }

    Ok(output_mtz_dir.to_string_lossy().to_string())
}

/// 处理单个子目录的转换逻辑
async fn process_sub_dir(
    sub_dir: &Path,
    designer: &str,
    title: &str,
    description: &str,
    ui_version: &str,
    rounded_corner: &str,
    official_icons: &str,
    name_map: &Value,
    output_mtz_dir: &Path,
    app_handle: &tauri::AppHandle,
) -> Option<Vec<String>> {
    let mut error_logs = Vec::new();
    let app_name_key = match sub_dir.file_name() {
        Some(s) => s.to_string_lossy().to_string(),
        None => {
            error_logs.push("目录名解析失败".to_string());
            return Some(error_logs);
        }
    };

    // 查看在 app_name_config.json5 中是否能找到此子目录对应的值
    let app_name_value = match name_map.get("app_name") {
        Some(app_map) => match app_map.get(&app_name_key) {
            Some(val) => {
                if val.is_string() {
                    val.as_str().unwrap_or("").to_string()
                } else {
                    let msg = format!("❌🚚 {} 不是允许的大图标，请检查名称是否错误", app_name_key);
                    error_logs.push(msg);
                    return Some(error_logs);
                }
            }
            None => {
                let msg = format!("❌🚚 {} 不是允许的大图标，请检查名称是否错误", app_name_key);
                error_logs.push(msg);
                return Some(error_logs);
            }
        },
        None => {
            let msg = format!("❌🚚 app_name_config.json5 格式错误，缺少 app_name_config 字段");
            error_logs.push(msg);
            return Some(error_logs);
        }
    };

    // 目标目录： pathfile/🚚MTZ/微信
    let mut target_dir = output_mtz_dir.to_path_buf();
    target_dir.push(&app_name_key);

    // 创建目标目录
    if let Err(e) = fs::create_dir_all(&target_dir) {
        error_logs.push(format!("❌ 创建 {} 目标目录失败: {}", app_name_key, e));
        return Some(error_logs);
    }

    // 复制并重命名"Picker所需资源" -> "preview"
    // 复制并转换"桌面所需资源" -> "largeicons"
    let picker_source = sub_dir.join("Picker所需资源");
    let desktop_source = sub_dir.join("桌面所需资源");

    let preview_target = target_dir.join("preview");
    let largeicons_target = target_dir.join("largeicons");

    // 在处理 Picker所需资源 之前

    // 先处理 Picker所需资源
    if picker_source.exists() {
        let is_official = official_icons.to_lowercase() == "true";

        // 定义文件及其期望尺寸
        let required_files = if is_official {
            vec![
                ("1x2.png", 186, 440),
                ("2x1.png", 440, 160),
                ("2x2.png", 440, 440),
            ]
        } else {
            vec![
                ("1x2.png", 186, 440),
                ("2x1.png", 440, 160),
                ("2x2.png", 440, 440),
                ("不带外框160.png", 160, 160),
                ("汇总.png", 486, 486),
            ]
        };

        for (file_name, expected_width, expected_height) in required_files {
            let file_path = picker_source.join(file_name);
            if !file_path.exists() {
                let msg = format!("❌{}：缺少 Picker所需资源/{}", app_name_key, file_name);
                error_logs.push(msg);
            } else {
                // 检查尺寸
                match check_image_dimensions(&file_path, expected_width, expected_height) {
                    Ok(is_correct) => {
                        if !is_correct {
                            let msg = format!(
                                "❌{}：Picker所需资源/{} 尺寸错误，应为 {}x{}",
                                app_name_key, file_name, expected_width, expected_height
                            );
                            error_logs.push(msg);
                        }
                    }
                    Err(e) => {
                        let msg = format!(
                            "❌{}：Picker所需资源/{} 检查尺寸失败: {}",
                            app_name_key, file_name, e
                        );
                        error_logs.push(msg);
                    }
                }
            }
        }

        // 检查桌面所需资源的尺寸
        if desktop_source.exists() {
            let desktop_files = vec![
                ("1x2.png", 186, 440),
                ("2x1.png", 440, 160),
                ("2x2.png", 440, 440),
                ("外框122.png", 122, 122),
                ("外框168.png", 168, 168),
                ("外框224.png", 224, 224),
            ];

            for (file_name, expected_width, expected_height) in desktop_files {
                let file_path = desktop_source.join(file_name);
                if file_path.exists() {
                    // 如果是官方图标且是外框相关文件，跳过检查
                    if is_official && file_name.starts_with("外框") {
                        continue;
                    }

                    match check_image_dimensions(&file_path, expected_width, expected_height) {
                        Ok(is_correct) => {
                            if !is_correct {
                                let msg = format!(
                                    "❌{}：桌面所需资源/{} 尺寸错误，应为 {}x{}",
                                    app_name_key, file_name, expected_width, expected_height
                                );
                                error_logs.push(msg);
                            }
                        }
                        Err(e) => {
                            let msg = format!(
                                "❌{}：桌面所需资源/{} 检查尺寸失败: {}",
                                app_name_key, file_name, e
                            );
                            error_logs.push(msg);
                        }
                    }
                }
            }

            // 检查2k目录下的图片尺寸
            let k2_dir = desktop_source.join("2k");
            if k2_dir.exists() {
                let k2_files = vec![
                    ("1x2.png", 248, 587),
                    ("2x1.png", 587, 214),
                    ("2x2.png", 587, 587),
                ];

                for (file_name, expected_width, expected_height) in k2_files {
                    let file_path = k2_dir.join(file_name);
                    if file_path.exists() {
                        match check_image_dimensions(&file_path, expected_width, expected_height) {
                            Ok(is_correct) => {
                                if !is_correct {
                                    let msg = format!(
                                        "❌{}：桌面所需资源/2k/{} 尺寸错误，应为 {}x{}",
                                        app_name_key, file_name, expected_width, expected_height
                                    );
                                    error_logs.push(msg);
                                }
                            }
                            Err(e) => {
                                let msg = format!(
                                    "❌{}：桌面所需资源/2k/{} 检查尺寸失败: {}",
                                    app_name_key, file_name, e
                                );
                                error_logs.push(msg);
                            }
                        }
                    }
                }
            }
        }

        if !preview_target.exists() {
            if let Err(e) = fs::create_dir_all(&preview_target) {
                error_logs.push(format!("❌ 创建 preview 目录失败: {}", e));
            }
        }
        // 遍历 Picker所需资源 下的文件，按照规则进行重命名（跳过隐藏文件）
        // 1x2.png -> preview_largeicons_1x2_0.png
        // 2x1.png -> preview_largeicons_2x1_0.png
        // 2x2.png -> preview_largeicons_2x2_0.png
        // 不带外框160.png -> preview_largeicons_1x1_0.png
        // 汇总.png -> preview_largeicons_cover_0.png
        if let Ok(entries) = fs::read_dir(&picker_source) {
            for entry in entries {
                if let Ok(e) = entry {
                    let file_path = e.path();
                    // 跳过隐藏文件
                    if is_hidden(&file_path) {
                        continue;
                    }
                    if file_path.is_file() {
                        let file_name = file_path.file_name().unwrap_or_default().to_string_lossy();
                        let new_name = match file_name.as_ref() {
                            "1x2.png" => "preview_largeicons_1x2_0.png".to_string(),
                            "2x1.png" => "preview_largeicons_2x1_0.png".to_string(),
                            "2x2.png" => "preview_largeicons_2x2_0.png".to_string(),
                            "不带外框160.png" => "preview_largeicons_1x1_0.png".to_string(),
                            "汇总.png" => "preview_largeicons_cover_0.png".to_string(),
                            _ => {
                                // 如果有其他文件，可以直接原样复制或者记录缺少，这里先记录一下
                                // 题目要求只列出缺少资源，但并没写到如何处理多余文件，此处仅做复制
                                file_name.to_string()
                            }
                        };
                        let dest = preview_target.join(&new_name);
                        if let Err(e) = fs::copy(&file_path, &dest) {
                            error_logs.push(format!("❌ 拷贝文件时出现异常: {}", e));
                        }
                    }
                }
            }
        }
    } else {
        // 缺少 Picker所需资源
        let msg = format!("❌{}：缺少 Picker所需资源", app_name_key);
        error_logs.push(msg);
    }

    // 在处理 桌面所需资源 之前

    // 再处理 桌面所需资源 -> largeicons
    // 需要按照题目所示结构
    if desktop_source.exists() {
        // 创建包名目录
        let package_dir = largeicons_target.join(&app_name_value);
        if let Err(e) = fs::create_dir_all(&package_dir) {
            error_logs.push(format!("❌ 创建包名目录失败: {}", e));
            return Some(error_logs);
        }

        // 先检查文件是否存在，再创建对应目录
        let file_dir_mapping = vec![
            // (文件名, 目标目录)
            (
                "1x2.png",
                vec!["1x2/res/drawable-nxhdpi", "1x2/res/drawable-xxhdpi"],
            ),
            (
                "2x1.png",
                vec!["2x1/res/drawable-nxhdpi", "2x1/res/drawable-xxhdpi"],
            ),
            (
                "2x2.png",
                vec!["2x2/res/drawable-nxhdpi", "2x2/res/drawable-xxhdpi"],
            ),
            ("外框122.png", vec!["1x1/res/drawable-xhdpi"]),
            (
                "外框168.png",
                vec!["1x1/res/drawable-nxhdpi", "1x1/res/drawable-xxhdpi"],
            ),
            ("外框224.png", vec!["1x1/res/drawable-xxxhdpi"]),
        ];

        // 检查文件并创建必要的目录
        let is_official = official_icons.to_lowercase() == "true";

        for (file_name, target_dirs) in file_dir_mapping {
            let source_file = desktop_source.join(file_name);
            if !source_file.exists() {
                // 如果是官方图标，跳过外框相关的错误记录
                if is_official
                    && (file_name.starts_with("外框")
                        || file_name == "不带外框160.png"
                        || file_name == "汇总.png")
                {
                    continue;
                }
                let msg = format!("{}：缺少 {}", app_name_key, file_name);
                error!("{}", msg);
                error_logs.push(msg);
                continue; // 如果源文件不存在，跳过后续处理
            }

            // 对每个目标目录进行处理
            for target_dir in target_dirs {
                let full_target_dir = package_dir.join(target_dir);
                if let Err(e) = fs::create_dir_all(&full_target_dir) {
                    error_logs.push(format!("❌ 创建目录失败: {}", e));
                    continue;
                }

                // 构建目标文件路径 - 使用 app_name_value 作为文件名
                let target_file = full_target_dir.join(format!("{}.png", app_name_value));

                // 复制文件
                if let Err(e) = fs::copy(&source_file, &target_file) {
                    error_logs.push(format!("❌ 复制文件失败 {}: {}", file_name, e));
                }
            }
        }

        // 处理2k目录
        let desktop_2k = desktop_source.join("2k");
        if desktop_2k.exists() {
            let k2_files = vec!["1x2.png", "2x1.png", "2x2.png"];
            for file_name in k2_files {
                let source_file = desktop_2k.join(file_name);
                if source_file.exists() {
                    let target_dir = match file_name {
                        "1x2.png" => package_dir.join("1x2/res/drawable-xxxhdpi"),
                        "2x1.png" => package_dir.join("2x1/res/drawable-xxxhdpi"),
                        "2x2.png" => package_dir.join("2x2/res/drawable-xxxhdpi"),
                        _ => continue,
                    };
                    if let Err(e) = fs::create_dir_all(&target_dir) {
                        error_logs.push(format!("❌ 创建2k目录失败: {}", e));
                        continue;
                    }
                    let target_file = target_dir.join(&format!("{}.png", app_name_value));
                    if let Err(e) = fs::copy(&source_file, &target_file) {
                        error_logs.push(format!("❌ 复制2k文件失败: {}", e));
                    }
                }
            }
        }

        // 生成 transform_config.xml
        let transform_config_path = package_dir.join("transform_config.xml");
        if let Err(e) = write_transform_config(&transform_config_path, rounded_corner) {
            error_logs.push(format!("❌ 写入 transform_config.xml 失败: {}", e));
        }

        // 生成 description.xml
        let description_xml_path = target_dir.join("description.xml");
        if let Err(e) = write_description_xml(
            &description_xml_path,
            title,
            designer,
            ui_version,
            official_icons,
            description,
        ) {
            error_logs.push(format!("❌ 写入 description.xml 失败: {}", e));
        }

        // 执行三层压缩
        // 1. 压缩包名目录 - 保留源文件
        let package_dir = largeicons_target.join(&app_name_value);
        if package_dir.exists() {
            if let Err(e) = 压缩目录(
                app_handle.clone(),
                package_dir.to_string_lossy().to_string(),
                package_dir.to_string_lossy().to_string(),
                Some(true), // 不删除源文件
            )
            .await
            {
                error!("压缩包名目录失败: {}", e);
            }
        }

        // 2. 压缩 largeicons 目录 - 保留源文件
        if largeicons_target.exists() {
            if let Err(e) = 压缩目录(
                app_handle.clone(),
                largeicons_target.to_string_lossy().to_string(),
                largeicons_target.to_string_lossy().to_string(),
                Some(true), // 不删除源文件
            )
            .await
            {
                error!("压缩largeicons目录失败: {}", e);
            }
        }

        // 3. 压缩整个应用目录 - 删除源文件
        if let Err(e) = 压缩目录(
            app_handle.clone(),
            target_dir.to_string_lossy().to_string(),
            format!("{}.mtz", target_dir.to_string_lossy()),
            Some(true), // 删除源文件
        )
        .await
        {
            println!("压缩应用目录失败: {}", e);
        }

        Some(error_logs)
    } else {
        // 缺少 桌面所需资源
        let msg = format!("❌{}：缺少 桌面所需资源", app_name_key);
        error_logs.push(msg);
        Some(error_logs)
    }
}

/// 写入 transform_config.xml
fn write_transform_config(path: &Path, rounded_corner: &str) -> Result<(), std::io::Error> {
    println!("📝 写入 transform_config.xml");
    let content = format!(
        r#"<?xml version="1.0" encoding="UTF-8"?>
<IconTransform>
    <Config name="enableIconMask" value="{rounded_corner}" />
</IconTransform>
"#
    );

    let file = File::create(path)?;
    let mut writer = BufWriter::new(file);
    writer.write_all(content.as_bytes())?;
    Ok(())
}

/// 写入 description.xml
fn write_description_xml(
    path: &Path,
    title: &str,
    designer: &str,
    ui_version: &str,
    official_icons: &str,
    description: &str,
) -> Result<(), std::io::Error> {
    println!("📝 写入 description.xml");
    let content = format!(
        r#"<?xml version="1.0" encoding="UTF-8"?>
<theme>
    <title>{}</title>
    <designer>{}</designer>
    <author>{}</author>
    <uiVersion>{}</uiVersion>
    <officialIcons>{}</officialIcons>
    <description>{}</description>
</theme>
"#,
        title, designer, designer, ui_version, official_icons, description
    );
    let file = File::create(path)?;
    let mut writer = BufWriter::new(file);
    writer.write_all(content.as_bytes())?;
    Ok(())
}

// 添加尺寸检查函数
fn check_image_dimensions(
    path: &Path,
    expected_width: u32,
    expected_height: u32,
) -> Result<bool, String> {
    match ImageReader::open(path) {
        Ok(reader) => match reader.decode() {
            Ok(img) => {
                let (width, height) = (img.width(), img.height());
                Ok(width == expected_width && height == expected_height)
            }
            Err(e) => Err(format!("解析图片失败: {}", e)),
        },
        Err(e) => Err(format!("打开图片失败: {}", e)),
    }
}
