// 声明模块

#[path = "compress_directory.rs"]
pub mod compress_directory;

#[path = "commands.rs"]
mod commands;
#[path = "decompile_9patch.rs"]
pub mod decompile_9patch;
#[path = "pack_large_icons.rs"]
pub mod pack_large_icons;
#[path = "pack_theme.rs"]
mod pack_theme;

#[path = "cache_files.rs"]
mod cache_files;
#[path = "clean_cache.rs"]
mod clean_cache;
#[path = "compile_9patch.rs"]
pub mod compile_9patch;
#[path = "compile_or_decompile_9patch.rs"]
pub mod compile_or_decompile_9patch;
#[path = "copy_to_clipboard.rs"]
mod copy_to_clipboard;
#[path = "font_to_image.rs"]
mod font_to_image;
#[path = "fs_utils.rs"]
mod fs_utils;
#[path = "image_crop.rs"]
mod image_crop;
#[path = "precompile.rs"]
mod precompile;
#[path = "random_color_theme.rs"]
mod random_color_theme;
#[path = "replace_color.rs"]
mod replace_color;
#[path = "search_color.rs"]
mod search_color;
#[path = "unpack_theme.rs"]
mod unpack_theme;
#[path = "version_mapping.rs"]
mod version_mapping;

// 导出函数
use cache_files::{删除历史记录, 删除历史记录和缓存, 缓存文件};
use clean_cache::清理缓存;
use commands::{
    find_tool_path, get_app_path, open_devtools, open_uri, read_file, 创建临时目录, 创建目录,
    删除目录文件夹, 复制文件, 复制目录, 打包文件, 打开目录或文件, 提取压缩文件, 检查9patch文件,
    检查路径是否存在, 清理失效路径, 清空目录, 获取用户名称, 获取缓存目录, 获取配置文件路径,
    获取配置目录, 超大文件检测,
};
use compile_9patch::编译点9图;
use compile_or_decompile_9patch::编译或反编译点9图;
use compress_directory::压缩目录;
use copy_to_clipboard::拷贝到粘贴板;
use decompile_9patch::反编译点9图;
use font_to_image::字体生成图片;
use fs_utils::删除目录;
use image_crop::图片裁切;
use pack_large_icons::打包大图标;
use pack_theme::打包主题;
use precompile::预编译;
use random_color_theme::process_theme;
use replace_color::{get_resource_file_path, get_theme_ui_version, replace_xml_colors};
use search_color::search_color;
use tauri_plugin_log::{Target, TargetKind};
use unpack_theme::解包主题;
use version_mapping::{get_drawable_dir_by_version, get_version_mapping};

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_os::init())
        // 确保全局快捷键插件只在这里初始化一次
        .plugin(tauri_plugin_global_shortcut::Builder::new().build())
        .plugin(tauri_plugin_opener::init())
        .plugin(
            tauri_plugin_log::Builder::new()
                .targets([
                    Target::new(TargetKind::Stdout),  // 输出到标准输出
                    Target::new(TargetKind::Stderr),  // 输出到标准错误
                    Target::new(TargetKind::Webview), // 输出到前端控制台
                ])
                .level(log::LevelFilter::Debug) // 设置日志级别为 Debug
                // .level(log::LevelFilter::Error) // 设置日志级别为 Error
                .build(),
        )
        // 剪贴板管理插件，用于读写系统剪贴板
        .plugin(tauri_plugin_clipboard_manager::init())
        // 数据持久化存储插件，用于保存应用配置和数据
        .plugin(tauri_plugin_store::Builder::new().build())
        // 系统对话框插件，用于打开文件选择、保存等对话框
        .plugin(tauri_plugin_dialog::init())
        // 文件系统操作插件，用于文件读写等操作
        .plugin(tauri_plugin_fs::init())
        // Shell命令执行插件，用于执行系统命令
        .plugin(tauri_plugin_shellx::init(true))
        // 持久化权限作用域插件，用于管理文件系统访问权限
        .plugin(tauri_plugin_persisted_scope::init())
        .invoke_handler(tauri::generate_handler![
            解包主题,
            反编译点9图,
            编译点9图,
            编译或反编译点9图,
            提取压缩文件,
            打包主题,
            检查路径是否存在,
            获取缓存目录,
            get_app_path,
            缓存文件,
            预编译,
            清理缓存,
            拷贝到粘贴板,
            read_file,
            创建目录,
            创建临时目录,
            打开目录或文件,
            open_uri,
            清理失效路径,
            删除历史记录,
            删除历史记录和缓存,
            检查9patch文件,
            压缩目录,
            清空目录,
            复制文件,
            复制目录,
            删除目录文件夹,
            打包大图标,
            获取用户名称,
            超大文件检测,
            字体生成图片,
            图片裁切,
            open_devtools,
            打包文件,
            获取配置文件路径,
            获取配置目录,
            process_theme,
            删除目录,
            find_tool_path,
            search_color,
            replace_xml_colors,
            get_theme_ui_version,
            get_resource_file_path,
            get_version_mapping,
            get_drawable_dir_by_version
        ])
        .run(tauri::generate_context!())
        .expect("运行 Tauri 应用程序时出错");
}
