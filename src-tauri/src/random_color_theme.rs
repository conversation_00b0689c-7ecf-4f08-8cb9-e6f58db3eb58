use image::{DynamicImage, GenericImageView, ImageB<PERSON>er, Rgba};
use json5;
use lazy_static::lazy_static;
use log::{error, info};
use quick_xml::{
    events::{BytesEnd, BytesText, Event},
    Reader, Writer,
};
use rand::{rng, Rng};
use rayon::prelude::*;
use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::error::Error as StdError;
use std::fs;
use std::fs::File;
use std::io::BufReader;
use std::io::Read;
use std::io::Write;
use std::path::Path;
use std::sync::{Arc, Mutex};
use std::time::Instant;
use tauri::command;
use tauri_plugin_shellx::{process::CommandEvent, ShellExt};
use walkdir;

// 定义 Result 类型别名
type Result<T> = std::result::Result<T, Box<dyn StdError + Send + Sync>>;

// 颜色缓存，存储已生成的颜色，避免重复
lazy_static! {
    static ref COLOR_CACHE: Mutex<HashSet<String>> = Mutex::new(HashSet::new());
    static ref TRANSPARENT_COLOR_REGEX: Mutex<Vec<Regex>> = Mutex::new(Vec::new());
    static ref TRANSPARENT_IMG_REGEX: Mutex<Vec<Regex>> = Mutex::new(Vec::new());
    static ref DELETE_COLOR_REGEX: Mutex<Vec<Regex>> = Mutex::new(Vec::new());
    static ref DELETE_IMG_REGEX: Mutex<Vec<Regex>> = Mutex::new(Vec::new());
}

// 配置结构体
#[derive(Debug, Deserialize, Clone, Default)]
struct ThemeConfig {
    delete_img: Option<Vec<String>>,
    transparent_img: Option<Vec<String>>,
    delete_color: Option<Vec<String>>,
    transparent_color: Option<Vec<String>>,
    add_image: Option<HashMap<String, Vec<String>>>,
    // 其他配置项...
}

// 数据输出结构体
#[derive(Debug, Serialize, Deserialize, Clone)]
struct ThemeData {
    name: String,
    target: String,
}

// 图片处理类型枚举
#[derive(Debug, Clone, PartialEq)]
enum ImageType {
    Png,
    Xml,
    Img,
}

/// 清空颜色缓存
fn clear_color_cache() {
    let mut color_cache = COLOR_CACHE.lock().unwrap();
    color_cache.clear();
    info!("颜色缓存已清空");
}

/// 生成不重复的随机颜色
fn generate_unique_color() -> String {
    let mut rng = rng();
    let mut color_cache = COLOR_CACHE.lock().unwrap();

    loop {
        // 生成完全不透明的随机颜色 (格式: #AARRGGBB，AA=FF表示完全不透明)
        let r = rng.random_range(0..=255);
        let g = rng.random_range(0..=255);
        let b = rng.random_range(0..=255);
        let color = format!("#FF{:02X}{:02X}{:02X}", r, g, b);

        // 检查颜色是否已存在于缓存中
        if !color_cache.contains(&color) {
            color_cache.insert(color.clone());
            return color;
        }
    }
}

/// 获取颜色值，去除透明度部分
fn get_color_without_alpha(color: &str) -> String {
    if color.len() >= 9 && color.starts_with('#') {
        color[3..].to_string()
    } else {
        color.to_string()
    }
}

/// 预先生成颜色池
fn generate_color_pool(size: usize) {
    let start = Instant::now();
    info!("开始预生成颜色池");

    // 获取当前缓存大小并清空
    let mut color_cache = COLOR_CACHE.lock().unwrap();
    color_cache.clear();
    drop(color_cache); // 释放锁

    info!("已清空颜色缓存，开始生成新颜色");

    let batch_size = 2000; // 增加批次大小
    let mut generated = 0;

    while generated < size {
        let mut new_colors = HashSet::with_capacity(batch_size);
        let mut rng = rng();

        // 批量生成颜色
        for _ in 0..batch_size {
            if generated >= size {
                break;
            }

            // 生成随机颜色
            let r = rng.random_range(0..=255);
            let g = rng.random_range(0..=255);
            let b = rng.random_range(0..=255);
            let color = format!("#FF{:02X}{:02X}{:02X}", r, g, b);

            // 添加到临时集合
            if new_colors.insert(color) {
                generated += 1;
            }
        }

        // 批量插入
        let mut color_cache = COLOR_CACHE.lock().unwrap();
        for color in new_colors {
            color_cache.insert(color);
        }
        drop(color_cache); // 释放锁
    }

    let final_count = COLOR_CACHE.lock().unwrap().len();
    info!(
        "预生成完成: 共 {} 个不重复颜色，耗时: {:?}",
        final_count,
        start.elapsed()
    );
}

/// 检查路径是否为 .9.png 文件
fn is_9png(path: &str) -> bool {
    path.ends_with(".9.png")
}

/// 创建目录（如果不存在）
fn create_dir_if_not_exists(path: &Path) -> Result<()> {
    if !path.exists() {
        fs::create_dir_all(path)?;
    }
    Ok(())
}

/// 去除文件名中的扩展名
fn remove_extension(file_name: &str) -> String {
    match file_name.rfind('.') {
        Some(pos) => file_name[0..pos].to_string(),
        None => file_name.to_string(),
    }
}

/// 处理哈希表的key，移除$符号并确保后缀为.png
fn process_key(key: &str) -> String {
    // 移除$符号
    let processed_key = key.replace('$', "");

    // 确保后缀为.png
    if !processed_key.ends_with(".png") {
        let base_name = remove_extension(&processed_key);
        return format!("{}.png", base_name);
    }

    processed_key
}

/// 编译正则表达式
fn compile_regex_patterns(patterns: &[String]) -> Vec<Regex> {
    patterns
        .iter()
        .filter_map(|pattern| match Regex::new(pattern) {
            Ok(regex) => Some(regex),
            Err(e) => {
                error!("编译正则表达式失败: {} - {}", pattern, e);
                None
            }
        })
        .collect()
}

/// 检查字符串是否匹配任何正则表达式
fn matches_any_regex(text: &str, regexes: &[Regex]) -> bool {
    regexes.iter().any(|regex| regex.is_match(text))
}

/// 处理单个图片
fn process_image(path: &Path, is_nine_patch: bool) -> Result<(DynamicImage, String)> {
    let img = image::open(path)?;
    let color = generate_unique_color();

    if is_nine_patch {
        // 处理 .9.png 图片（四周1px之外的不透明像素改为随机颜色）
        let (width, height) = img.dimensions();
        let mut new_img = img.to_rgba8();

        if width > 2 && height > 2 {
            for y in 0..height {
                for x in 0..width {
                    // 跳过边缘1px
                    if x > 1 && x < width - 1 && y > 1 && y < height - 1 {
                        let pixel = img.get_pixel(x, y);
                        if pixel[3] > 0 {
                            // 如果像素不是完全透明
                            let r = u8::from_str_radix(&color[3..5], 16).unwrap_or(0);
                            let g = u8::from_str_radix(&color[5..7], 16).unwrap_or(0);
                            let b = u8::from_str_radix(&color[7..9], 16).unwrap_or(0);
                            new_img.put_pixel(x, y, Rgba([r, g, b, 255]));
                        }
                    }
                }
            }
        }

        Ok((DynamicImage::ImageRgba8(new_img), color))
    } else {
        // 处理普通图片（所有不透明像素改为随机颜色）
        let (width, height) = img.dimensions();
        let mut new_img = img.to_rgba8();

        for y in 0..height {
            for x in 0..width {
                let pixel = img.get_pixel(x, y);
                if pixel[3] > 0 {
                    // 如果像素不是完全透明
                    let r = u8::from_str_radix(&color[3..5], 16).unwrap_or(0);
                    let g = u8::from_str_radix(&color[5..7], 16).unwrap_or(0);
                    let b = u8::from_str_radix(&color[7..9], 16).unwrap_or(0);
                    new_img.put_pixel(x, y, Rgba([r, g, b, 255]));
                }
            }
        }

        Ok((DynamicImage::ImageRgba8(new_img), color))
    }
}

/// 生成固定大小的随机颜色图片
fn generate_color_image(color: &str) -> Result<DynamicImage> {
    let width = 100;
    let height = 100;
    let mut img = ImageBuffer::new(width, height);

    let r = u8::from_str_radix(&color[3..5], 16).unwrap_or(0);
    let g = u8::from_str_radix(&color[5..7], 16).unwrap_or(0);
    let b = u8::from_str_radix(&color[7..9], 16).unwrap_or(0);

    for y in 0..height {
        for x in 0..width {
            img.put_pixel(x, y, Rgba([r, g, b, 255]));
        }
    }

    Ok(DynamicImage::ImageRgba8(img))
}

/// 解析配置文件
fn parse_config(config_path: &Path) -> Result<ThemeConfig> {
    let start = Instant::now();
    info!("开始解析配置文件: {:?}", config_path);

    // 读取配置文件内容
    let mut file = File::open(config_path)?;
    let mut content = String::new();
    file.read_to_string(&mut content)?;

    // 使用 json5 解析配置文件
    let config: ThemeConfig = match json5::from_str(&content) {
        Ok(config) => config,
        Err(err) => {
            error!("解析配置文件失败: {}", err);
            // 返回默认配置而不是中断流程
            ThemeConfig::default()
        }
    };

    info!("解析配置文件完成, 耗时: {:?}", start.elapsed());
    Ok(config)
}

/// 处理XML文件中的颜色
fn process_colors(
    xml_path: &Path,
    delete_colors: &HashSet<String>,
    transparent_colors: &HashSet<String>,
) -> Result<HashMap<String, ThemeData>> {
    let start = Instant::now();
    let target_dir_name = xml_path
        .parent()
        .and_then(|p| p.file_name())
        .and_then(|n| n.to_str())
        .unwrap_or("unknown")
        .to_string();

    info!("开始处理颜色: {:?}", xml_path);

    // 获取正则表达式
    let transparent_regexes = TRANSPARENT_COLOR_REGEX.lock().unwrap();
    let delete_regexes = DELETE_COLOR_REGEX.lock().unwrap();

    let file = File::open(xml_path)?;
    let reader = BufReader::new(file);
    let mut reader = Reader::from_reader(reader);
    reader.config_mut().trim_text(true);

    let mut buf = Vec::new();
    let mut writer = Writer::new(Vec::new());
    let mut data_map = HashMap::new();
    let mut in_color_tag = false;

    loop {
        match reader.read_event_into(&mut buf) {
            Ok(Event::Start(ref e)) if e.name().as_ref() == b"color" => {
                // 标记正在处理color标签
                in_color_tag = true;

                // 处理颜色标签开始
                let attrs: Vec<_> = e.attributes().filter_map(|a| a.ok()).collect();
                let name_attr = attrs.iter().find(|a| a.key.as_ref() == b"name");

                if let Some(name_attr) = name_attr {
                    let name = String::from_utf8(name_attr.value.to_vec())?;

                    // 检查是否需要删除该颜色
                    if delete_colors.contains(&name)
                        || name.contains("transparent")
                        || matches_any_regex(&name, &delete_regexes)
                    {
                        // 不输出任何内容，等待遇到结束标签时重置状态
                        continue;
                    }

                    // 检查是否需要将颜色设置为透明
                    if transparent_colors.contains(&name)
                        || name.contains("transparent")
                        || matches_any_regex(&name, &transparent_regexes)
                    {
                        // 写入透明色
                        writer.write_event(Event::Start(e.clone()))?;
                        writer.write_event(Event::Text(BytesText::new("#00000000")))?;
                        writer.write_event(Event::End(BytesEnd::new("color")))?;
                        continue;
                    }

                    // 生成随机颜色并写入数据列表
                    let random_color = generate_unique_color();
                    let color_without_alpha =
                        format!("#{}", get_color_without_alpha(&random_color));
                    data_map.insert(
                        color_without_alpha,
                        ThemeData {
                            name: name.clone(),
                            target: target_dir_name.clone(),
                        },
                    );

                    writer.write_event(Event::Start(e.clone()))?;
                    writer.write_event(Event::Text(BytesText::new(&random_color)))?;
                    writer.write_event(Event::End(BytesEnd::new("color")))?;
                    continue;
                } else {
                    // 处理没有name属性的color标签
                    writer.write_event(Event::Start(e.clone()))?;
                }
            }
            Ok(Event::End(ref e)) if e.name().as_ref() == b"color" => {
                // 重置颜色标签状态
                if in_color_tag {
                    in_color_tag = false;
                    continue; // 跳过结束标签，因为我们已经手动写入了结束标签
                } else {
                    // 如果没有特殊处理，正常写入结束标签
                    writer.write_event(Event::End(e.clone()))?;
                }
            }
            Ok(Event::Text(ref _e)) if in_color_tag => {
                // 跳过color标签内的原始文本内容，因为我们已经替换了它
                continue;
            }
            Ok(Event::Eof) => break,
            Err(e) => {
                error!("解析XML错误: {}", e);
                continue;
            }
            Ok(e) => writer.write_event(e)?,
        }

        buf.clear();
    }

    // 写入修改后的内容
    let output = writer.into_inner();
    let mut output_file = File::create(xml_path)?;
    output_file.write_all(&output)?;

    info!("处理颜色完成, 耗时: {:?}", start.elapsed());
    Ok(data_map)
}

/// 检查XML是否包含常见的 Android drawable 元素
/// 支持类型：vector、shape、selector、path、color、bitmap、nine-patch、layer-list、level-list、transition、inset、clip、scale、animated-vector、animation-list、ripple、adaptive-icon
/// 以及 miuix.theme.symbol.SymbolDrawable 专用 <drawable class="miuix.theme.symbol.SymbolDrawable" ... />
fn is_vector_xml(content: &str) -> bool {
    // patterns 数组包含所有常见 Android drawable 的开始标签
    let patterns = [
        // 向量、形状、选择器、路径、颜色
        "<vector",
        "</vector>",
        "<shape",
        "</shape>",
        "<selector",
        "</selector>",
        "<path",
        "color android:color",
        "color android:color=\"@android:color",
        "<bitmap",
        "<nine-patch",
        "<layer-list",
        "<level-list",
        "<transition",
        "<inset",
        "<clip",
        "<scale",
        "<animated-vector",
        "<animation-list",
        "<ripple",
        "<adaptive-icon",
    ];

    for pattern in patterns {
        if content.contains(pattern) {
            return true;
        }
    }

    // 特殊支持 miuix.theme.symbol.SymbolDrawable
    if content.contains("<drawable")
        && (content.contains("class=\"miuix.theme.symbol.SymbolDrawable\"")
            || content.contains("class='miuix.theme.symbol.SymbolDrawable'"))
    {
        return true;
    }

    false
}

/// 递归处理目录下的所有XML颜色文件
fn process_all_colors(
    dir: &Path,
    delete_colors: &HashSet<String>,
    transparent_colors: &HashSet<String>,
) -> Result<HashMap<String, ThemeData>> {
    let mut data_map = HashMap::new();

    if !dir.exists() {
        return Ok(data_map);
    }

    for entry in fs::read_dir(dir)? {
        let entry = entry?;
        let path = entry.path();

        if path.is_dir() {
            // 递归处理子目录
            let sub_data = process_all_colors(&path, delete_colors, transparent_colors)?;
            data_map.extend(sub_data);
        } else if path.file_name().and_then(|n| n.to_str()) == Some("theme_values.xml") {
            // 处理主题值XML文件
            let color_data = process_colors(&path, delete_colors, transparent_colors)?;
            data_map.extend(color_data);
        }
    }

    Ok(data_map)
}

/// 处理图片并生成数据
fn process_image_entry(
    key: &str,
    image_type: &ImageType,
    output_dir: &Path,
) -> Result<(String, ThemeData)> {
    let path = Path::new(key);
    let file_name = path
        .file_name()
        .and_then(|n| n.to_str())
        .unwrap_or("unknown");
    let target_dir_name = path
        .parent() // drawable-xxhdpi
        .and_then(|p| p.parent()) // res
        .and_then(|p| p.parent()) // 子目录
        .and_then(|p| p.file_name())
        .and_then(|n| n.to_str())
        .unwrap_or("unknown")
        .to_string();

    // 确保输出目录存在
    let output_path = path.strip_prefix(output_dir).unwrap_or(path);
    let output_path = output_dir.join(output_path);
    create_dir_if_not_exists(output_path.parent().unwrap_or(Path::new("")))?;

    let color = match image_type {
        ImageType::Png => {
            // 处理 PNG 图片
            let is_nine_patch = is_9png(key);
            let (processed_img, color) = process_image(path, is_nine_patch)?;
            processed_img.save(&output_path)?;
            color
        }
        ImageType::Xml | ImageType::Img => {
            // 为 XML 或其他图片生成随机颜色图片
            let color = generate_unique_color();
            let img = generate_color_image(&color)?;
            img.save(&output_path)?;
            color
        }
    };

    Ok((
        format!("#{}", get_color_without_alpha(&color)),
        ThemeData {
            name: file_name.to_string(),
            target: target_dir_name,
        },
    ))
}

/// 处理单个子目录
fn process_subdirectory(
    input_dir: &Path,
    output_dir: &Path,
    subdir_name: &str,
    image_entries: &mut HashMap<String, ImageType>,
) -> Result<()> {
    let start = Instant::now();
    info!("开始处理子目录: {}", subdir_name);

    let subdir_path = input_dir.join(subdir_name);
    if !subdir_path.exists() || !subdir_path.is_dir() {
        return Ok(());
    }

    // 创建输出子目录
    let output_subdir = output_dir
        .join(subdir_name)
        .join("res")
        .join("drawable-xxhdpi");
    create_dir_if_not_exists(&output_subdir)?;

    // 处理 res 目录
    let res_dir = subdir_path.join("res");
    if !res_dir.exists() || !res_dir.is_dir() {
        return Ok(());
    }

    // 处理各种 drawable 目录
    let drawable_dirs = [
        "drawable-xxhdpi",
        "drawable-xhdpi",
        "drawable-xxxhdpi",
        "drawable",
    ];

    for drawable_dir in &drawable_dirs {
        let dir_path = res_dir.join(drawable_dir);
        if !dir_path.exists() || !dir_path.is_dir() {
            continue;
        }

        for entry in fs::read_dir(dir_path)? {
            let entry = entry?;
            let path = entry.path();

            if !path.is_file() {
                continue;
            }

            let file_name = path.file_name().and_then(|n| n.to_str()).unwrap_or("");

            if file_name.ends_with(".png") {
                // 复制 PNG 文件到输出目录
                let output_path = output_subdir.join(&file_name);
                if !output_path.exists() {
                    match fs::copy(&path, &output_path) {
                        Ok(_) => {
                            // 记录已复制的 PNG 文件
                            let key = output_path.to_string_lossy().to_string();
                            image_entries.insert(key, ImageType::Png);
                        }
                        Err(e) => {
                            error!("复制文件失败: {:?} - {}", path, e);
                        }
                    }
                }
            } else if file_name.ends_with(".xml") {
                // 检查 XML 文件是否包含向量或图形元素
                let mut content = String::new();
                if let Ok(mut file) = File::open(&path) {
                    if file.read_to_string(&mut content).is_ok() && is_vector_xml(&content) {
                        // 记录需要生成图片的 XML 文件
                        let processed_name = process_key(file_name); // 使用process_key处理文件名
                        let output_file = output_subdir.join(&processed_name);
                        let key = output_file.to_string_lossy().to_string();
                        image_entries.insert(key, ImageType::Xml);
                    }
                }
            } else {
                // 记录其他格式的文件，需要生成图片
                let processed_name = process_key(file_name); // 使用process_key处理文件名
                let output_file = output_subdir.join(&processed_name);
                let key = output_file.to_string_lossy().to_string();
                image_entries.insert(key, ImageType::Img);
            }
        }
    }

    // 处理 values/drawables.xml
    let values_dir = res_dir.join("values");
    let drawables_path = values_dir.join("drawables.xml");

    if drawables_path.exists() && drawables_path.is_file() {
        let file = File::open(drawables_path)?;
        let reader = BufReader::new(file);
        let mut reader = Reader::from_reader(reader);
        reader.config_mut().trim_text(true);

        let mut buf = Vec::new();

        loop {
            match reader.read_event_into(&mut buf) {
                Ok(Event::Start(ref e)) if e.name().as_ref() == b"item" => {
                    let attrs: Vec<_> = e.attributes().filter_map(|a| a.ok()).collect();
                    let name_attr = attrs.iter().find(|a| a.key.as_ref() == b"name");

                    if let Some(name_attr) = name_attr {
                        let name = String::from_utf8(name_attr.value.to_vec())?;

                        // 读取 item 的值
                        if let Ok(Event::Text(text)) = reader.read_event_into(&mut buf) {
                            let value = String::from_utf8(text.to_vec())?;

                            // 检查值是否为资源引用（以 @ 开头）且不是颜色值（不以 # 开头）
                            if value.starts_with('@') && !value.starts_with('#') {
                                // 提取资源名称
                                let resource_name = value.split('/').nth(1).unwrap_or("");

                                // 记录 item.name
                                let processed_name = process_key(&name); // 使用process_key处理文件名
                                let name_output = output_subdir.join(&processed_name);
                                let name_key = name_output.to_string_lossy().to_string();
                                image_entries.insert(name_key, ImageType::Xml);

                                // 记录 resource_name
                                if !resource_name.is_empty() {
                                    let processed_resource = process_key(resource_name); // 使用process_key处理文件名
                                    let resource_output = output_subdir.join(&processed_resource);
                                    let resource_key =
                                        resource_output.to_string_lossy().to_string();
                                    image_entries.insert(resource_key, ImageType::Xml);
                                }
                            }
                        }
                    }
                }
                Ok(Event::Eof) => break,
                Err(e) => {
                    error!("解析drawables.xml错误: {}", e);
                    break;
                }
                _ => (),
            }

            buf.clear();
        }
    }

    info!(
        "处理子目录完成: {}, 耗时: {:?}",
        subdir_name,
        start.elapsed()
    );
    Ok(())
}

/// 并发处理图片
fn parallel_process_images(
    image_entries: &HashMap<String, ImageType>,
    output_dir: &Path,
) -> Result<HashMap<String, ThemeData>> {
    let start = Instant::now();
    info!("开始并行处理图片, 总数: {}", image_entries.len());

    let data_map = Arc::new(Mutex::new(HashMap::new()));
    let errors = Arc::new(Mutex::new(Vec::new()));

    // 使用 rayon 并行处理图片
    image_entries.par_iter().for_each(|(key, image_type)| {
        let result = process_image_entry(key, image_type, output_dir);

        match result {
            Ok((color, data)) => {
                let mut data_map = data_map.lock().unwrap();
                data_map.insert(color, data);
            }
            Err(e) => {
                error!("处理图片失败: {} - {}", key, e);
                let mut errors = errors.lock().unwrap();
                errors.push((key.clone(), e.to_string()));
            }
        }
    });

    let data_map = data_map.lock().unwrap().clone();
    info!(
        "并行处理图片完成, 耗时: {:?}, 成功: {}, 失败: {}",
        start.elapsed(),
        data_map.len(),
        errors.lock().unwrap().len()
    );

    Ok(data_map)
}

/// 处理添加图片配置
fn process_add_image(
    config: &ThemeConfig,
    output_dir: &Path,
    image_entries: &mut HashMap<String, ImageType>,
) -> Result<()> {
    if let Some(add_image) = &config.add_image {
        for (subdir, images) in add_image {
            let drawable_dir = output_dir.join(subdir).join("res").join("drawable-xxhdpi");
            if drawable_dir.exists() && drawable_dir.is_dir() {
                for image in images {
                    let output_path = drawable_dir.join(&image);
                    let key = output_path.to_string_lossy().to_string();
                    image_entries.insert(key, ImageType::Img);
                }
            }
        }
    }

    Ok(())
}

/// 处理图片后处理
fn post_process_images(
    output_dir: &Path,
    delete_img: &HashSet<String>,
    transparent_img: &HashSet<String>,
) -> Result<()> {
    // 获取正则表达式
    let transparent_regexes = TRANSPARENT_IMG_REGEX.lock().unwrap();
    let delete_regexes = DELETE_IMG_REGEX.lock().unwrap();

    // 遍历输出目录中的所有图片
    for entry in walkdir::WalkDir::new(output_dir) {
        let entry = entry?;
        let path = entry.path();

        if !path.is_file() {
            continue;
        }

        let file_name = path.file_name().and_then(|n| n.to_str()).unwrap_or("");

        if file_name.ends_with(".png") {
            // 检查是否需要删除
            if delete_img.contains(file_name) || matches_any_regex(file_name, &delete_regexes) {
                fs::remove_file(path)?;
                continue;
            }

            // 检查是否需要设置为透明
            if transparent_img.contains(file_name)
                || file_name.contains("transparent")
                || matches_any_regex(file_name, &transparent_regexes)
            {
                // 读取图片
                let mut img = image::open(path)?;
                // 将图片设置为透明
                for pixel in img.as_mut_rgba8().unwrap().pixels_mut() {
                    pixel[3] = 0; // 设置 alpha 通道为 0
                }
                // 保存修改后的图片
                img.save(path)?;
            }
        }
    }

    Ok(())
}

/// 检查文件是否包含nptc块
fn has_nptc_chunk(buffer: &[u8]) -> bool {
    if buffer.len() < 4 {
        return false;
    }
    buffer.windows(4).any(|window| window == b"npTc")
}

/// 编译单个点9图文件
async fn compile_nine_patch_file(app_handle: &tauri::AppHandle, file_path: &Path) -> Result<()> {
    let file_path_str = file_path.to_string_lossy().to_string();

    // 读取文件内容检查是否已编译
    let buffer = fs::read(file_path).map_err(|e| {
        error!("读取点9图文件失败: {} - {}", file_path.display(), e);
        e
    })?;

    // 如果已经包含nptc块，跳过编译
    if has_nptc_chunk(&buffer) {
        return Ok(());
    }

    info!("编译点9图: {}", file_path.display());

    // 执行aapt编译
    let sidecar_command = app_handle.shell().sidecar("aapt").map_err(|e| {
        error!("获取aapt命令失败: {}", e);
        e
    })?;

    let (mut rx, _child) = sidecar_command
        .args(&["s", "-i", &file_path_str, "-o", &file_path_str])
        .spawn()
        .map_err(|e| {
            error!("启动aapt命令失败: {}", e);
            e
        })?;

    // 等待命令完成
    let mut success = false;
    while let Some(event) = rx.recv().await {
        match event {
            CommandEvent::Stdout(_) => {
                // aapt编译成功通常没有输出
            }
            CommandEvent::Stderr(line) => {
                let error_output = String::from_utf8_lossy(&line);
                error!("aapt编译错误: {}", error_output);
            }
            CommandEvent::Terminated(payload) => {
                success = payload.code == Some(0);
                break;
            }
            _ => {}
        }
    }

    if !success {
        return Err(format!("aapt编译失败: {}", file_path.display()).into());
    }

    Ok(())
}

/// 并行编译主题包中的所有点9图
async fn compile_nine_patch_files(app_handle: &tauri::AppHandle, theme_dir: &Path) -> Result<()> {
    let start_time = Instant::now();
    info!("开始编译主题包中的点9图文件: {}", theme_dir.display());

    // 收集所有.9.png文件
    let mut nine_patch_files = Vec::new();
    for entry in walkdir::WalkDir::new(theme_dir) {
        let entry = entry?;
        let path = entry.path();

        if path.is_file() {
            if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
                if file_name.ends_with(".9.png") {
                    nine_patch_files.push(path.to_path_buf());
                }
            }
        }
    }

    if nine_patch_files.is_empty() {
        info!("未找到需要编译的点9图文件");
        return Ok(());
    }

    info!("找到 {} 个点9图文件需要编译", nine_patch_files.len());

    // 并行编译点9图文件
    let semaphore = Arc::new(tokio::sync::Semaphore::new(4)); // 限制并发数
    let mut handles = Vec::new();

    for file_path in nine_patch_files {
        let app_handle = app_handle.clone();
        let permit = semaphore.clone().acquire_owned().await.unwrap();

        let handle = tokio::spawn(async move {
            let _permit = permit;
            compile_nine_patch_file(&app_handle, &file_path).await
        });

        handles.push(handle);
    }

    // 等待所有编译任务完成
    let mut success_count = 0;
    let mut error_count = 0;

    for handle in handles {
        match handle.await {
            Ok(Ok(())) => success_count += 1,
            Ok(Err(e)) => {
                error!("点9图编译失败: {}", e);
                error_count += 1;
            }
            Err(e) => {
                error!("点9图编译任务失败: {}", e);
                error_count += 1;
            }
        }
    }

    let duration = start_time.elapsed();
    info!(
        "点9图编译完成: 成功 {} 个, 失败 {} 个, 耗时: {:?}",
        success_count, error_count, duration
    );

    if error_count > 0 {
        error!("部分点9图编译失败，但继续处理其他文件");
    }

    Ok(())
}

/// 创建颜色值版本和图片版本
fn create_versions(output_dir: &Path) -> Result<()> {
    let parent_dir = output_dir.parent().unwrap_or(Path::new(""));

    // 创建图片版本目录
    let image_version_dir = parent_dir.join("图片版本");
    if image_version_dir.exists() {
        fs::remove_dir_all(&image_version_dir)?;
    }
    fs::create_dir_all(&image_version_dir)?;

    // 创建颜色值版本目录
    let color_version_dir = parent_dir.join("颜色值版本");
    if color_version_dir.exists() {
        fs::remove_dir_all(&color_version_dir)?;
    }
    fs::create_dir_all(&color_version_dir)?;

    // 遍历输入目录并复制文件
    for entry in walkdir::WalkDir::new(output_dir) {
        let entry = entry?;
        let path = entry.path();
        let relative_path = path.strip_prefix(output_dir)?;

        // 检查是否在res目录中（完全匹配）
        let is_in_res = relative_path
            .components()
            .any(|comp| comp.as_os_str() == "res");

        // 如果是目录，在两个版本目录中都创建对应的目录
        if path.is_dir() {
            let image_target = image_version_dir.join(relative_path);
            fs::create_dir_all(&image_target)?;

            // 对于颜色值版本，只跳过完全匹配res的目录
            if !is_in_res {
                let color_target = color_version_dir.join(relative_path);
                fs::create_dir_all(&color_target)?;
            }
            continue;
        }

        // 处理文件
        if path.is_file() {
            // 对于图片版本，跳过theme_values.xml文件
            if path
                .file_name()
                .and_then(|n| n.to_str())
                .map_or(false, |name| name != "theme_values.xml")
            {
                let image_target = image_version_dir.join(relative_path);
                if let Some(parent) = image_target.parent() {
                    fs::create_dir_all(parent)?;
                }
                fs::copy(path, image_target)?;
            }

            // 对于颜色值版本，只跳过res目录下的文件（完全匹配）
            if !is_in_res {
                let color_target = color_version_dir.join(relative_path);
                if let Some(parent) = color_target.parent() {
                    fs::create_dir_all(parent)?;
                }
                fs::copy(path, color_target)?;
            }
        }
    }

    Ok(())
}

/// 主处理函数
#[command]
pub async fn process_theme(
    app_handle: tauri::AppHandle,
    input_dir: String,
    output_dir: String,
    config_path: String,
) -> std::result::Result<String, String> {
    // 清空颜色缓存，确保每次执行从干净的状态开始
    clear_color_cache();

    // 1. 解析配置文件
    let config = match parse_config(Path::new(&config_path)) {
        Ok(config) => config,
        Err(e) => {
            error!("解析配置文件失败: {}", e);
            return Err(format!("解析配置文件失败: {}", e));
        }
    };

    // 克隆配置对象，避免部分移动
    let config_clone = config.clone();

    // 2. 创建配置项的哈希集合
    let delete_img = config
        .delete_img
        .clone()
        .unwrap_or_default()
        .into_iter()
        .collect::<HashSet<_>>();
    let transparent_img = config
        .transparent_img
        .clone()
        .unwrap_or_default()
        .into_iter()
        .collect::<HashSet<_>>();
    let delete_color = config
        .delete_color
        .clone()
        .unwrap_or_default()
        .into_iter()
        .collect::<HashSet<_>>();
    let transparent_color = config
        .transparent_color
        .clone()
        .unwrap_or_default()
        .into_iter()
        .collect::<HashSet<_>>();

    // 编译正则表达式
    {
        let mut transparent_color_regex = TRANSPARENT_COLOR_REGEX.lock().unwrap();
        *transparent_color_regex =
            compile_regex_patterns(&config.transparent_color.unwrap_or_default());

        let mut transparent_img_regex = TRANSPARENT_IMG_REGEX.lock().unwrap();
        *transparent_img_regex =
            compile_regex_patterns(&config.transparent_img.unwrap_or_default());

        let mut delete_color_regex = DELETE_COLOR_REGEX.lock().unwrap();
        *delete_color_regex = compile_regex_patterns(&config.delete_color.unwrap_or_default());

        let mut delete_img_regex = DELETE_IMG_REGEX.lock().unwrap();
        *delete_img_regex = compile_regex_patterns(&config.delete_img.unwrap_or_default());
    }

    // 3. 预生成颜色池
    generate_color_pool(100000);

    // 4. 创建输出目录
    let output_path = Path::new(&output_dir);
    if let Err(e) = create_dir_if_not_exists(output_path) {
        error!("创建输出目录失败: {}", e);
        return Err(format!("创建输出目录失败: {}", e));
    }

    // 执行阶段
    let mut image_entries = HashMap::new();
    let mut data_map = HashMap::new();

    // 1. 处理输入目录下的所有子目录
    let input_path = Path::new(&input_dir);

    match fs::read_dir(input_path) {
        Ok(entries) => {
            for entry in entries {
                if let Ok(entry) = entry {
                    if entry.path().is_dir() {
                        let subdir_name = entry.file_name().to_string_lossy().to_string();
                        if let Err(e) = process_subdirectory(
                            input_path,
                            output_path,
                            &subdir_name,
                            &mut image_entries,
                        ) {
                            error!("处理子目录失败: {} - {}", subdir_name, e);
                        }
                    }
                }
            }
        }
        Err(e) => {
            error!("读取输入目录失败: {}", e);
            return Err(format!("读取输入目录失败: {}", e));
        }
    }

    // 2. 处理添加图片配置
    if let Err(e) = process_add_image(&config_clone, output_path, &mut image_entries) {
        error!("处理add_image配置失败: {}", e);
    }

    // 3. 对哈希表中的图片进行去重并并行处理
    let unique_entries: HashMap<String, ImageType> = image_entries.into_iter().collect();

    match parallel_process_images(&unique_entries, output_path) {
        Ok(images_data) => {
            data_map.extend(images_data);
        }
        Err(e) => {
            error!("并行处理图片失败: {}", e);
        }
    }

    // 4. 处理颜色
    match process_all_colors(output_path, &delete_color, &transparent_color) {
        Ok(colors_data) => {
            // 直接使用process_colors生成的颜色数据，不再重新生成颜色
            data_map.extend(colors_data);
        }
        Err(e) => {
            error!("处理颜色失败: {}", e);
        }
    }

    // 后处理阶段
    // 1. 处理图片（删除、透明化等）
    if let Err(e) = post_process_images(output_path, &delete_img, &transparent_img) {
        error!("后处理图片失败: {}", e);
    }

    // 2. 保存数据到JSON文件
    let data_json = serde_json::to_string_pretty(&data_map).unwrap_or_else(|e| {
        error!("序列化数据失败: {}", e);
        "{}".to_string()
    });

    let parent_dir = output_path.parent().unwrap_or(Path::new(""));
    let data_path = parent_dir.join("data.json");

    if let Err(e) = fs::write(&data_path, data_json) {
        error!("保存数据到JSON文件失败: {}", e);
    }

    // 3. 编译主题包中的点9图文件
    if let Err(e) = compile_nine_patch_files(&app_handle, output_path).await {
        error!("编译点9图文件失败: {}", e);
        // 不返回错误，继续处理其他步骤
    }

    // 4. 创建不同版本的输出目录
    if let Err(e) = create_versions(output_path) {
        error!("创建版本目录失败: {}", e);
    }

    let result = format!("处理完成，处理项目数: {}", data_map.len());
    info!("{}", result);

    Ok(result)
}
