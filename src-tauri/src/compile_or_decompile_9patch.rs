use log::error;
use memmap2::Mmap;
use rayon::prelude::*;
use std::fs::File;
use std::io::{self, Read};

fn read_file_content(file_path: &str) -> io::Result<Vec<u8>> {
    let file = File::open(file_path)?;
    if file.metadata()?.len() > 1024 * 1024 {
        let mmap = unsafe { Mmap::map(&file)? };
        Ok(mmap.to_vec())
    } else {
        let mut buffer = Vec::new();
        File::open(file_path)?.read_to_end(&mut buffer)?;
        Ok(buffer)
    }
}

fn has_nptc_chunk(buffer: &[u8]) -> bool {
    const CHUNK_SIZE: usize = 1024 * 1024;
    if buffer.len() < 4 {
        return false;
    }
    if buffer.len() <= CHUNK_SIZE {
        return buffer.windows(4).any(|window| window == b"npTc");
    }
    buffer.par_chunks(CHUNK_SIZE).any(|chunk| {
        if chunk.len() < 4 {
            return false;
        }
        chunk.windows(4).any(|window| window == b"npTc")
    })
}

#[tauri::command]
pub async fn 编译或反编译点9图(
    app_handle: tauri::AppHandle,
    file_path: &str,
    output_path: &str,
) -> Result<(), String> {
    let buffer = read_file_content(file_path).map_err(|e| {
        error!("读取文件失败: {}", e);
        format!("读取文件失败: {}", e)
    })?;

    if has_nptc_chunk(&buffer) {
        super::decompile_9patch::反编译点9图(file_path, output_path).map_err(|e| e.to_string())
    } else {
        super::compile_9patch::编译点9图(app_handle, file_path, output_path).await
    }?;
    Ok(())
}
