use log::error;
use memmap2::Mmap;
use rayon::prelude::*;
use std::fs::File;
use std::io::{self, Read};
use tauri::{Emitter, Manager};
use tauri_plugin_shellx::process::CommandEvent;
use tauri_plugin_shellx::ShellExt;

// 检查文件是否包含.9.png特有的npTc块
fn has_nptc_chunk(buffer: &[u8]) -> bool {
    const CHUNK_SIZE: usize = 1024 * 1024; // 1MB 块大小
    if buffer.len() < 4 {
        return false;
    }
    if buffer.len() <= CHUNK_SIZE {
        return buffer.windows(4).any(|window| window == b"npTc");
    }
    buffer.par_chunks(CHUNK_SIZE).any(|chunk| {
        if chunk.len() < 4 {
            return false;
        }
        chunk.windows(4).any(|window| window == b"npTc")
    })
}

// 通用的文件读取函数
fn read_file_content(file_path: &str) -> io::Result<Vec<u8>> {
    let file = File::open(file_path)?;
    if file.metadata()?.len() > 1024 * 1024 * 20 {
        let mmap = unsafe { Mmap::map(&file)? };
        Ok(mmap.to_vec())
    } else {
        let mut buffer = Vec::new();
        File::open(file_path)?.read_to_end(&mut buffer)?;
        Ok(buffer)
    }
}

#[tauri::command]
pub async fn 编译点9图(
    app_handle: tauri::AppHandle,
    file_path: &str,
    output_path: &str,
) -> Result<(), String> {
    // 读取并验证文件
    let buffer = read_file_content(file_path).map_err(|e| {
        error!("读取文件失败: {}", e);
        format!("读取文件失败: {}", e)
    })?;

    if has_nptc_chunk(&buffer) {
        return Ok(());
    }

    // 获取主窗口用于事件发送
    let window = app_handle.get_webview_window("main").unwrap();

    // 执行aapt命令
    let sidecar_command = app_handle.shell().sidecar("aapt").unwrap();
    let (mut rx, _child) = sidecar_command
        .args(&["s", "-i", file_path, "-o", output_path])
        .spawn()
        .map_err(|e| {
            error!("启动aapt命令失败: {}", e);
            format!("启动aapt命令失败: {}", e)
        })?;

    // 在新线程中处理命令输出
    tauri::async_runtime::spawn(async move {
        while let Some(event) = rx.recv().await {
            match event {
                CommandEvent::Stdout(line) => {
                    let output = String::from_utf8_lossy(&line);
                    // 发送输出到前端
                    window
                        .emit("aapt-output", output.to_string())
                        .unwrap_or_else(|e| error!("发送事件失败: {}", e));
                }
                CommandEvent::Stderr(line) => {
                    let error_output = String::from_utf8_lossy(&line);
                    error!("aapt错误: {}", error_output);
                    // 发送错误到前端
                    window
                        .emit("aapt-error", error_output.to_string())
                        .unwrap_or_else(|e| error!("发送事件失败: {}", e));
                }
                _ => {}
            }
        }
    });

    Ok(())
}
