use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use tauri::{command, Manager};

#[derive(Debug, Serialize, Deserialize)]
pub struct VersionMapping {
    pub version_mapping: HashMap<String, String>,
    pub default_version: String,
    pub version_descriptions: HashMap<String, String>,
}

impl VersionMapping {
    /// 从配置文件加载版本映射
    pub fn load_from_config(config_path: &Path) -> Result<Self> {
        let content = fs::read_to_string(config_path)
            .with_context(|| format!("无法读取版本映射配置文件: {}", config_path.display()))?;

        let mapping: VersionMapping =
            json5::from_str(&content).context("解析版本映射配置文件失败")?;

        Ok(mapping)
    }

    /// 根据 uiVersion 获取对应的 drawable 目录名
    pub fn get_drawable_dir(&self, ui_version: u32) -> String {
        let version_key = ui_version.to_string();

        if let Some(dir) = self.version_mapping.get(&version_key) {
            log::info!("找到版本映射: uiVersion={} -> drawable/{}", ui_version, dir);
            dir.clone()
        } else {
            log::warn!(
                "未找到 uiVersion={} 的映射，使用默认版本: {}",
                ui_version,
                self.default_version
            );
            self.default_version.clone()
        }
    }

    /// 获取版本描述
    #[allow(dead_code)]
    pub fn get_version_description(&self, ui_version: u32) -> String {
        let version_key = ui_version.to_string();
        self.version_descriptions
            .get(&version_key)
            .cloned()
            .unwrap_or_else(|| format!("未知版本 {}", ui_version))
    }

    /// 获取完整的 drawable 相对路径
    pub fn get_drawable_path(&self, ui_version: u32) -> String {
        let dir = self.get_drawable_dir(ui_version);
        format!("drawable/{}", dir)
    }
}

/// 获取版本映射配置
#[command]
pub async fn get_version_mapping(app_handle: tauri::AppHandle) -> Result<VersionMapping, String> {
    tokio::task::spawn_blocking(move || {
        load_version_mapping(&app_handle).map_err(|e| format!("加载版本映射失败: {}", e))
    })
    .await
    .map_err(|e| format!("任务执行失败: {}", e))?
}

/// 根据 uiVersion 获取 drawable 目录名
#[command]
pub async fn get_drawable_dir_by_version(
    app_handle: tauri::AppHandle,
    ui_version: u32,
) -> Result<String, String> {
    tokio::task::spawn_blocking(move || {
        let mapping =
            load_version_mapping(&app_handle).map_err(|e| format!("加载版本映射失败: {}", e))?;

        Ok(mapping.get_drawable_dir(ui_version))
    })
    .await
    .map_err(|e| format!("任务执行失败: {}", e))?
}

fn load_version_mapping(app_handle: &tauri::AppHandle) -> Result<VersionMapping> {
    // 尝试从资源目录加载
    if let Ok(resource_path) = app_handle.path().resolve(
        "config/version_mapping.json5",
        tauri::path::BaseDirectory::Resource,
    ) {
        if resource_path.exists() {
            return VersionMapping::load_from_config(&resource_path);
        }
    }

    // 如果资源目录没有，尝试从开发目录加载
    let dev_path = Path::new("src-tauri/config/version_mapping.json5");
    if dev_path.exists() {
        return VersionMapping::load_from_config(dev_path);
    }

    // 如果都没有，返回默认配置
    log::warn!("未找到版本映射配置文件，使用默认配置");
    Ok(create_default_mapping())
}

fn create_default_mapping() -> VersionMapping {
    let mut version_mapping = HashMap::new();
    version_mapping.insert("10".to_string(), "v12".to_string());
    version_mapping.insert("15".to_string(), "v15".to_string());
    version_mapping.insert("16".to_string(), "v16".to_string());
    version_mapping.insert("18".to_string(), "v18".to_string());

    let mut version_descriptions = HashMap::new();
    version_descriptions.insert("10".to_string(), "MIUI 12".to_string());
    version_descriptions.insert("15".to_string(), "MIUI 15".to_string());
    version_descriptions.insert("16".to_string(), "MIUI 16".to_string());
    version_descriptions.insert("18".to_string(), "MIUI 18".to_string());

    VersionMapping {
        version_mapping,
        default_version: "v15".to_string(),
        version_descriptions,
    }
}
