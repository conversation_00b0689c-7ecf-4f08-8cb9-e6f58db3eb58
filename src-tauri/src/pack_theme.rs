use crate::编译点9图;
use log::{error, warn};
use std::collections::{HashMap, HashSet};
use std::io::{BufWriter, Seek, Write};
// use std::panic::PanicInfo;
use std::path::{Path, PathBuf};
use std::sync::Arc;

use tokio::fs::{self};
use tokio::sync::Mutex;
use tokio::task;
use walkdir::WalkDir;
use zip::{write::FileOptions, CompressionMethod, ZipWriter};

// 获取CPU核心数
lazy_static::lazy_static! {
    static ref CPU_CORES: usize = num_cpus::get() * 2;
}
// 创建文件权限的 FileOptions
fn create_file_options(compression: CompressionMethod) -> FileOptions {
    FileOptions::default()
        .compression_method(compression)
        .unix_permissions(0o644) // -rw-r--r-- 只读权限
}

// 创建目录权限的 FileOptions
fn create_directory_options(compression: CompressionMethod) -> FileOptions {
    FileOptions::default()
        .compression_method(compression)
        .unix_permissions(0o40755) // 040755 = d rwxr-xr-x，其中 040000 是目录标志位
}

// 并行处理所有.9.png文件
async fn process_all_nine_patches(
    app_handle: &tauri::AppHandle,
    input_path: &Path,
) -> Result<HashMap<PathBuf, Vec<u8>>, String> {
    let nine_patches = Arc::new(Mutex::new(HashMap::new()));
    let semaphore = Arc::new(tokio::sync::Semaphore::new(*CPU_CORES));
    let mut handles = Vec::new();

    // 遍历所有文件找出.9.png
    for entry in WalkDir::new(input_path)
        .into_iter()
        .filter_map(Result::ok)
        .filter(|e| e.file_type().is_file())
        .filter(|e| e.path().to_string_lossy().contains(".9.png"))
    {
        let path = entry.path().to_path_buf();
        let app_handle = app_handle.clone();
        let permit = semaphore.clone().acquire_owned();
        let nine_patches = Arc::clone(&nine_patches);
        let file_path_str = path.to_string_lossy().to_string();

        let handle = task::spawn(async move {
            let _permit = permit.await.unwrap();

            // 直接在任务中处理.9.png文件
            if let Ok(()) = 编译点9图(app_handle.clone(), &file_path_str, &file_path_str).await
            {
                if let Ok(content) = fs::read(&path).await {
                    let mut patches = nine_patches.lock().await;
                    patches.insert(path, content);
                }
            }
        });
        handles.push(handle);
    }

    // 等待所有任务完成
    futures::future::join_all(handles).await;

    let result = Arc::try_unwrap(nine_patches).unwrap().into_inner();

    Ok(result)
}

// 写入文件到zip的通用函数
fn write_to_zip<W: Write + Seek>(
    zip_writer: &mut ZipWriter<W>,
    path: &Path,
    content: &[u8],
    options: FileOptions,
) -> Result<(), String> {
    // 将路径转换为统一格式（使用正斜杠）
    let normalized_path = path
        .components()
        .map(|c| c.as_os_str().to_string_lossy().into_owned())
        .collect::<Vec<_>>()
        .join("/");

    zip_writer
        .start_file(&normalized_path, options)
        .map_err(|e| {
            warn!("写入文件 {} 失败: {}", path.display(), e);
            e.to_string()
        })?;

    zip_writer.write_all(content).map_err(|e| {
        error!("写入内容失败: {}", e);
        e.to_string()
    })?;

    Ok(())
}

// 处理bootanimation目录
async fn process_bootanimation_directory(
    dir_path: &Path,
    entries: &[walkdir::DirEntry],
) -> Result<Vec<u8>, String> {
    let mut temp_buffer = Vec::new();
    {
        let cursor = std::io::Cursor::new(&mut temp_buffer);
        let mut temp_zip = ZipWriter::new(cursor);
        let options = FileOptions::default().compression_method(CompressionMethod::Stored);

        for entry in entries {
            let entry_path = entry.path();
            let relative_path = entry_path
                .strip_prefix(dir_path)
                .map_err(|e| e.to_string())?;

            if let Ok(content) = fs::read(entry_path).await {
                write_to_zip(&mut temp_zip, relative_path, &content, options)?;
            }
        }

        temp_zip.finish().map_err(|e| e.to_string())?;
    }
    Ok(temp_buffer)
}

// 处理需要压缩的目录 - 使用流式处理
async fn process_compressed_directory<W: Write + Seek + Send>(
    zip_writer: &mut ZipWriter<W>,
    dir_path: PathBuf,
    entries: &[walkdir::DirEntry],
    _options: FileOptions,
    nine_patches: &HashMap<PathBuf, Vec<u8>>,
) -> Result<(), String> {
    let dir_name = dir_path.file_name().unwrap().to_string_lossy().to_string();

    // 使用 Stored 方法
    zip_writer
        .start_file(
            &dir_name,
            create_directory_options(CompressionMethod::Stored),
        )
        .map_err(|e| {
            println!("警告: 创建目录条目 {} 失败: {}", dir_name, e);
            e.to_string()
        })?;

    let mut temp_buffer = Vec::new();
    {
        let cursor = std::io::Cursor::new(&mut temp_buffer);
        let mut temp_zip = ZipWriter::new(cursor);

        for entry in entries {
            let entry_path = entry.path().to_path_buf();
            let relative_path = entry_path
                .strip_prefix(&dir_path)
                .map_err(|e| e.to_string())?
                .to_path_buf();

            // 使用 Stored 方法
            let file_options = create_file_options(CompressionMethod::Stored);

            let content = if let Some(nine_patch_content) = nine_patches.get(&entry_path) {
                nine_patch_content.clone()
            } else {
                match fs::read(&entry_path).await {
                    Ok(content) => content,
                    Err(e) => {
                        warn!("警告: 读取文件 {} 失败: {}", entry_path.display(), e);
                        continue;
                    }
                }
            };

            write_to_zip(&mut temp_zip, &relative_path, &content, file_options)?;
        }

        temp_zip.finish().map_err(|e| {
            warn!("警告: 目录 {} 压缩失败: {}", dir_path.display(), e);
            e.to_string()
        })?;
    }

    zip_writer.write_all(&temp_buffer).map_err(|e| {
        warn!("警告: 写入压缩数据失败: {}", e);
        e.to_string()
    })?;

    Ok(())
}

// 处理不需要压缩的目录
async fn process_uncompressed_directory<W: Write + Seek + Send>(
    zip_writer: &mut ZipWriter<W>,
    dir_path: PathBuf,
    base_path: PathBuf,
    entries: &[walkdir::DirEntry],
    _options: FileOptions,
    nine_patches: &HashMap<PathBuf, Vec<u8>>,
) -> Result<(), String> {
    let compression = CompressionMethod::Stored;

    // 检查是否是boots目录且包含bootanimation子目录
    if dir_path.file_name().unwrap_or_default() == "boots" {
        let bootanimation_path = dir_path.join("bootanimation");
        if bootanimation_path.is_dir() {
            let bootanimation_entries: Vec<_> = WalkDir::new(&bootanimation_path)
                .min_depth(1)
                .into_iter()
                .filter_map(Result::ok)
                .filter(|e| e.file_type().is_file())
                .collect();

            if let Ok(bootanimation_content) =
                process_bootanimation_directory(&bootanimation_path, &bootanimation_entries).await
            {
                // 使用跨平台的路径连接
                let relative_path = PathBuf::from("boots").join("bootanimation.zip");
                write_to_zip(
                    zip_writer,
                    &relative_path,
                    &bootanimation_content,
                    create_file_options(compression),
                )?;
                return Ok(());
            }
        }
    }

    // 获取相对路径并规范化
    let relative_dir_path = dir_path
        .strip_prefix(&base_path)
        .map_err(|e| e.to_string())?
        .to_path_buf();

    if !relative_dir_path.as_os_str().is_empty() {
        // 将路径转换为统一格式
        let normalized_path = relative_dir_path
            .components()
            .map(|c| c.as_os_str().to_string_lossy().into_owned())
            .collect::<Vec<_>>()
            .join("/");

        zip_writer
            .add_directory(normalized_path, create_directory_options(compression))
            .map_err(|e| format!("创建目录条目失败: {}", e))?;
    }

    for entry in entries {
        let entry_path = entry.path().to_path_buf();
        let relative_path = entry_path
            .strip_prefix(&base_path)
            .map_err(|e| e.to_string())?
            .to_path_buf();

        // 如果是目录，创建目录条目
        if entry.file_type().is_dir() {
            zip_writer
                .add_directory(
                    relative_path.to_string_lossy().to_string(),
                    create_directory_options(compression),
                )
                .map_err(|e| format!("创建目录条目失败: {}", e))?;
            continue;
        }

        // 处理文件
        let content = if let Some(nine_patch_content) = nine_patches.get(&entry_path) {
            nine_patch_content.clone()
        } else {
            match fs::read(&entry_path).await {
                Ok(content) => content,
                Err(e) => {
                    warn!("警告: 读取文件 {} 失败: {}", entry_path.display(), e);
                    continue;
                }
            }
        };

        write_to_zip(
            zip_writer,
            &relative_path,
            &content,
            create_file_options(compression),
        )?;
    }

    Ok(())
}

/// 打包主题函数
#[tauri::command]
pub async fn 打包主题(
    app_handle: tauri::AppHandle,
    input_path: String,
    include_files: Option<Vec<String>>,
    output_path: Option<String>,
    original_theme_name: Option<String>, // 添加原始主题名参数
) -> Result<String, String> {
    // 不需要变成压缩包的目录
    let ignore_dirs: HashSet<&str> = [
        "wallpaper",
        "preview",
        "boots",
        "etc",
        "content",
        "ringtones",
        "fonts",
        "rights",
    ]
    .iter()
    .cloned()
    .collect();

    let input_path = Path::new(&input_path);

    // 优先使用原始主题名（如果提供），否则使用缓存目录名
    let theme_name = if let Some(original_name) = original_theme_name {
        original_name
    } else {
        input_path
            .file_name()
            .ok_or("无法获取主题名称")?
            .to_string_lossy()
            .to_string()
    };

    let output_file_path = match output_path {
        Some(path) => Path::new(&path).join(format!("{}.mtz", theme_name)),
        None => input_path
            .parent()
            .unwrap()
            .join(format!("{}.mtz", theme_name)),
    };

    // 检查并删除已存在的mtz文件
    if output_file_path.exists() {
        if let Err(e) = fs::remove_file(&output_file_path).await {
            warn!("删除已存在的mtz文件失败: {}", e);
            // 继续执行，因为可能是权限问题，创建新文件时可能会自动覆盖
        }
    }

    if let Some(parent) = output_file_path.parent() {
        if !parent.exists() {
            std::fs::create_dir_all(parent).map_err(|e| format!("无法创建输出目录: {}", e))?;
        }
    }

    // 先处理所有.9.png文件
    let nine_patches = process_all_nine_patches(&app_handle, input_path).await?;

    let file =
        std::fs::File::create(&output_file_path).map_err(|e| format!("无法创建文件: {}", e))?;

    let file = BufWriter::new(file);
    let zip_writer = Arc::new(Mutex::new(ZipWriter::new(file)));
    let options = FileOptions::default().compression_method(CompressionMethod::Stored);

    let mut root_files = Vec::new();
    let mut dirs_to_process = Vec::new();
    let mut ignored_dirs = Vec::new();

    let mut dir_entries = fs::read_dir(input_path).await.map_err(|e| e.to_string())?;

    while let Ok(Some(entry)) = dir_entries.next_entry().await {
        let name = entry.file_name().to_string_lossy().to_string();

        if let Some(ref include_list) = include_files {
            if !include_list.iter().any(|included| name.contains(included)) {
                continue;
            }
        }

        match entry.metadata().await {
            Ok(metadata) => {
                if metadata.is_dir() {
                    let entries: Vec<_> = WalkDir::new(entry.path())
                        .min_depth(1)
                        .into_iter()
                        .filter_map(Result::ok)
                        .filter(|e| e.file_type().is_file())
                        .collect();

                    if ignore_dirs.contains(name.as_str()) {
                        ignored_dirs.push((entry.path(), entries));
                    } else {
                        dirs_to_process.push((entry.path(), entries));
                    }
                } else {
                    root_files.push(entry.path());
                }
            }
            Err(e) => {
                warn!("警告: 无法获取 {} 的元数据: {}", name, e);
                continue;
            }
        }
    }

    {
        let mut zip_writer = zip_writer.lock().await;
        let file_options = create_file_options(CompressionMethod::Stored);
        for path in root_files {
            let content = if let Some(nine_patch_content) = nine_patches.get(&path) {
                nine_patch_content.clone()
            } else {
                match fs::read(&path).await {
                    Ok(content) => content,
                    Err(e) => {
                        warn!("读取文件 {} 失败: {}", path.display(), e);
                        continue;
                    }
                }
            };

            let file_name = path.file_name().unwrap().to_string_lossy().to_string();
            write_to_zip(
                &mut zip_writer,
                Path::new(&file_name),
                &content,
                file_options,
            )?;
        }
    }

    {
        let mut zip_writer = zip_writer.lock().await;
        for (dir_path, entries) in dirs_to_process {
            let dir_name = dir_path.file_name().unwrap().to_string_lossy().to_string();

            if let Err(e) = process_compressed_directory(
                &mut zip_writer,
                dir_path,
                &entries,
                options,
                &nine_patches,
            )
            .await
            {
                warn!("处理目录 {} 失败: {}", dir_name, e);
            }
        }
    }

    {
        let mut zip_writer = zip_writer.lock().await;
        for (dir_path, entries) in ignored_dirs {
            if let Err(e) = process_uncompressed_directory(
                &mut zip_writer,
                dir_path.clone(),
                input_path.to_path_buf(),
                &entries,
                options,
                &nine_patches,
            )
            .await
            {
                warn!("处理目录 {} 失败: {}", dir_path.display(), e);
                continue;
            }
        }

        zip_writer
            .finish()
            .map_err(|e| format!("完成压缩失败: {}", e))?;
    }

    Ok(output_file_path.to_string_lossy().to_string())
}
