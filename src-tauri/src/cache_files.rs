use crate::commands::获取缓存目录;
use crate::version_mapping::VersionMapping;
use base64::{engine::general_purpose, Engine as _};
use futures::future::join_all;
use futures::StreamExt;
use fxhash::FxHashMap;
use log::{error, info};
use rayon::prelude::*;
use rayon::ThreadPoolBuilder;
use serde::{Deserialize, Serialize};
use serde_json;
use std::collections::HashSet;
use std::fs;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::{Instant, SystemTime, UNIX_EPOCH};
use tauri::AppHandle;
use tauri::Emitter;
use tauri::Event;
use tauri::Listener;
use tauri::Manager;
use tauri::Runtime;
use tokio::fs as async_fs;
use tokio::io::{AsyncWriteExt, BufReader, BufWriter};
use tokio::sync::Semaphore;
use tokio::task;
use uuid;
use walkdir::WalkDir;

/// 规范化Windows路径，移除长路径前缀
fn normalize_windows_path(path: PathBuf) -> PathBuf {
    #[cfg(target_os = "windows")]
    {
        if let Some(path_str) = path.to_str() {
            if path_str.starts_with("\\\\?\\") {
                return PathBuf::from(&path_str[4..]);
            }
        }
    }
    path
}

/// 安全检查路径是否存在，适用于跨平台
fn path_exists_safe(path: &Path) -> bool {
    match std::fs::metadata(path) {
        Ok(_) => true,
        Err(_) => false,
    }
}

/// 安全检查路径是否为目录，适用于跨平台
fn is_dir_safe(path: &Path) -> bool {
    match std::fs::metadata(path) {
        Ok(metadata) => metadata.is_dir(),
        Err(_) => false,
    }
}

/// 并发复制数
const MAX_CONCURRENT_COPIES: usize = 128;

/// 需要忽略的文件和目录
const IGNORED_PATTERNS: &[&str] = &[
    ".DS_Store",
    ".vscode",
    ".history",
    ".git",
    "node_modules",
    ".thumbs.db",
    "Thumbs.db",
    "__MACOSX",
    "raw",
    "temp",
    "超大文件列表.txt",
    "colors",
];

/// 生成基于完整路径的唯一缓存目录名
/// 直接使用完整路径的编码作为缓存目录名，确保不同路径但同名的目录不会冲突
pub fn generate_unique_cache_dir_name(source_path: &str, _dir_name: &str) -> String {
    // 将路径中的非法字符替换为下划线
    let normalized_path = source_path
        .replace("/", "_")
        .replace("\\", "_")
        .replace(":", "_");

    // 对完整路径进行 base64 编码
    let encoded_path = general_purpose::URL_SAFE.encode(normalized_path.as_bytes());

    // 移除 base64 编码中的填充字符 '='
    let clean_encoded = encoded_path.replace('=', "");

    // 直接返回编码后的字符串作为缓存目录名
    // 这样可以确保不同路径的目录一定会有不同的缓存目录名
    clean_encoded
}

/// 检查路径是否应该被忽略
fn should_ignore(path: &Path) -> bool {
    let file_name = match path.file_name().and_then(|n| n.to_str()) {
        Some(name) => name,
        None => return false,
    };

    IGNORED_PATTERNS.iter().any(|&pattern| file_name == pattern)
}

/// 内存态文件信息 (用于从 metadata 获取)
#[derive(Debug, Clone, Serialize, Deserialize)]
struct FileInfoRecord {
    modified: u64, // UNIX时间戳，单位秒
    size: u64,
}

impl FileInfoRecord {
    fn new(modified: u64, size: u64) -> Self {
        FileInfoRecord { modified, size }
    }
}

/// 内存态文件信息 (用于从 metadata 获取)
#[derive(Debug, Clone)]
struct FileInfo {
    modified: SystemTime,
    size: u64,
}

impl FileInfo {
    fn new(modified: SystemTime, size: u64) -> Self {
        FileInfo { modified, size }
    }

    /// 转为可序列化的 FileInfoRecord
    fn to_record(&self) -> FileInfoRecord {
        let modified = match self.modified.duration_since(UNIX_EPOCH) {
            Ok(dur) => dur.as_secs(),
            Err(_) => 0,
        };
        FileInfoRecord::new(modified, self.size)
    }
}

/// 使用 std::fs::metadata 获取文件信息
fn get_file_info(path: &Path) -> std::io::Result<FileInfo> {
    let metadata = path.metadata()?;
    Ok(FileInfo::new(metadata.modified()?, metadata.len()))
}

/// 并行收集 (相对路径 -> FileInfo)
fn collect_file_info(base_dir: &Path) -> FxHashMap<String, FileInfo> {
    // 并行遍历目录
    let entries: Vec<_> = WalkDir::new(base_dir)
        .into_iter()
        .par_bridge()
        .filter_map(|e| e.ok())
        .filter(|e| {
            e.file_type().is_file()
                && !should_ignore(e.path())
                && !e.path().ancestors().any(|ancestor| should_ignore(ancestor))
        })
        .collect();

    // 获取文件信息
    let map = entries
        .par_iter()
        .filter_map(|entry| {
            let path = entry.path();
            let rel_path = match path.strip_prefix(base_dir) {
                Ok(p) => p.to_string_lossy().to_string(),
                Err(_) => return None,
            };
            match get_file_info(path) {
                Ok(info) => Some((rel_path, info)),
                Err(_) => None,
            }
        })
        .collect::<FxHashMap<_, _>>();

    map
}

/// 读取 <dir_name>_record.json (若失败则返回 None)
async fn read_dir_record(file_path: &Path) -> Option<FxHashMap<String, FileInfoRecord>> {
    if !file_path.exists() {
        return None;
    }
    match async_fs::read(file_path).await {
        Ok(content) => match bincode::deserialize(&content) {
            Ok(map) => Some(map),
            Err(_) => None,
        },
        Err(_) => None,
    }
}

/// 写入 <dir_name>_record.json
async fn write_dir_record(file_path: &Path, record_map: &FxHashMap<String, FileInfoRecord>) {
    match bincode::serialize(record_map) {
        Ok(bytes) => {
            let _ = async_fs::write(file_path, bytes).await;
        }
        Err(_) => {}
    }
}

/// 历史记录项结构
#[derive(Debug, Clone, Serialize, Deserialize)]
struct HistoryItem {
    path: String,
    timestamp: u64,
    /// 唯一的缓存目录名，基于完整路径生成
    #[serde(default)]
    cache_dir_name: String,
}

/// 读取 source_record.json，返回已缓存的源目录集合
async fn read_source_record(record_path: &Path) -> Vec<HistoryItem> {
    if !record_path.exists() {
        return Vec::new();
    }
    match async_fs::read_to_string(record_path).await {
        Ok(content) => match serde_json::from_str::<serde_json::Value>(&content) {
            Ok(json_val) => {
                let mut items = Vec::new();
                if let Some(arr) = json_val.get("items").and_then(|v| v.as_array()) {
                    for item in arr {
                        if let (Some(path), Some(timestamp)) = (
                            item.get("path").and_then(|v| v.as_str()),
                            item.get("timestamp").and_then(|v| v.as_u64()),
                        ) {
                            // 获取缓存目录名，如果没有则根据路径生成
                            let cache_dir_name = item
                                .get("cache_dir_name")
                                .and_then(|v| v.as_str())
                                .map(|s| s.to_string())
                                .unwrap_or_else(|| {
                                    // 对于旧记录，从路径中提取目录名并生成唯一缓存目录名
                                    let dir_name = Path::new(path)
                                        .file_name()
                                        .and_then(|n| n.to_str())
                                        .unwrap_or("unknown");
                                    generate_unique_cache_dir_name(path, dir_name)
                                });

                            items.push(HistoryItem {
                                path: path.to_string(),
                                timestamp,
                                cache_dir_name,
                            });
                        }
                    }
                }
                // 按时间戳倒序排序，最新的在前面
                items.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));
                items
            }
            Err(_) => Vec::new(),
        },
        Err(_) => Vec::new(),
    }
}

/// 写回 source_record.json，更新所有源目录
async fn write_source_record(record_path: &Path, source_dirs: &[HistoryItem]) {
    let json_val = serde_json::json!({
        "items": source_dirs
    });
    match serde_json::to_string_pretty(&json_val) {
        Ok(json_str) => {
            let _ = async_fs::write(record_path, json_str).await;
        }
        Err(_) => {}
    }
}

/// 使用 tokio::io::copy 进行异步复制，并保留原文件的修改时间
async fn optimized_copy(src: &Path, dst: &Path) -> std::io::Result<()> {
    // 获取源文件的元数据（包括修改时间）
    let metadata = std::fs::metadata(src)?;
    let mtime = filetime::FileTime::from_system_time(metadata.modified()?);

    // 创建目标文件的父目录
    if let Some(parent) = dst.parent() {
        async_fs::create_dir_all(parent).await?;
    }

    // 打开源文件和创建目标文件
    let src_file = async_fs::File::open(src).await?;
    let dst_file = async_fs::File::create(dst).await?;

    // 使用缓冲读写器提高性能
    let mut reader = BufReader::with_capacity(16 * 1024, src_file);
    let mut writer = BufWriter::with_capacity(16 * 1024, dst_file);

    // 复制文件内容
    tokio::io::copy(&mut reader, &mut writer).await?;
    writer.flush().await?;

    // 设置目标文件的修改时间与源文件一致
    filetime::set_file_mtime(dst, mtime)?;

    Ok(())
}

/// 并行复制文件列表
async fn parallel_copy_files(files: &[(PathBuf, PathBuf)]) -> std::io::Result<()> {
    // 提前创建所有需要的父目录
    let parent_dirs: HashSet<PathBuf> = files
        .iter()
        .filter_map(|(_, dst)| dst.parent().map(|p| p.to_path_buf()))
        .collect();

    // 并行创建目录
    let create_dirs = parent_dirs
        .into_iter()
        .map(|dir| async_fs::create_dir_all(dir));
    join_all(create_dirs).await;

    let semaphore = Arc::new(Semaphore::new(MAX_CONCURRENT_COPIES));
    let mut futures = futures::stream::FuturesUnordered::new();

    for (src, dst) in files {
        let src_clone = src.clone();
        let dst_clone = dst.clone();
        let permit = semaphore.clone().acquire_owned().await.unwrap();

        futures.push(tokio::spawn(async move {
            let result = optimized_copy(&src_clone, &dst_clone).await;
            drop(permit);
            result
        }));
    }

    // 使用 FuturesUnordered 高效地处理所有任务
    while let Some(result) = futures.next().await {
        result??;
    }
    Ok(())
}

/// 并行删除文件
fn parallel_remove_files(files: &[PathBuf]) -> Vec<String> {
    files
        .par_iter()
        .filter_map(|file_path| match fs::remove_file(file_path) {
            Ok(_) => None,
            Err(e) => {
                let err_msg = format!("删除文件失败 {}: {}", file_path.display(), e);
                error!("{}", err_msg);
                Some(err_msg)
            }
        })
        .collect()
}

/// 清理所有空目录，按深度倒序排序
fn clean_empty_dirs(base_dir: &Path) -> Vec<String> {
    let mut dirs = collect_all_directories(base_dir);
    // 按深度倒序排序，确保先删除子目录
    dirs.par_sort_unstable_by_key(|d| std::usize::MAX - d.components().count());

    let errors = Arc::new(tokio::sync::Mutex::new(Vec::new()));
    dirs.par_iter().for_each(|dir| {
        match fs::read_dir(&dir) {
            Ok(mut entries) => {
                if entries.next().is_none() {
                    // 空目录，尝试删除
                    if let Err(e) = fs::remove_dir(&dir) {
                        let err_msg = format!("删除目录失败 {}: {}", dir.display(), e);
                        error!("{}", err_msg);
                        let mut errs = errors.blocking_lock();
                        errs.push(err_msg);
                    }
                }
            }
            Err(e) => {
                let err_msg = format!("读取目录失败 {}: {}", dir.display(), e);
                error!("{}", err_msg);
                let mut errs = errors.blocking_lock();
                errs.push(err_msg);
            }
        }
    });
    match Arc::try_unwrap(errors) {
        Ok(mutex) => mutex.into_inner(),
        Err(_) => panic!("仍有Arc引用存在"),
    }
}

/// 收集所有需要检查的目录，包括所有嵌套的子目录
fn collect_all_directories(base_dir: &Path) -> Vec<PathBuf> {
    let dirs: Vec<PathBuf> = WalkDir::new(base_dir)
        .into_iter()
        .filter_map(|e| match e {
            Ok(entry) => {
                let path = entry.path();
                if entry.file_type().is_dir() && !should_ignore(path) {
                    Some(path.to_owned())
                } else {
                    None
                }
            }
            Err(err) => {
                error!("遍历目录时出错: {}", err);
                None
            }
        })
        .collect();

    dirs
}

/// 处理manifest文件
async fn process_manifest<R: Runtime>(
    app: &AppHandle<R>,
    manifest_path: String,
) -> Result<(), String> {
    // info!("发送事件到前端，处理文件: {}", manifest_path);

    // 创建一个 oneshot channel
    let (tx, rx) = tokio::sync::oneshot::channel();

    // 生成唯一的事件ID
    let event_id = uuid::Uuid::new_v4().to_string();

    // 注册一个一次性事件监听器
    let event_name = format!("manifestProcessed_{}", event_id);
    let events = app.once(&event_name, move |_event: Event| {
        let _ = tx.send(());
    });

    // 发送事件到前端，包含事件ID
    app.emit(
        "processManifestFile",
        serde_json::json!({
            "xmlPath": manifest_path,
            "eventId": event_id
        }),
    )
    .map_err(|e| {
        error!("发送事件失败: {}", e);
        e.to_string()
    })?;

    // info!("事件已发送，等待前端处理完成");

    // 等待前端处理完成
    match rx.await {
        Ok(_) => {
            // info!("前端处理完成");
            // 清理事件监听器
            app.unlisten(events);
            Ok(())
        }
        Err(e) => {
            error!("等待前端处理完成时发生错误");
            app.unlisten(events);
            Err(format!("等待前端处理失败: {}", e))
        }
    }
}

/// 处理 description.xml 文件，根据 uiVersion 复制对应的 theme_fallback.xml
/// 使用唯一的缓存目录名，确保不同路径但同名的目录不会冲突
async fn process_description_xml<R: Runtime>(
    app_handle: &AppHandle<R>,
    source_path: &Path,
    _cache_dir: &Path,
    cache_sub_dir: &Path,
) -> Result<String, String> {
    // 查找 description.xml 文件
    let description_path = source_path.join("description.xml");

    if !path_exists_safe(&description_path) {
        return Ok("未找到 description.xml 文件，跳过处理".to_string());
    }

    // 读取 XML 文件内容
    let content = match fs::read_to_string(&description_path) {
        Ok(content) => content,
        Err(e) => return Err(format!("无法读取 description.xml 文件: {}", e)),
    };

    // 解析 XML 文件中的 uiVersion 标签值
    let ui_version = match extract_ui_version(&content) {
        Some(version) => version,
        None => {
            return Ok("未找到 uiVersion 标签，跳过处理".to_string());
        }
    };

    // 根据 uiVersion 确定目标 drawable 目录的相对路径
    let drawable_relative_path = {
        // 加载版本映射配置
        let mapping = match load_version_mapping_for_cache(&app_handle) {
            Ok(mapping) => mapping,
            Err(e) => {
                error!("加载版本映射配置失败: {}", e);
                // 使用默认映射作为降级方案
                return get_default_drawable_path(ui_version);
            }
        };

        mapping.get_drawable_path(ui_version)
    };

    // 使用 Tauri API 解析打包的资源路径
    let drawable_path = match app_handle.path().resolve(
        &drawable_relative_path,
        tauri::path::BaseDirectory::Resource,
    ) {
        Ok(path) => {
            let normalized_path = normalize_windows_path(path);
            info!(
                "根据 uiVersion={}，使用 drawable 路径: {}",
                ui_version,
                normalized_path.display()
            );
            normalized_path
        }
        Err(e) => {
            let err_msg = format!(
                "无法解析 drawable 资源路径 '{}': {}",
                drawable_relative_path, e
            );
            error!("{}", err_msg);
            return Err(err_msg);
        }
    };

    if !is_dir_safe(&drawable_path) {
        return Ok(format!(
            "目标 drawable 目录不存在: {}, 跳过处理",
            drawable_path.display()
        ));
    }

    // 列出 drawable 目录中的可用主题文件夹
    let available_theme_dirs = if let Ok(entries) = fs::read_dir(&drawable_path) {
        entries
            .filter_map(|e| e.ok())
            .filter(|e| e.path().is_dir())
            .filter_map(|e| {
                let name = e.file_name().to_string_lossy().to_string();
                let theme_path = e.path().join("theme_fallback.xml");
                if path_exists_safe(&theme_path) {
                    Some((name, theme_path))
                } else {
                    None
                }
            })
            .collect::<Vec<(String, PathBuf)>>()
    } else {
        Vec::new()
    };

    // 遍历每个可用主题目录，将 theme_fallback.xml 复制到缓存目录的对应子目录
    let mut copied_count = 0;
    let mut skipped_count = 0;

    for (dir_name, source_theme_fallback) in &available_theme_dirs {
        // 在缓存目录下查找对应的子目录
        let target_dir = cache_sub_dir.join(dir_name);

        // 检查目标目录是否存在
        if !is_dir_safe(&target_dir) {
            skipped_count += 1;
            continue;
        }

        // 目标文件路径
        let target_theme_fallback = target_dir.join("theme_fallback.xml");

        // 如果目标文件已存在则跳过
        if path_exists_safe(&target_theme_fallback) {
            skipped_count += 1;
            continue;
        }

        // 复制文件
        match async_fs::copy(&source_theme_fallback, &target_theme_fallback).await {
            Ok(_) => {
                copied_count += 1;
            }
            Err(e) => {
                error!(
                    "复制 theme_fallback.xml 失败: {} -> {}: {}",
                    source_theme_fallback.display(),
                    target_theme_fallback.display(),
                    e
                );
                skipped_count += 1;
            }
        }
    }

    let result = format!(
        "处理 theme_fallback.xml: 成功复制到 {} 个目录，跳过 {} 个目录",
        copied_count, skipped_count
    );
    Ok(result)
}

/// 从 XML 内容中提取 uiVersion 标签值
fn extract_ui_version(xml_content: &str) -> Option<u32> {
    let ui_version_start = xml_content.find("<uiVersion>")?;
    let ui_version_end = xml_content.find("</uiVersion>")?;

    if ui_version_start < ui_version_end {
        let start_pos = ui_version_start + 11; // "<uiVersion>".len()
        let version_str = &xml_content[start_pos..ui_version_end];
        version_str.trim().parse::<u32>().ok()
    } else {
        None
    }
}

/// 为缓存文件处理加载版本映射配置
fn load_version_mapping_for_cache<R: tauri::Runtime>(
    app_handle: &AppHandle<R>,
) -> Result<VersionMapping, Box<dyn std::error::Error>> {
    // 尝试从资源目录加载
    if let Ok(resource_path) = app_handle.path().resolve(
        "config/version_mapping.json5",
        tauri::path::BaseDirectory::Resource,
    ) {
        if resource_path.exists() {
            return VersionMapping::load_from_config(&resource_path).map_err(|e| e.into());
        }
    }

    // 如果资源目录没有，尝试从开发目录加载
    let dev_path = Path::new("src-tauri/config/version_mapping.json5");
    if dev_path.exists() {
        return VersionMapping::load_from_config(dev_path).map_err(|e| e.into());
    }

    // 如果都没有，返回默认配置
    info!("未找到版本映射配置文件，使用默认配置");
    Ok(create_default_mapping_for_cache())
}

/// 创建默认版本映射（用于缓存文件处理）
fn create_default_mapping_for_cache() -> VersionMapping {
    use std::collections::HashMap;

    let mut version_mapping = HashMap::new();
    version_mapping.insert("10".to_string(), "v12".to_string());
    version_mapping.insert("15".to_string(), "v15".to_string());
    version_mapping.insert("16".to_string(), "v16".to_string());
    version_mapping.insert("18".to_string(), "v18".to_string());

    let mut version_descriptions = HashMap::new();
    version_descriptions.insert("10".to_string(), "MIUI 12".to_string());
    version_descriptions.insert("15".to_string(), "MIUI 15".to_string());
    version_descriptions.insert("16".to_string(), "MIUI 16".to_string());
    version_descriptions.insert("18".to_string(), "MIUI 18".to_string());

    VersionMapping {
        version_mapping,
        default_version: "v15".to_string(),
        version_descriptions,
    }
}

/// 获取默认的 drawable 路径（降级方案）
fn get_default_drawable_path(ui_version: u32) -> Result<String, String> {
    let drawable_relative_path = match ui_version {
        10 => "drawable/v12",
        15 => "drawable/v15",
        16 => "drawable/v16",
        18 => "drawable/v18",
        _ => {
            info!("不支持的 uiVersion: {}, 使用默认路径 v15", ui_version);
            "drawable/v15"
        }
    };

    Ok(format!(
        "使用默认映射: uiVersion={} -> {}",
        ui_version, drawable_relative_path
    ))
}

/// 缓存文件主函数
#[tauri::command]
pub async fn 缓存文件(
    app_handle: AppHandle,
    source_path: String,
    timestamp: Option<u64>,
) -> Result<String, String> {
    // 初始化 Rayon 线程池(可选)
    let num_threads = num_cpus::get() * 2;
    let _ = ThreadPoolBuilder::new()
        .num_threads(num_threads)
        .build_global();

    let start_time = Instant::now();
    let source = Path::new(&source_path);
    if !is_dir_safe(source) {
        return Err(format!("源目录不存在或不是目录: {}", source_path));
    }

    let dir_name = match source.file_name() {
        Some(name) => match name.to_str() {
            Some(s) => s,
            None => {
                return Err("目录名称包含非法字符".to_string());
            }
        },
        None => {
            return Err("无法获取目录名称".to_string());
        }
    };

    // 获取缓存根目录
    let cache_dir = 获取缓存目录(app_handle.clone())
        .await
        .map_err(|e| format!("获取缓存目录失败: {}", e))?
        .parse::<PathBuf>()
        .map_err(|e| format!("路径解析失败: {}", e))?;

    // 生成唯一的缓存目录名，确保不同路径但同名的目录不会冲突
    let unique_cache_dir_name = generate_unique_cache_dir_name(&source_path, dir_name);

    let cache_sub_dir = cache_dir.join(&unique_cache_dir_name);

    match async_fs::create_dir_all(&cache_sub_dir).await {
        Ok(_) => (),
        Err(_e) => (),
    };

    // 读取缓存根目录下的 source_record.json
    let source_record_path = cache_dir.join("source_record.json");
    let mut cached_sources = read_source_record(&source_record_path).await;

    // <unique_cache_dir_name>_record.bin
    let dir_record_name = format!("{}_record.bin", unique_cache_dir_name); // 使用二进制格式
    let dir_record_path = cache_dir.join(dir_record_name);

    // 并行收集当前源目录文件信息
    let source_collect_start = Instant::now();
    let source_map = task::spawn_blocking({
        let source_clone = source.to_path_buf();
        move || collect_file_info(&source_clone)
    })
    .await
    .map_err(|e| {
        let err_msg = format!("收集源目录信息失败: {:?}", e);
        log::error!("{}", err_msg);
        err_msg
    })?;
    log::info!(
        "完成源目录文件信息收集，共{}个文件，耗时: {:?}",
        source_map.len(),
        source_collect_start.elapsed()
    );

    // 用于区分新增文件和修改文件
    let mut new_files = Vec::new();
    let mut modified_files = Vec::new();
    // 用于存放最终要复制的所有文件
    let mut files_to_copy = Vec::new();
    // 用于存放最终要删除的所有文件
    let mut files_to_delete = Vec::new();

    // 判断是否是首次缓存
    let is_first_time = !cached_sources.iter().any(|item| item.path == source_path);

    if is_first_time {
        // 全量复制
        for (rel_path, _) in &source_map {
            let src_path = source.join(rel_path);
            let dst_path = cache_sub_dir.join(rel_path);
            files_to_copy.push((src_path, dst_path));
            // 首次缓存所有都可以看作是"新增"
            new_files.push(rel_path.clone());
        }
    } else {
        // 增量缓存
        let old_record_result = read_dir_record(&dir_record_path).await;
        if let Some(old_record) = old_record_result {
            // 1) 对比：新增 or 修改
            for (rel_path, src_info) in &source_map {
                let src_rec = src_info.to_record();
                match old_record.get(rel_path) {
                    Some(old_rec) => {
                        // 判断是否修改
                        if old_rec.size != src_rec.size || old_rec.modified != src_rec.modified {
                            files_to_copy
                                .push((source.join(rel_path), cache_sub_dir.join(rel_path)));
                            modified_files.push(rel_path.clone());
                        }
                    }
                    None => {
                        // 新增
                        files_to_copy.push((source.join(rel_path), cache_sub_dir.join(rel_path)));
                        new_files.push(rel_path.clone());
                    }
                }
            }
            // 2) 删除
            for (old_rel_path, _) in &old_record {
                if !source_map.contains_key(old_rel_path) {
                    files_to_delete.push(cache_sub_dir.join(old_rel_path));
                }
            }
        } else {
            // 若找不到旧记录，则认为全量复制(可视为首次)
            for (rel_path, _) in &source_map {
                files_to_copy.push((source.join(rel_path), cache_sub_dir.join(rel_path)));
                new_files.push(rel_path.clone());
            }
        }
    }

    // 并行复制

    let copy_result = parallel_copy_files(&files_to_copy).await;
    if let Err(e) = copy_result {
        let err_msg = format!("并行复制文件失败: {}", e);
        log::error!("{}", err_msg);
        return Err(err_msg);
    }

    // 首次缓存或有新增文件时处理 description.xml 并复制 theme_fallback.xml
    if is_first_time || !new_files.is_empty() {
        match process_description_xml(&app_handle, source, &cache_dir, &cache_sub_dir).await {
            Ok(_version) => {}
            Err(_e) => {
                // 继续执行，不阻塞进程
            }
        }
    }

    // 处理新增和修改的manifest.xml文件
    let manifest_files: Vec<String> = files_to_copy
        .iter()
        .filter_map(|(_, dst_path)| {
            if dst_path.file_name().and_then(|n| n.to_str()) == Some("manifest.xml") {
                Some(dst_path.to_string_lossy().to_string())
            } else {
                None
            }
        })
        .collect();

    // 收集处理结果的日志
    let mut success_logs = Vec::new();
    let mut error_logs = Vec::new();

    // 串行处理所有manifest文件，确保每个处理完成
    for manifest_path in manifest_files {
        match process_manifest(&app_handle, manifest_path.clone()).await {
            Ok(_) => {
                success_logs.push(manifest_path);
            }
            Err(e) => {
                let err_msg = format!("处理manifest文件失败 {}: {}", manifest_path, e);
                error_logs.push(err_msg.clone());
                return Err(err_msg);
            }
        }
    }

    // 构建单条完整的日志消息
    let mut log_message = String::new();

    if !success_logs.is_empty() {
        for path in &success_logs {
            log_message.push_str(&format!("  ✓ {}\n", path));
        }
    }

    if !error_logs.is_empty() {
        log_message.push_str(&format!(
            "\n处理失败的manifest文件 ({}):\n",
            error_logs.len()
        ));
        for err in &error_logs {
            log_message.push_str(&format!("  ✗ {}\n", err));
        }
    }

    // 一次性打印所有日志
    if !log_message.is_empty() {
        info!("{}", log_message.trim());
    }

    // 并行删除文件

    let delete_errors = parallel_remove_files(&files_to_delete);

    // 清理空目录

    let clean_errors = clean_empty_dirs(&cache_sub_dir);

    // 写回 <dir_name>_record.bin
    let new_record_map = source_map
        .iter()
        .map(|(rel_path, info)| (rel_path.clone(), info.to_record()))
        .collect::<FxHashMap<_, _>>();

    write_dir_record(&dir_record_path, &new_record_map).await;

    // 首次缓存则记录到 source_record.json
    if is_first_time {
        // 生成唯一的缓存目录名
        let unique_cache_dir_name = generate_unique_cache_dir_name(&source_path, dir_name);

        let current_timestamp = timestamp.unwrap_or_else(|| {
            SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs()
        });

        cached_sources.push(HistoryItem {
            path: source_path.clone(),
            timestamp: current_timestamp,
            cache_dir_name: unique_cache_dir_name.clone(),
        });

        let write_history_start = Instant::now();
        write_source_record(&source_record_path, &cached_sources).await;
        log::debug!(
            "完成历史记录写入，耗时: {:?}",
            write_history_start.elapsed()
        );
    }

    // 在处理完之后，统一打印增删改的文件列表（使用折叠格式）
    let mut log_messages = Vec::new();

    // 使用特殊标记表示折叠日志开始
    log_messages.push("文件变更详情 [折叠]".to_string());

    // 新增
    if !new_files.is_empty() {
        log_messages.push(format!("新增文件 ({}):", new_files.len()));
        for file in &new_files {
            log_messages.push(format!("  + {}", file));
        }
    }

    // 修改
    if !modified_files.is_empty() {
        log_messages.push(format!("修改文件 ({}):", modified_files.len()));
        for file in &modified_files {
            log_messages.push(format!("  ~ {}", file));
        }
    }

    // 删除
    if !files_to_delete.is_empty() {
        log_messages.push(format!("删除文件 ({}):", files_to_delete.len()));
        for path in &files_to_delete {
            if let Ok(rel) = path.strip_prefix(&cache_sub_dir) {
                log_messages.push(format!("  - {}", rel.to_string_lossy()));
            } else {
                log_messages.push(format!("  - {}", path.display()));
            }
        }
    }

    // 一次性打印所有日志，使用特殊格式表示这是折叠日志
    if !log_messages.is_empty() {
        // 只打印汇总信息
        info!(
            "文件变更：新增 {} 个，修改 {} 个，删除 {} 个",
            new_files.len(),
            modified_files.len(),
            files_to_delete.len()
        );

        // 创建专门为前端控制台设计的消息格式
        // 使用特殊前缀标记，前端可以据此识别并特殊处理这类消息
        let console_log_marker = "CONSOLE_OBJECT:";

        // 构建文件变更数据
        let changes_data = serde_json::json!({
            "文件变更": {
                "新增": new_files,
                "修改": modified_files,
                "删除": files_to_delete.iter().filter_map(|path| {
                    path.strip_prefix(&cache_sub_dir).ok().map(|p| p.to_string_lossy().to_string())
                }).collect::<Vec<_>>()
            }
        });

        // 输出带特殊标记的JSON对象，前端可以识别并特殊处理
        info!(
            "{}{}",
            console_log_marker,
            serde_json::to_string(&changes_data).unwrap_or_default()
        );
    }

    // 若有删除/清理错误信息，则在此打印
    if !delete_errors.is_empty() {
        error!("删除文件时发生错误:\n{}", delete_errors.join("\n"));
    }
    if !clean_errors.is_empty() {
        error!("清理空目录时发生错误:\n{}", clean_errors.join("\n"));
    }

    let elapsed = start_time.elapsed();
    log::info!(
        "缓存文件处理完成，总耗时: {:?}，缓存路径: {}",
        elapsed,
        cache_sub_dir.display()
    );

    Ok(cache_sub_dir.to_string_lossy().to_string())
}

/// 删除历史记录
#[tauri::command(rename_all = "camelCase")]
pub async fn 删除历史记录(app_handle: AppHandle, source_path: String) -> Result<(), String> {
    // 获取缓存根目录
    let cache_dir = 获取缓存目录(app_handle.clone())
        .await
        .map_err(|e| format!("获取缓存目录失败: {}", e))?
        .parse::<PathBuf>()
        .map_err(|e| format!("路径解析失败: {}", e))?;

    // 规范化路径（处理不同平台的路径分隔符）
    let source = PathBuf::from(&source_path);
    let dir_name = source
        .file_name()
        .ok_or_else(|| format!("无法获取目录名称: {}", source_path))?
        .to_str()
        .ok_or_else(|| format!("目录名称包含非法字符: {}", source_path))?;

    // 读取 source_record.json
    let source_record_path = cache_dir.join("source_record.json");
    let mut cached_sources = read_source_record(&source_record_path).await;

    // 如果路径不存在于记录中，返回错误
    let history_item = cached_sources.iter().find(|item| item.path == source_path);
    if history_item.is_none() {
        return Err(format!("未找到该路径的缓存记录: {}", source_path));
    }

    // 获取唯一的缓存目录名
    let cache_dir_name = if let Some(item) = history_item {
        // 使用记录中存储的缓存目录名（如果有）
        if !item.cache_dir_name.is_empty() {
            item.cache_dir_name.clone()
        } else {
            // 兼容旧版本，生成唯一的缓存目录名
            generate_unique_cache_dir_name(&source_path, dir_name)
        }
    } else {
        // 这个分支实际上不会执行，因为前面已经检查了 history_item 是否存在
        generate_unique_cache_dir_name(&source_path, dir_name)
    };

    // 删除缓存目录
    let cache_sub_dir = cache_dir.join(&cache_dir_name);
    if path_exists_safe(&cache_sub_dir) {
        if let Err(e) = async_fs::remove_dir_all(&cache_sub_dir).await {
            let err_msg = format!("删除缓存目录失败: {}", e);
            error!("{}", err_msg);
            return Err(err_msg);
        }
    }

    // 删除记录文件
    let record_file = cache_dir.join(format!("{}_record.bin", cache_dir_name));
    if path_exists_safe(&record_file) {
        if let Err(e) = async_fs::remove_file(&record_file).await {
            let err_msg = format!("删除记录文件失败: {}", e);
            error!("{}", err_msg);
            return Err(err_msg);
        }
    }

    // 从 source_record.json 中移除该路径
    cached_sources.retain(|item| item.path != source_path);
    write_source_record(&source_record_path, &cached_sources).await;

    Ok(())
}

/// 删除历史记录和缓存
#[tauri::command(rename_all = "snake_case")]
pub async fn 删除历史记录和缓存(
    app_handle: AppHandle,
    source_path: String,
    cache_dir: String,
) -> Result<(), String> {
    // 删除历史记录
    删除历史记录(app_handle.clone(), source_path.clone()).await?;

    // 获取缓存根目录
    let cache_root = 获取缓存目录(app_handle.clone())
        .await
        .map_err(|e| format!("获取缓存目录失败: {}", e))?
        .parse::<PathBuf>()
        .map_err(|e| format!("路径解析失败: {}", e))?;

    // 获取主题名称
    let dir_name = Path::new(&source_path)
        .file_name()
        .and_then(|name| name.to_str())
        .ok_or_else(|| "无法获取主题名称".to_string())?;

    // 读取 source_record.json 获取唯一的缓存目录名
    let source_record_path = cache_root.join("source_record.json");
    let cached_sources = read_source_record(&source_record_path).await;

    // 查找对应路径的缓存目录名
    let unique_cache_dir_name = cached_sources
        .iter()
        .find(|item| item.path == source_path)
        .and_then(|item| {
            if !item.cache_dir_name.is_empty() {
                Some(item.cache_dir_name.clone())
            } else {
                None
            }
        })
        .unwrap_or_else(|| generate_unique_cache_dir_name(&source_path, dir_name));

    // 需要删除的路径列表
    let paths_to_delete = vec![
        // 1. 主题缓存目录
        Path::new(&cache_dir).to_path_buf(),
        // 2. mtz文件
        cache_root.join(format!("{}.mtz", unique_cache_dir_name)),
        // 3. 临时文件（如果有）
        cache_root.join(format!("{}_temp.mtz", unique_cache_dir_name)),
    ];

    // 并行删除所有路径
    for path in paths_to_delete {
        if path_exists_safe(&path) {
            let result = if is_dir_safe(&path) {
                async_fs::remove_dir_all(&path).await
            } else {
                async_fs::remove_file(&path).await
            };

            if let Err(e) = result {
                let err_msg = format!("删除失败 {}: {}", path.display(), e);
                error!("{}", err_msg);
                // 继续删除其他文件，而不是立即返回错误
                continue;
            }
        }
    }

    Ok(())
}
