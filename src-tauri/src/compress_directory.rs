use anyhow::Error as AnyhowError;
use log::error;
use memmap2::MmapOptions;
use rayon::prelude::*;
use std::io::{BufWriter, Write};
use std::path::Path;
use std::sync::Arc;
use tauri::Error as InvokeError;
use tokio::sync::Mutex;
use tokio::task;
use zip::{write::FileOptions, CompressionMethod, ZipWriter};

const BUFFER_SIZE: usize = 128 * 1024 * 1024; // 128MB
const BATCH_SIZE: usize = 5000; // 增加批处理大小
const LARGE_FILE_THRESHOLD: u64 = 100 * 1024 * 1024; // 100MB

// 获取CPU核心数
lazy_static::lazy_static! {
    static ref CPU_CORES: usize = num_cpus::get() * 2;
}

#[derive(Clone)]
struct FileEntry {
    relative_path: String,
    data: Arc<Vec<u8>>,
}

async fn process_file_batch(
    files: Vec<std::path::PathBuf>,
    from_path: &Path,
) -> Vec<Result<FileEntry, std::io::Error>> {
    let results: Vec<_> = files
        .into_par_iter()
        .map(|path| {
            let relative_path = path
                .strip_prefix(from_path)
                .unwrap()
                .to_str()
                .unwrap()
                .to_string();

            let data = if path.metadata().map(|m| m.len()).unwrap_or(0) > LARGE_FILE_THRESHOLD {
                let file = std::fs::File::open(&path)?;
                let mmap = unsafe { MmapOptions::new().map(&file)? };
                mmap.to_vec()
            } else {
                std::fs::read(&path)?
            };

            Ok(FileEntry {
                relative_path,
                data: Arc::new(data),
            })
        })
        .collect();

    results
}

// 创建文件权限的 FileOptions
fn create_file_options() -> FileOptions {
    FileOptions::default()
        .compression_method(CompressionMethod::Stored)
        .unix_permissions(0o777) // -rwxrwxrwx 完整读写执行权限
}

// 创建目录权限的 FileOptions
fn create_directory_options() -> FileOptions {
    FileOptions::default()
        .compression_method(CompressionMethod::Stored)
        .unix_permissions(0o40777) // 040777 = d rwx rwx rwx，其中 040000 是目录标志位
}

#[tauri::command]
pub async fn 压缩目录(
    _app_handle: tauri::AppHandle,
    from: String,
    to: String,
    delete_source: Option<bool>,
) -> Result<(), InvokeError> {
    // 先创建带 .zip 后缀的文件路径
    let zip_path = format!("{}.zip", to);

    let from_clone = from.clone();
    // 并行收集文件列表
    let files: Vec<_> = task::spawn_blocking(move || {
        walkdir::WalkDir::new(&from_clone)
            .into_iter()
            .filter_map(|e| e.ok())
            .filter(|e| e.file_type().is_file())
            .map(|e| e.path().to_owned())
            .collect::<Vec<_>>()
    })
    .await?;

    // 创建ZIP文件
    let file = std::fs::File::create(&zip_path)?;
    let buffered = BufWriter::with_capacity(BUFFER_SIZE, file);
    let zip_writer = Arc::new(Mutex::new(ZipWriter::new(buffered)));

    // 分批处理文件
    let from_path = Path::new(&from);
    let chunks = files.chunks(BATCH_SIZE);

    let file_options = create_file_options();
    let dir_options = create_directory_options();

    for (_i, chunk) in chunks.enumerate() {
        // 并行处理当前批次的文件
        let results = process_file_batch(chunk.to_vec(), from_path).await;

        // 写入处理好的文件
        let mut zip = zip_writer.lock().await;
        for result in results {
            match result {
                Ok(entry) => {
                    // 检查是否为目录路径
                    if entry.relative_path.ends_with('/') {
                        if let Err(e) = zip.add_directory(&entry.relative_path, dir_options) {
                            error!("创建目录条目失败 {}: {}", entry.relative_path, e);
                            continue;
                        }
                    } else {
                        if let Err(e) = zip.start_file(&entry.relative_path, file_options) {
                            error!("创建文件条目失败 {}: {}", entry.relative_path, e);
                            continue;
                        }

                        if let Err(e) = zip.write_all(&entry.data) {
                            error!("写入文件内容失败 {}: {}", entry.relative_path, e);
                            continue;
                        }
                    }
                }
                Err(e) => error!("处理文件失败: {}", e),
            }
        }
    }

    // 完成ZIP文件
    let mut zip = zip_writer.lock().await;
    if let Err(e) = zip.finish() {
        // 清理zip文件
        if let Err(e) = std::fs::remove_file(&zip_path) {
            error!("清理zip文件失败: {}", e);
        }
        return Err(InvokeError::from(AnyhowError::from(e)));
    }

    // 修改删除源文件/目录的逻辑
    let should_delete = delete_source.unwrap_or(from == to);
    if should_delete {
        if let Err(e) = std::fs::remove_dir_all(&from) {
            error!("删除源目录失败: {}", e);
            return Err(InvokeError::from(AnyhowError::msg(e)));
        }
    }

    // 重命名 .zip 文件为无后缀文件
    if let Err(e) = std::fs::rename(&zip_path, &to) {
        error!("重命名zip文件失败: {}", e);
        return Err(InvokeError::from(AnyhowError::msg(e)));
    }

    Ok(())
}
