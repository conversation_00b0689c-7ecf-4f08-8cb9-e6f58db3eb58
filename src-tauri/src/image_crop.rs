use crate::commands::获取配置文件路径;
use anyhow::{Context, Result};
use image::{<PERSON>B<PERSON><PERSON>, Rgba};
use json5;
use rayon::prelude::*;
use serde::Deserialize;
use std::fs;
use std::sync::Arc;

#[derive(Deserialize)]
struct ClipConfig {
    #[serde(deserialize_with = "deserialize_file_names")]
    file_name: Vec<String>,
    module: String,
    #[serde(default)]
    non_nine_patch: bool,
    #[serde(deserialize_with = "deserialize_y")]
    w: String,
    #[serde(deserialize_with = "deserialize_y")]
    h: String,
    #[serde(deserialize_with = "deserialize_y")]
    x: String,
    #[serde(deserialize_with = "deserialize_y")]
    y: String,
    #[serde(deserialize_with = "deserialize_vec")]
    x_divs: Vec<String>,
    #[serde(deserialize_with = "deserialize_vec")]
    y_divs: Vec<String>,
    #[serde(deserialize_with = "deserialize_vec")]
    bottom: Vec<String>,
    #[serde(deserialize_with = "deserialize_vec")]
    right: Vec<String>,
    #[serde(deserialize_with = "deserialize_vec", default = "default_padding")]
    padding: Vec<String>,
}

// 自定义反序列化函数，支持数字和字符串
fn deserialize_y<'de, D>(deserializer: D) -> Result<String, D::Error>
where
    D: serde::Deserializer<'de>,
{
    use serde::de::Error;
    use serde_json::Value;

    match Value::deserialize(deserializer)? {
        Value::String(s) => Ok(s),
        Value::Number(n) => Ok(n.to_string()),
        _ => Err(Error::custom("y必须是一个字符串或数字")),
    }
}

// 自定义反序列化函数，支持数组中的数字和字符串
fn deserialize_vec<'de, D>(deserializer: D) -> Result<Vec<String>, D::Error>
where
    D: serde::Deserializer<'de>,
{
    use serde::de::Error;
    use serde_json::Value;

    let values = Vec::<Value>::deserialize(deserializer)?;
    values
        .into_iter()
        .map(|v| match v {
            Value::String(s) => Ok(s),
            Value::Number(n) => Ok(n.to_string()),
            _ => Err(Error::custom("数组元素必须是字符串或数字")),
        })
        .collect()
}

// 添加新的反序列化函数来处理 file_name
fn deserialize_file_names<'de, D>(deserializer: D) -> Result<Vec<String>, D::Error>
where
    D: serde::Deserializer<'de>,
{
    use serde::de::Error;
    use serde_json::Value;

    match Value::deserialize(deserializer)? {
        // 如果是字符串，转换为单元素数组
        Value::String(s) => Ok(vec![s]),
        // 如果是数组，转换所有元素为字符串
        Value::Array(arr) => arr
            .into_iter()
            .map(|v| match v {
                Value::String(s) => Ok(s),
                Value::Number(n) => Ok(n.to_string()),
                _ => Err(Error::custom("文件名必须是字符串或数字")),
            })
            .collect(),
        _ => Err(Error::custom("file_name必须是字符串或数组")),
    }
}

#[derive(Deserialize)]
struct Config {
    #[serde(flatten)]
    clips: std::collections::HashMap<String, std::collections::HashMap<String, ClipConfig>>,
}

// 添加一个简单的词法分析器来处理表达式
#[derive(Debug, Clone)]
#[allow(dead_code)]
enum Token {
    Number(f64),
    ImageWidth,
    ImageHeight,
    Plus,
    Minus,
    Multiply,
    Divide,
    LeftParen,
    RightParen,
}

fn tokenize(expr: &str) -> Result<Vec<Token>> {
    let mut tokens = Vec::new();
    let mut chars = expr.chars().peekable();

    while let Some(&c) = chars.peek() {
        match c {
            ' ' => {
                chars.next();
            }
            '(' => {
                tokens.push(Token::LeftParen);
                chars.next();
            }
            ')' => {
                tokens.push(Token::RightParen);
                chars.next();
            }
            '+' => {
                tokens.push(Token::Plus);
                chars.next();
            }
            '-' => {
                tokens.push(Token::Minus);
                chars.next();
            }
            '*' => {
                tokens.push(Token::Multiply);
                chars.next();
            }
            '/' => {
                tokens.push(Token::Divide);
                chars.next();
            }
            '0'..='9' => {
                let mut num = String::new();
                while let Some(&c) = chars.peek() {
                    if c.is_digit(10) || c == '.' {
                        num.push(c);
                        chars.next();
                    } else {
                        break;
                    }
                }
                let value = num.parse::<f64>().context("无效的数字")?;
                tokens.push(Token::Number(value));
            }
            'i' => {
                let mut word = String::new();
                while let Some(&c) = chars.peek() {
                    if c.is_alphabetic() || c == '.' {
                        word.push(c);
                        chars.next();
                    } else {
                        break;
                    }
                }
                match word.as_str() {
                    "image.width" => tokens.push(Token::ImageWidth),
                    "image.height" => tokens.push(Token::ImageHeight),
                    _ => return Err(anyhow::anyhow!("未知的标识符: {}", word)),
                }
            }
            _ => return Err(anyhow::anyhow!("无效的字符: {}", c)),
        }
    }
    Ok(tokens)
}

fn apply_op(values: &mut Vec<f64>, op: &Token) -> Result<()> {
    if values.len() < 2 {
        return Err(anyhow::anyhow!("表达式格式错误"));
    }
    let b = values.pop().unwrap();
    let a = values.pop().unwrap();
    let result = match op {
        Token::Plus => a + b,
        Token::Minus => a - b,
        Token::Multiply => a * b,
        Token::Divide => {
            if b == 0.0 {
                return Err(anyhow::anyhow!("除数不能为零"));
            }
            a / b
        }
        _ => return Err(anyhow::anyhow!("无效的运算符")),
    };
    values.push(result);
    Ok(())
}

fn precedence(token: &Token) -> u8 {
    match token {
        Token::Plus | Token::Minus => 1,
        Token::Multiply | Token::Divide => 2,
        _ => 0,
    }
}

fn evaluate_tokens(tokens: &[Token], img_width: u32, img_height: u32) -> Result<f64> {
    let mut values = Vec::new();
    let mut operators = Vec::new();

    for token in tokens {
        match token {
            Token::Number(n) => values.push(*n),
            Token::ImageWidth => values.push(img_width as f64),
            Token::ImageHeight => values.push(img_height as f64),
            Token::LeftParen => operators.push(token.clone()),
            Token::RightParen => {
                while let Some(op) = operators.last() {
                    if matches!(op, Token::LeftParen) {
                        break;
                    }
                    apply_op(&mut values, op)?;
                    operators.pop();
                }
                if let Some(Token::LeftParen) = operators.last() {
                    operators.pop();
                }
            }
            op @ (Token::Plus | Token::Minus | Token::Multiply | Token::Divide) => {
                while let Some(top) = operators.last() {
                    if matches!(top, Token::LeftParen) || precedence(top) < precedence(&op) {
                        break;
                    }
                    apply_op(&mut values, top)?;
                    operators.pop();
                }
                operators.push(op.clone());
            }
        }
    }

    while let Some(op) = operators.pop() {
        apply_op(&mut values, &op)?;
    }

    values
        .pop()
        .ok_or_else(|| anyhow::anyhow!("表达式计算错误"))
}

// 更新evaluate_dimension函数
fn evaluate_dimension(expr: &str, img_width: u32, img_height: u32) -> Result<u32> {
    let expr = expr.trim().to_lowercase();

    // 处理纯数字
    if let Ok(num) = expr.parse::<u32>() {
        return Ok(num);
    }

    // 词法分析并计算表达式
    let tokens = tokenize(&expr)?;
    let result = evaluate_tokens(&tokens, img_width, img_height)?;

    // 确保结果为正数并转换为u32
    if result < 0.0 {
        return Err(anyhow::anyhow!("计算结果不能为负数: {}", result));
    }

    Ok(result.round() as u32)
}

// 添加默认padding值的函数
fn default_padding() -> Vec<String> {
    vec![
        "0".to_string(),
        "0".to_string(),
        "0".to_string(),
        "0".to_string(),
    ]
}

#[tauri::command]
pub async fn 图片裁切(
    app_handle: tauri::AppHandle,
    image_path: &str,
) -> Result<String, String> {
    let image_path = image_path.to_string();
    let app_handle = app_handle.clone();
    match tokio::task::spawn_blocking(move || process_image(app_handle, &image_path)).await {
        Ok(result) => result.map_err(|e| e.to_string()),
        Err(e) => Err(format!("处理图片时发生错误: {}", e)),
    }
}

fn process_image(app_handle: tauri::AppHandle, image_path: &str) -> Result<String> {
    let config_path = std::path::PathBuf::from(
        获取配置文件路径(app_handle, "image_clip_config.json5".to_string()).unwrap(),
    );

    let config_content = fs::read_to_string(&config_path).context("无法读取配置文件")?;

    // 打印配置文件内容以便调试

    let config: Config = json5::from_str(&config_content).map_err(|e| {
        println!("解析错误: {}", e);
        anyhow::anyhow!("配置文件格式错误: {}", e)
    })?;

    // 读取源图片
    let img = image::open(image_path).context("无法打开图片，需要真实格式为png的图片。")?;

    // 检查图片尺寸是否满足要求（宽度至少为1080像素，高度至少为2400像素）
    let width = img.width();
    let height = img.height();
    if width < 1080 || height < 2400 {
        return Err(anyhow::anyhow!(
            "图片尺寸不符合要求：当前尺寸为 {}x{}，要求尺寸至少为 1080x2400",
            width,
            height
        ));
    }

    let img = Arc::new(img.to_rgba8());

    // 获取桌面路径
    let desktop = dirs::desktop_dir().context("无法获取桌面路径")?;

    // 创建基础输出目录
    let mut index = 0;
    let output_base_dir = loop {
        let dir_name = if index == 0 {
            "bg-clip".to_string()
        } else {
            format!("bg-clip-{}", index)
        };
        let dir = desktop.join(&dir_name);
        if !dir.exists() {
            fs::create_dir_all(&dir)?;
            break dir;
        }
        index += 1;
    };

    // 并行处理每个顶层对象
    config
        .clips
        .par_iter()
        .try_for_each(|(parent_name, clips)| {
            // 创建父目录
            let parent_dir = output_base_dir.join(parent_name);
            fs::create_dir_all(&parent_dir)?;

            // 按 module 分组
            let mut module_tasks: std::collections::HashMap<String, Vec<(&String, &ClipConfig)>> =
                std::collections::HashMap::new();

            for (clip_name, clip_config) in clips {
                module_tasks
                    .entry(clip_config.module.clone())
                    .or_default()
                    .push((clip_name, clip_config));
            }

            // 处理每个模块
            module_tasks
                .par_iter()
                .try_for_each(|(module_name, tasks)| {
                    // 创建模块目录
                    let module_dir = parent_dir.join(module_name);
                    fs::create_dir_all(&module_dir)?;

                    // 处理模块内的每个裁切任务
                    tasks.par_iter().try_for_each(|(_, clip_config)| {
                        // 计算裁切区域
                        let x = evaluate_dimension(&clip_config.x, img.width(), img.height())?;
                        let y = evaluate_dimension(&clip_config.y, img.width(), img.height())?;
                        let w = evaluate_dimension(&clip_config.w, img.width(), img.height())?;
                        let h = evaluate_dimension(&clip_config.h, img.width(), img.height())?;

                        // 验证裁切区域
                        if x + w > img.width() || y + h > img.height() {
                            return Err(anyhow::anyhow!(
                        "裁切区域超出图片范围: 图片大小 {}x{}, 裁切区域 x={}, y={}, w={}, h={}",
                        img.width(),
                        img.height(),
                        x,
                        y,
                        w,
                        h
                    ));
                        }

                        // 获取padding值
                        let (new_width, new_height, padding_top, padding_left) =
                            if clip_config.non_nine_patch {
                                (w, h, 0, 0) // 非9.png不需要边框和padding
                            } else {
                                let padding_top = evaluate_dimension(
                                    &clip_config.padding.get(0).unwrap_or(&"0".to_string()),
                                    w,
                                    h,
                                )?;
                                let padding_right = evaluate_dimension(
                                    &clip_config.padding.get(1).unwrap_or(&"0".to_string()),
                                    w,
                                    h,
                                )?;
                                let padding_bottom = evaluate_dimension(
                                    &clip_config.padding.get(2).unwrap_or(&"0".to_string()),
                                    w,
                                    h,
                                )?;
                                let padding_left = evaluate_dimension(
                                    &clip_config.padding.get(3).unwrap_or(&"0".to_string()),
                                    w,
                                    h,
                                )?;
                                (
                                    w + 2 + padding_left + padding_right,
                                    h + 2 + padding_top + padding_bottom,
                                    padding_top,
                                    padding_left,
                                )
                            };

                        // 创建新图片缓冲区
                        let mut cropped = ImageBuffer::new(new_width, new_height);

                        // 初始化为透明
                        for pixel in cropped.pixels_mut() {
                            *pixel = Rgba([0, 0, 0, 0]);
                        }

                        // 复制裁切区域
                        for dy in 0..h {
                            for dx in 0..w {
                                let src_x = x + dx;
                                let src_y = y + dy;
                                let dst_x = dx
                                    + if clip_config.non_nine_patch { 0 } else { 1 }
                                    + padding_left;
                                let dst_y = dy
                                    + if clip_config.non_nine_patch { 0 } else { 1 }
                                    + padding_top;
                                cropped.put_pixel(dst_x, dst_y, *img.get_pixel(src_x, src_y));
                            }
                        }

                        // 只有非9.png才绘制边框
                        if !clip_config.non_nine_patch {
                            let black = Rgba([0, 0, 0, 255]);

                            // 绘制分割线
                            // 绘制顶部分割线
                            for chunk in clip_config.x_divs.chunks(2) {
                                if chunk.len() == 2 {
                                    let start = evaluate_dimension(&chunk[0], w, h)?;
                                    let end = evaluate_dimension(&chunk[1], w, h)?;
                                    for x in start..=end {
                                        cropped.put_pixel(x + 1 + padding_left, 0, black);
                                        // 绘制在最顶部
                                    }
                                }
                            }

                            // 绘制左侧分割线
                            for chunk in clip_config.y_divs.chunks(2) {
                                if chunk.len() == 2 {
                                    let start = evaluate_dimension(&chunk[0], w, h)?;
                                    let end = evaluate_dimension(&chunk[1], w, h)?;
                                    for y in start..=end {
                                        cropped.put_pixel(0, y + 1 + padding_top, black);
                                        // 绘制在最左侧
                                    }
                                }
                            }

                            // 绘制底部分割线
                            if clip_config.bottom.len() == 2 {
                                let left_margin = evaluate_dimension(&clip_config.bottom[0], w, h)?;
                                let right_margin =
                                    evaluate_dimension(&clip_config.bottom[1], w, h)?;
                                for x in left_margin..=(w - 1 - right_margin) {
                                    cropped.put_pixel(x + 1 + padding_left, new_height - 1, black);
                                    // 绘制在最底部
                                }
                            }

                            // 绘制右侧分割线
                            if clip_config.right.len() == 2 {
                                let top_margin = evaluate_dimension(&clip_config.right[0], w, h)?;
                                let bottom_margin =
                                    evaluate_dimension(&clip_config.right[1], w, h)?;
                                for y in top_margin..=(h - 1 - bottom_margin) {
                                    cropped.put_pixel(new_width - 1, y + 1 + padding_top, black);
                                    // 绘制在最右侧
                                }
                            }
                        }

                        // 更新保存路径
                        for file_name in &clip_config.file_name {
                            let output_path = if clip_config.non_nine_patch {
                                module_dir.join(file_name)
                            } else {
                                module_dir.join(format!("{}", file_name))
                            };
                            cropped.save(&output_path)?;
                        }

                        Ok(())
                    })
                })?;

            Ok::<(), anyhow::Error>(())
        })?;

    Ok(output_base_dir.to_string_lossy().to_string())
}
