use memmap2::MmapOptions;
use rayon::prelude::*;
use serde::{Deserialize, Serialize};
use std::collections::HashSet;
use std::fs;
use std::fs::File;
use std::io::{self, ErrorK<PERSON>, Read};
use std::path::Path;
use tokio::task;

#[derive(Debug, Serialize, Deserialize)]
pub struct ColorData {
    name: String,
    target: String,
}

// 将颜色字符串转换为u32整数表示
fn color_to_u32(color: &str) -> Option<u32> {
    if color.len() == 7 && color.starts_with('#') {
        u32::from_str_radix(&color[1..], 16).ok()
    } else {
        None
    }
}

/// 使用内存映射读取JSON文件
fn read_large_json_file(
    file_path: &str,
) -> io::Result<std::collections::HashMap<String, ColorData>> {
    let file = File::open(file_path)?;
    let metadata = file.metadata()?;

    // 对于大文件使用内存映射
    if metadata.len() > 10 * 1024 * 1024 {
        let mmap = unsafe { MmapOptions::new().map(&file)? };
        serde_json::from_slice(&mmap).map_err(|err| {
            io::Error::new(ErrorKind::InvalidData, format!("无法解析JSON文件: {}", err))
        })
    } else {
        // 小文件直接读取
        let mut content = Vec::new();
        let mut file = file; // 重新绑定以获取可变引用
        file.read_to_end(&mut content)?;
        serde_json::from_slice(&content).map_err(|err| {
            io::Error::new(ErrorKind::InvalidData, format!("无法解析JSON文件: {}", err))
        })
    }
}

/// 使用并行遍历方式处理PNG图片，提取与data.json匹配的颜色
fn process_png_image(img_path: &str, data_path: &str, package: &str) -> io::Result<String> {
    let path = Path::new(img_path);

    // 基本检查
    if !path.exists() {
        return Err(io::Error::new(ErrorKind::NotFound, "输入文件不存在"));
    }
    if path.extension().and_then(|e| e.to_str()) != Some("png") {
        return Err(io::Error::new(ErrorKind::InvalidInput, "只支持PNG格式图片"));
    }

    // 检查是否存在同名JSON文件
    let json_path = path.with_extension("json");
    if json_path.exists() {
        return Ok(format!("已存在同名JSON文件: {}", json_path.display()));
    }

    // 加载颜色数据并优化为整数表示
    let colors_data = read_large_json_file(data_path)?;

    // 使用整数而非字符串表示颜色，极大提高查找效率
    let mut valid_colors_map = HashSet::new();
    let mut color_data_map = std::collections::HashMap::new();

    for (color, data) in colors_data {
        // 根据package参数确定是否添加颜色
        let should_add = if package == "null" {
            // 如果package为null，添加所有颜色
            true
        } else if (package == "com.android.systemui" || package == "miui.systemui.plugin")
            && (data.target == "com.android.systemui" || data.target == "miui.systemui.plugin")
        {
            // 如果package是systemui相关，在两个systemui包中查找
            true
        } else {
            // 其他情况正常匹配
            data.target == package
        };

        if should_add {
            if let Some(color_int) = color_to_u32(&color) {
                valid_colors_map.insert(color_int);
                color_data_map.insert(color_int, (color, data));
            }
        }
    }

    // 加载图片
    let img =
        image::open(path).map_err(|_e| io::Error::new(ErrorKind::InvalidInput, "无法打开图片"))?;
    let rgba_img = img.to_rgba8();
    let (width, height) = rgba_img.dimensions();

    // 使用Rayon并行处理像素数据
    let pixels = rgba_img.as_raw();

    // 使用整数表示颜色进行高效匹配
    let filtered_colors: HashSet<u32> = (0..(width * height) as usize)
        .into_par_iter()
        .filter_map(|i| {
            let offset = i * 4;
            // 快速跳过透明像素
            if pixels[offset + 3] == 0 {
                return None;
            }

            // 读取RGB并直接转换为整数
            let r = pixels[offset];
            let g = pixels[offset + 1];
            let b = pixels[offset + 2];

            // 将RGB直接组合为u32，避免字符串转换
            let color_int = ((r as u32) << 16) | ((g as u32) << 8) | (b as u32);

            if valid_colors_map.contains(&color_int) {
                Some(color_int)
            } else {
                None
            }
        })
        .collect();

    // 保存结果
    if !filtered_colors.is_empty() {
        let output_dir = path.parent().unwrap_or(Path::new("."));
        fs::create_dir_all(output_dir)?;

        let json_filename = format!("{}.json", path.file_stem().unwrap().to_str().unwrap());
        let output_path = output_dir.join(&json_filename);

        let colors_count = filtered_colors.len();

        // 转换回完整的ColorData格式用于输出
        let result_colors: std::collections::HashMap<String, ColorData> = filtered_colors
            .iter()
            .filter_map(|&color_int| {
                color_data_map.get(&color_int).map(|(color, data)| {
                    (
                        color.clone(),
                        ColorData {
                            name: data.name.clone(),
                            target: data.target.clone(),
                        },
                    )
                })
            })
            .collect();

        let json_content = serde_json::to_string_pretty(&result_colors)?;
        fs::write(&output_path, json_content)?;

        Ok(format!(
            "已保存 {} 个匹配颜色到: {}",
            colors_count,
            output_path.display()
        ))
    } else {
        Ok("未找到匹配的颜色".to_string())
    }
}

/// 异步处理图片颜色 - Tauri命令
#[tauri::command]
pub async fn search_color(
    input_file: String,
    data_path: String,
    package: String,
) -> Result<String, String> {
    task::spawn_blocking(move || {
        process_png_image(&input_file, &data_path, &package).map_err(|e| e.to_string())
    })
    .await
    .unwrap_or_else(|e| Err(format!("任务执行失败: {}", e)))
}
