// 1.将传入的.mtz文件解压到传入目录的同级目录，例如传入/home/<USER>/test.mtz，则解压到/home/<USER>/test。如果同级目录已经存在test目录则删除该目录后继续解压。
// 2.在解压后的test目录下，将所有实际类型为压缩文件的文件继续解压，递归遍历所有目录直到没有压缩文件为止，在解压后需要保证目录名称和压缩文件名称相同。
// 3.在解压过程中检查名称为.9.png的文件，如果存在则将.9.png文件反编译，如果反编译失败则打印错误信息，不阻塞进程继续执行。
// 4.解压完成后，返回成功或失败信息，成功时还要将文件的具体路径保存到"应用私有目录/历史文件.json"中，如果失败和"应用私有目录/历史文件.json"中已经存在该路径则不保存。如果没有"应用私有目录/历史文件.json"文件则创建该文件写入文件路径。
// 注意，在操作时将不影响结果的操作并行处理，一定要极大的加快速度
use futures::future::join_all;
use log::{error, info};
use memmap2::Mmap;
use rayon::prelude::*;
use std::fs;
use std::io::{self, BufReader, Read};
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::sync::Mutex as StdMutex;
use std::time::Instant;
use tokio::fs as async_fs;
use tokio::sync::Mutex;
use tokio::task;
use uuid::Uuid;
use walkdir::WalkDir;

// 检测ZIP格式压缩文件的魔数
fn is_compressed_file(path: &Path) -> io::Result<bool> {
    let file = fs::File::open(path)?;
    let metadata = file.metadata()?;

    if metadata.len() < 4 {
        return Ok(false);
    }

    // 对大文件使用内存映射,小文件直接读取
    if metadata.len() > 1024 * 1024 {
        if let Ok(mmap) = unsafe { Mmap::map(&file) } {
            if mmap.len() >= 4 {
                // 只检测ZIP格式魔数
                return Ok(&mmap[0..4] == &[0x50, 0x4B, 0x03, 0x04]);
            }
        }
    } else {
        let mut buffer = [0u8; 4];
        let mut reader = BufReader::new(file);
        if reader.read_exact(&mut buffer).is_ok() {
            // 只检测ZIP格式
            return Ok(&buffer == &[0x50, 0x4B, 0x03, 0x04]);
        }
    }

    // 如果文件没有扩展名但看起来像ZIP文件，也尝试解压
    if path.extension().is_none() && metadata.len() > 100 && metadata.len() < 1024 * 1024 * 100 {
        // 尝试读取文件开头检测是否为ZIP
        let file = fs::File::open(path)?;
        let mut reader = BufReader::new(file);
        let mut buffer = [0u8; 4];
        if reader.read_exact(&mut buffer).is_ok() {
            return Ok(&buffer == &[0x50, 0x4B, 0x03, 0x04]);
        }
    }

    Ok(false)
}

// 优化解压函数,使用异步IO和并行处理
async fn extract_file(source: &Path, target_dir: &Path) -> Result<(), String> {
    let start = Instant::now();
    info!("开始解压文件: {:?}", source);

    if !source.exists() {
        error!("源文件不存在: {:?}", source);
        return Err("源文件不存在".to_string());
    }

    // 创建临时目录
    let temp_dir = target_dir.with_file_name(format!("temp_{}", Uuid::new_v4()));
    async_fs::create_dir_all(&temp_dir).await.map_err(|e| {
        error!("创建临时目录失败: {}", e);
        format!("创建临时目录失败: {}", e)
    })?;

    // 读取文件内容
    let source_path = source.to_path_buf();
    let file_content = task::spawn_blocking(move || fs::read(&source_path))
        .await
        .map_err(|e| e.to_string())?
        .map_err(|e| {
            error!("读取文件内容失败: {}", e);
            e.to_string()
        })?;

    // 在一个阻塞任务中处理解压,使用rayon并行处理
    let temp_dir_clone = temp_dir.clone();
    task::spawn_blocking(move || -> Result<(), String> {
        let cursor = std::io::Cursor::new(file_content);
        let archive = Arc::new(StdMutex::new(zip::ZipArchive::new(cursor).map_err(
            |e| {
                error!("创建ZIP存档失败: {}", e);
                e.to_string()
            },
        )?));

        // 收集所有需要处理的文件
        let mut entries = Vec::new();
        {
            let mut archive = archive.lock().unwrap();
            for i in 0..archive.len() {
                let file = archive.by_index(i).map_err(|e| e.to_string())?;
                entries.push((i, file.name().to_string()));
            }
        }

        // 并行处理文件
        entries
            .par_iter()
            .try_for_each(|(i, name)| -> Result<(), String> {
                let mut archive = archive.lock().unwrap();
                let mut file = archive.by_index(*i).map_err(|e| e.to_string())?;
                // 标准化路径分隔符，将反斜杠替换为正斜杠，确保跨平台兼容性
                let normalized_name = name.replace('\\', "/");
                let outpath = temp_dir_clone.join(&normalized_name);

                if normalized_name.ends_with('/') {
                    fs::create_dir_all(&outpath).map_err(|e| {
                        error!("创建目录失败: {}", e);
                        e.to_string()
                    })?;
                    return Ok(());
                }

                if let Some(parent) = outpath.parent() {
                    fs::create_dir_all(parent).map_err(|e| {
                        error!("创建父目录失败: {}", e);
                        e.to_string()
                    })?;
                }

                let mut buffer = Vec::new();
                file.read_to_end(&mut buffer).map_err(|e| {
                    error!("读取文件内容失败: {}", e);
                    e.to_string()
                })?;
                fs::write(&outpath, buffer).map_err(|e| {
                    error!("写入文件失败: {}", e);
                    e.to_string()
                })?;
                Ok(())
            })?;

        Ok(())
    })
    .await
    .map_err(|e| e.to_string())??;

    // 重命名目录
    if target_dir.exists() {
        async_fs::remove_dir_all(target_dir).await.map_err(|e| {
            error!("删除已存在的目标目录失败: {}", e);
            format!("删除已存在的目标目录失败: {}", e)
        })?;
    }
    async_fs::rename(&temp_dir, target_dir).await.map_err(|e| {
        error!("重命名临时目录失败: {}", e);
        format!("重命名临时目录失败: {}", e)
    })?;

    info!("文件解压完成,耗时: {:?}", start.elapsed());
    Ok(())
}

// 异步处理压缩文件
async fn process_compressed_file(
    path: PathBuf,
    processed: Arc<Mutex<Vec<PathBuf>>>,
) -> Result<(), String> {
    let start = Instant::now();

    let path_clone = path.clone();
    let is_compressed = task::spawn_blocking(move || is_compressed_file(&path_clone))
        .await
        .map_err(|e| e.to_string())?
        .map_err(|e| {
            error!("检查压缩文件失败: {}", e);
            e.to_string()
        })?;

    let mut lock = processed.lock().await;
    if is_compressed && !lock.contains(&path) {
        lock.push(path.clone());
        drop(lock);

        // 确保解压后的目录名与文件名相同
        let file_name = path.file_name().unwrap().to_string_lossy().to_string();
        // 对于bootanimation.zip特殊处理
        let is_bootanimation = file_name == "bootanimation.zip";

        let target_dir_name = if is_bootanimation {
            "bootanimation".to_string()
        } else if file_name.ends_with(".zip") {
            // 如果有.zip后缀，去掉后缀
            file_name[0..file_name.len() - 4].to_string()
        } else {
            // 无后缀，直接使用文件名
            file_name
        };

        // 创建目标目录路径
        let target_dir = path.parent().unwrap().join(&target_dir_name);
        let temp_dir = path
            .parent()
            .unwrap()
            .join(format!("temp_{}", Uuid::new_v4()));

        // 尝试解压
        if let Err(e) = extract_file(&path, &temp_dir).await {
            error!("解压文件 {:?} 失败: {}", path, e);
            let _ = async_fs::remove_dir_all(&temp_dir).await;
            return Ok(());
        }

        // 解压成功，删除原文件
        if let Err(e) = async_fs::remove_file(&path).await {
            error!("删除原压缩文件失败: {}", e);
        }

        // 如果目标目录已存在，先删除
        if target_dir.exists() {
            if let Err(e) = async_fs::remove_dir_all(&target_dir).await {
                error!("删除目标目录失败: {}", e);
                return Err(e.to_string());
            }
        }

        // 重命名临时目录为目标目录
        if let Err(e) = async_fs::rename(&temp_dir, &target_dir).await {
            error!("重命名临时目录失败: {}", e);
            return Err(e.to_string());
        }

        info!("处理压缩文件 {:?} 完成,耗时: {:?}", path, start.elapsed());
    }
    Ok(())
}

// 优化递归处理函数,使用并行遍历
async fn recursive_extract(
    dir: &Path,
    processed: Arc<Mutex<Vec<PathBuf>>>,
    depth: usize,
) -> Result<(), String> {
    // 限制最大递归深度，防止堆栈溢出
    const MAX_DEPTH: usize = 10;
    if depth >= MAX_DEPTH {
        error!("达到最大递归深度 {}, 停止处理", MAX_DEPTH);
        return Ok(());
    }

    let start = Instant::now();
    info!("开始递归处理目录: {:?}, 当前深度: {}", dir, depth);

    // 获取当前已处理文件数
    let processed_count_before = processed.lock().await.len();

    // 发现一轮压缩文件并处理
    let entries: Vec<_> = WalkDir::new(dir)
        .into_iter()
        .filter_map(|e| e.ok())
        .filter(|e| e.path().is_file())
        .par_bridge() // 使用并行迭代器
        .map(|e| e.path().to_path_buf())
        .collect();

    let mut tasks = Vec::new();

    for path in entries {
        // 检查该文件是否已被处理，避免重复处理
        let lock = processed.lock().await;
        if lock.contains(&path) {
            continue;
        }
        drop(lock);

        let processed_clone = Arc::clone(&processed);
        tasks.push(process_compressed_file(path, processed_clone));
    }

    join_all(tasks).await.into_iter().try_for_each(|r| r)?;

    // 检查是否有新的压缩文件被解压出来
    let processed_count_after = processed.lock().await.len();
    let has_new_files = processed_count_after > processed_count_before;

    // 只有当有新文件被处理时才继续递归
    if has_new_files {
        info!(
            "发现{}个新文件，继续递归处理",
            processed_count_after - processed_count_before
        );
        // 增加递归深度，使用Box::pin包装递归调用
        return Box::pin(recursive_extract(dir, processed, depth + 1)).await;
    }

    info!("递归处理完成,耗时: {:?}, 深度: {}", start.elapsed(), depth);
    Ok(())
}

#[tauri::command]
pub async fn 解包主题(file_path: String) -> Result<String, String> {
    let start_time = Instant::now();
    info!("开始解包主题...");

    let source = Path::new(&file_path);
    let target_dir = source.with_extension("");

    if target_dir.exists() {
        async_fs::remove_dir_all(&target_dir).await.map_err(|e| {
            error!("删除已存在的目标目录失败: {}", e);
            e.to_string()
        })?;
    }

    extract_file(source, &target_dir).await?;

    let processed = Arc::new(Mutex::new(Vec::new()));
    // 从深度0开始递归解压
    recursive_extract(&target_dir, processed, 0).await?;

    info!("解包主题完成,总耗时: {:?}", start_time.elapsed());
    Ok(target_dir.to_string_lossy().into_owned())
}

#[allow(dead_code)]
fn scan_nine_png_files(input_path: &str) -> Result<Vec<PathBuf>, String> {
    let files: Vec<_> = WalkDir::new(input_path)
        .into_iter()
        .filter_map(|entry| match entry {
            Ok(e) => {
                if e.file_type().is_file() && e.path().to_string_lossy().contains(".9.png") {
                    Some(e.path().to_path_buf())
                } else {
                    None
                }
            }
            Err(_) => None,
        })
        .collect();

    Ok(files)
}
