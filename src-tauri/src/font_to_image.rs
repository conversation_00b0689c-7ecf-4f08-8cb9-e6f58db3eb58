// 创建一个函数，该函数接收字体文件路径,字体颜色,中文字体大小,英文字体大小,A组数字大小,B组数字大小，使用这些参数生成图片，
// 具体规则为 读取src-tauri/font_to_img_config.json5文件，使用json中对象名作为目录名称，对象中的键作为图片名称，键值为图片内容。生成的图片宽度为字体原始宽度，高度统一为每个对象中图片的最大的高度。其中数字需要生成两份，分别是A组数组和B组数字，文字的大小为A组数字大小和B组数字大小。生成的图片保存到桌面/font-to-img目录下，如果font-to-img目录已经存在，在font-to-img后面添加序号，例如font-to-img-1，font-to-img-2，font-to-img-3，以此类推。
// 函数要支持前端调用
use crate::commands::获取配置文件路径;
use directories::UserDirs;
use image::{ImageBuffer, Rgba};
use json5;
use rusttype::{Font, Scale};
use serde_json::Value;
use std::error::Error;
use std::fs;
use std::path::PathBuf;
use tauri::command;

// 计算文本尺寸的函数
fn calculate_text_dimensions(font: &Font, scale: Scale, text: &str) -> (u32, u32) {
    let glyphs: Vec<_> = font
        .layout(text, scale, rusttype::Point { x: 0.0, y: 0.0 })
        .collect();

    let v_metrics = font.v_metrics(scale);

    // 计算精确的边界框，包括字体的自然间距
    let mut min_x = std::f32::MAX;
    let mut max_x = std::f32::MIN;

    for glyph in &glyphs {
        if let Some(bb) = glyph.pixel_bounding_box() {
            min_x = min_x.min(bb.min.x as f32);
            max_x = max_x.max(bb.max.x as f32);
        }

        // 考虑字形的advance宽度
        let h_metrics = glyph.unpositioned().h_metrics();
        max_x = max_x.max(h_metrics.advance_width);
    }

    // 向上取整计算宽度和高度
    let width = if max_x > min_x {
        max_x.ceil() as u32
    } else {
        scale.y.ceil() as u32
    };

    // 使用字体度量计算实际高度
    let height = (v_metrics.ascent - v_metrics.descent + v_metrics.line_gap).ceil() as u32;

    (width, height)
}

// 在图片上绘制文本的函数
fn draw_text(
    text: &str,
    font: &Font,
    scale: Scale,
    color: [u8; 4],
    max_height: u32,
) -> Result<ImageBuffer<Rgba<u8>, Vec<u8>>, Box<dyn Error>> {
    let (width, _) = calculate_text_dimensions(font, scale, text);
    let mut image = ImageBuffer::new(width, max_height);

    // 获取字体度量
    let v_metrics = font.v_metrics(scale);

    // 布局文字，垂直居中
    let y_start = (max_height as f32 - (v_metrics.ascent - v_metrics.descent)) / 2.0;
    let glyphs: Vec<_> = font
        .layout(
            text,
            scale,
            rusttype::Point {
                x: 0.0,
                y: y_start + v_metrics.ascent,
            },
        )
        .collect();

    // 绘制文字
    for glyph in glyphs {
        if let Some(bounding_box) = glyph.pixel_bounding_box() {
            glyph.draw(|x, y, v| {
                let x = x as i32 + bounding_box.min.x;
                let y = y as i32 + bounding_box.min.y;

                if x >= 0 && x < width as i32 && y >= 0 && y < max_height as i32 {
                    let pixel = image.get_pixel_mut(x as u32, y as u32);
                    *pixel = Rgba([color[0], color[1], color[2], (v * color[3] as f32) as u8]);
                }
            });
        }
    }

    Ok(image)
}

// 获取递增的输出目录路径
fn get_output_dir() -> PathBuf {
    let user_dirs = UserDirs::new().unwrap();
    let desktop_dir = user_dirs.desktop_dir().unwrap();
    let mut counter = 0;
    let mut output_dir;

    loop {
        output_dir = if counter == 0 {
            desktop_dir.join("font-to-img")
        } else {
            desktop_dir.join(format!("font-to-img-{}", counter))
        };

        if !output_dir.exists() {
            break;
        }
        counter += 1;
    }

    output_dir
}

// 解析颜色字符串为RGBA数组
fn parse_color(color: &str) -> [u8; 4] {
    let color = color.trim_start_matches('#');
    let r = u8::from_str_radix(&color[0..2], 16).unwrap_or(0);
    let g = u8::from_str_radix(&color[2..4], 16).unwrap_or(0);
    let b = u8::from_str_radix(&color[4..6], 16).unwrap_or(0);
    let a = if color.len() == 8 {
        u8::from_str_radix(&color[6..8], 16).unwrap_or(255)
    } else {
        255
    };
    [r, g, b, a]
}

// 将点数转换为像素
fn pt_to_px(pt_size: f32) -> f32 {
    // 使用标准屏幕分辨率 96 DPI
    // 1px = 1pt * (DPI/72)
    pt_size * 1.194444444444444444445
}

// 创建Scale对象
fn create_scale(pt_size: f32) -> Scale {
    let px_size = pt_to_px(pt_size);
    Scale {
        x: px_size,
        y: px_size,
    }
}

#[command]
pub async fn 字体生成图片(
    _app_handle: tauri::AppHandle,
    font_path: String,
    color: String,
    cn_size: f32,
    en_size: f32,
    num_size_a: f32,
    num_size_b: f32,
) -> Result<PathBuf, String> {
    // 添加调试日志
    println!("点到像素转换:");
    println!("中文字体: {}pt = {}px", cn_size, pt_to_px(cn_size));
    println!("英文字体: {}pt = {}px", en_size, pt_to_px(en_size));
    println!("数字A组: {}pt = {}px", num_size_a, pt_to_px(num_size_a));
    println!("数字B组: {}pt = {}px", num_size_b, pt_to_px(num_size_b));

    // 读取字体文件
    let font_data = fs::read(&font_path).map_err(|e| e.to_string())?;
    let font = Font::try_from_vec(font_data).ok_or("无效的字体文件")?;

    // 解析颜色
    let rgba = parse_color(&color);

    // 从 src-tauri 目录读取配置文件
    let config_path = std::path::PathBuf::from(
        获取配置文件路径(_app_handle.clone(), "font_to_img_config.json5".to_string()).unwrap(),
    );

    let json_content = fs::read_to_string(&config_path)
        .map_err(|e| format!("读取配置文件失败 ({}): {}", config_path.display(), e))?;
    let config: Value =
        json5::from_str(&json_content).map_err(|e| format!("解析配置文件失败: {}", e))?;

    // 创建输出目录
    let output_dir = get_output_dir();
    fs::create_dir_all(&output_dir).map_err(|e| format!("创建输出目录失败: {}", e))?;

    // 遍历配置文件中的每个分类
    for (category, items) in config.as_object().unwrap() {
        if category == "数字" {
            let category_dir_a = output_dir.join(format!("数字A组_{:}pt", num_size_a));
            let category_dir_b = output_dir.join(format!("数字B组_{:}pt", num_size_b));
            fs::create_dir_all(&category_dir_a)
                .map_err(|e| format!("创建A组数字目录失败: {}", e))?;
            fs::create_dir_all(&category_dir_b)
                .map_err(|e| format!("创建B组数字目录失败: {}", e))?;

            let items_map = items.as_object().unwrap();

            // 先计算A组和B组的最大高度
            let mut max_height_a = 0u32;
            let mut max_height_b = 0u32;

            // 计算所有文本在各自尺寸下的最大高度
            for text in items_map.values() {
                let text = text.as_str().unwrap();

                let scale_a = create_scale(num_size_a);
                let (_, height_a) = calculate_text_dimensions(&font, scale_a, text);
                max_height_a = max_height_a.max(height_a);

                let scale_b = create_scale(num_size_b);
                let (_, height_b) = calculate_text_dimensions(&font, scale_b, text);
                max_height_b = max_height_b.max(height_b);
            }

            // 使用统一的最大高度生成图片
            for (filename, text) in items_map {
                let text = text.as_str().unwrap();

                // A组图片
                let scale_a = create_scale(num_size_a);
                let image_a = draw_text(text, &font, scale_a, rgba, max_height_a)
                    .map_err(|e| format!("生成A组数字图片失败: {}", e))?;
                let output_path_a = category_dir_a.join(format!("{}.png", filename));
                image_a
                    .save(&output_path_a)
                    .map_err(|e| format!("保存A组数字图片失败: {}", e))?;

                // B组图片
                let scale_b = create_scale(num_size_b);
                let image_b = draw_text(text, &font, scale_b, rgba, max_height_b)
                    .map_err(|e| format!("生成B组数字图片失败: {}", e))?;
                let output_path_b = category_dir_b.join(format!("{}.png", filename));
                image_b
                    .save(&output_path_b)
                    .map_err(|e| format!("保存B组数字图片失败: {}", e))?;
            }
        } else {
            // 处理其他类别
            let items_map = items.as_object().unwrap();

            // 确定该分类使用的字体大小
            let first_text = items_map.values().next().unwrap().as_str().unwrap();
            let size = if first_text.chars().next().unwrap().is_ascii() {
                en_size
            } else {
                cn_size
            };

            let category_dir = output_dir.join(format!("{}_{:}pt", category, size));
            fs::create_dir_all(&category_dir).map_err(|e| format!("创建分类目录失败: {}", e))?;

            // 先计算该分类下所有文本的最大高度
            let mut max_height = 0u32;
            for text in items_map.values() {
                let text = text.as_str().unwrap();
                let scale = create_scale(size);
                let (_, height) = calculate_text_dimensions(&font, scale, text);
                max_height = max_height.max(height);
            }

            // 使用统一的最大高度生成所有图片
            for (filename, text) in items_map {
                let text = text.as_str().unwrap();
                let scale = create_scale(size);

                let image = draw_text(text, &font, scale, rgba, max_height)
                    .map_err(|e| format!("生成图片失败: {}", e))?;
                let output_path = category_dir.join(format!("{}.png", filename));
                image
                    .save(&output_path)
                    .map_err(|e| format!("保存图片失败: {}", e))?;
            }
        }
    }

    // 直接返回输出目录路径
    Ok(output_dir)
}
