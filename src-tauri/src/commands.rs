use log::error;
use rand;
use serde_json::{json, Value};
use std::fs;
use std::io::Write;
use std::path::Path;
use std::sync::Arc;
use tauri::Manager;
use tauri_plugin_opener::OpenerExt;
use walkdir::WalkDir;
use which::which;

// 用同步IO获取缓存目录
#[tauri::command]
pub async fn 获取缓存目录(app_handle: tauri::AppHandle) -> Result<String, String> {
    let cache_dir = app_handle
        .path()
        .app_local_data_dir()
        .map_err(|e| {
            error!("无法获取缓存目录: {}", e);
            format!("无法获取缓存目录: {}", e)
        })?
        .join("cache");

    std::fs::create_dir_all(&cache_dir).map_err(|e| {
        error!("无法创建缓存目录: {}", e);
        format!("无法创建缓存目录: {}", e)
    })?;

    Ok(cache_dir.to_string_lossy().into_owned())
}

// 获取应用路径
#[tauri::command]
pub fn get_app_path() -> Result<String, String> {
    match std::env::current_exe() {
        Ok(exe_path) => {
            if cfg!(target_os = "macos") {
                // 在macOS上，可执行文件位于 .app/Contents/MacOS/ 目录下
                // 我们返回 MacOS 目录的路径，以便直接在其中查找 adb 和 aapt
                if let Some(macos_dir) = exe_path.parent() {
                    return Ok(macos_dir.to_string_lossy().to_string());
                }
            } else if cfg!(target_os = "windows") {
                // 在Windows上，返回可执行文件所在目录
                if let Some(exe_dir) = exe_path.parent() {
                    return Ok(exe_dir.to_string_lossy().to_string());
                }
            } else {
                // 在Linux上，返回可执行文件所在目录
                if let Some(exe_dir) = exe_path.parent() {
                    return Ok(exe_dir.to_string_lossy().to_string());
                }
            }
            Ok(exe_path.to_string_lossy().to_string())
        }
        Err(e) => Err(format!("无法获取应用路径: {}", e)),
    }
}

// 使用内存映射和并行处理优化文件提取
#[tauri::command]
pub async fn 提取压缩文件(
    app_handle: tauri::AppHandle,
    input_path: String,
    file_name: String,
) -> Result<String, String> {
    let cache_dir = 获取缓存目录(app_handle).await?;
    let output_path = Path::new(&cache_dir).join(&file_name);

    // 使用内存映射读取大文件
    let file = std::fs::File::open(&input_path).map_err(|e| {
        error!("无法打开压缩文件: {}", e);
        format!("无法打开压缩文件: {}", e)
    })?;
    let mmap = unsafe { memmap2::Mmap::map(&file) }.map_err(|e| format!("无法映射文件: {}", e))?;

    let mut archive = zip::ZipArchive::new(std::io::Cursor::new(&mmap))
        .map_err(|e| format!("无法读取压缩文件: {}", e))?;

    // 同步搜索文件
    let mut found_index = None;
    for i in 0..archive.len() {
        if let Ok(file) = archive.by_index(i) {
            if Path::new(file.name())
                .file_name()
                .map(|n| n.to_string_lossy().into_owned())
                == Some(file_name.clone())
            {
                found_index = Some(i);
                break;
            }
        }
    }

    if let Some(index) = found_index {
        let mut file = archive
            .by_index(index)
            .map_err(|e| format!("无法读取文件: {}", e))?;

        let mut outfile =
            std::fs::File::create(&output_path).map_err(|e| format!("无法创建输出文件: {}", e))?;

        // 使用缓冲写入提高性能
        let mut buffer = Vec::with_capacity(1024 * 1024); // 1MB buffer
        std::io::copy(&mut file, &mut buffer).map_err(|e| format!("无法复制文件内容: {}", e))?;
        outfile
            .write_all(&buffer)
            .map_err(|e| format!("无法写入文件: {}", e))?;

        Ok(output_path.to_string_lossy().into_owned())
    } else {
        Err(format!("未找到文件 {}", file_name))
    }
}

// 同步检查路径是否存在
#[tauri::command]
pub async fn 检查路径是否存在(path: String) -> Result<bool, String> {
    Ok(std::fs::metadata(&path).is_ok())
}

/// 清空目录内容
///
/// # 参数
/// * `path` - 要清空的目录路径
///
/// # 返回值
/// * `Result<(), String>` - 成功返回 Ok(()), 失败返回错误信息
#[tauri::command]
pub async fn 清空目录(path: String) -> Result<(), String> {
    // 检查路径是否存在
    if !Path::new(&path).exists() {
        return Err(format!("目录不存在: {}", path));
    }

    // 检查是否为目录
    if !Path::new(&path).is_dir() {
        return Err(format!("路径不是目录: {}", path));
    }

    // 读取目录内容
    let entries = match fs::read_dir(&path) {
        Ok(entries) => entries,
        Err(e) => {
            println!("读取目录失败: {}", e);
            return Err(format!("读取目录失败: {}", e));
        }
    };

    // 使用多线程并行删除文件和目录
    let errors = Arc::new(tokio::sync::Mutex::new(Vec::new()));
    let mut handles: Vec<tokio::task::JoinHandle<()>> = Vec::new();

    for entry in entries {
        if let Ok(entry) = entry {
            let path = entry.path();
            let errors = Arc::clone(&errors);

            let task = tokio::task::spawn(async move {
                if path.is_file() {
                    if let Err(e) = fs::remove_file(&path) {
                        println!("删除文件失败: {:?}, 错误: {}", path, e);
                        let mut errors = errors.lock().await;
                        errors.push(format!("删除文件 {:?} 失败: {}", path, e));
                    } else {
                        println!("成功删除文件: {:?}", path);
                    }
                } else if path.is_dir() {
                    if let Err(e) = fs::remove_dir_all(&path) {
                        println!("删除目录失败: {:?}, 错误: {}", path, e);
                        let mut errors = errors.lock().await;
                        errors.push(format!("删除目录 {:?} 失败: {}", path, e));
                    } else {
                        println!("成功删除目录: {:?}", path);
                    }
                }
            });
            handles.push(task);
        }
    }
    // 等待所有任务完成
    futures::future::join_all(handles).await;

    // 检查是否有错误
    let errors = errors.lock().await;
    if !errors.is_empty() {
        return Err(errors.join("\n"));
    }
    Ok(())
}

// 复制文件
#[tauri::command]
pub async fn 复制文件(from: String, to: String) -> Result<(), String> {
    let from_path = Path::new(&from);
    let to_path = Path::new(&to);

    // 检查源路径是否存在
    if !from_path.exists() {
        return Err(format!("源路径不存在: {}", from));
    }

    // 确保目标父目录存在
    if let Some(parent) = to_path.parent() {
        if !parent.exists() {
            fs::create_dir_all(parent).map_err(|e| format!("创建目标目录失败: {}", e))?;
        }
    }

    // 根据源路径类型执行不同的复制操作
    if from_path.is_file() {
        // 复制文件
        fs::copy(&from, &to).map_err(|e| format!("复制文件失败: {}", e))?;
    } else if from_path.is_dir() {
        // 复制目录
        copy_dir_recursive(from_path, to_path).await?;
    } else {
        return Err(format!("无效的源路径类型: {}", from));
    }

    Ok(())
}

// 递归复制目录的辅助函数
async fn copy_dir_recursive(from: &Path, to: &Path) -> Result<(), String> {
    // 创建目标目录
    fs::create_dir_all(to).map_err(|e| format!("创建目标目录失败: {}", e))?;

    // 遍历源目录
    let mut dir = fs::read_dir(from).map_err(|e| format!("读取目录失败: {}", e))?;
    let mut tasks = Vec::new();

    while let Some(entry) = dir
        .next()
        .transpose()
        .map_err(|e| format!("读取目录项失败: {}", e))?
    {
        let from_path = entry.path();
        let to_path = to.join(from_path.file_name().unwrap());

        if from_path.is_file() {
            // 直接复制文件
            let from_path = from_path.clone();
            let to_path = to_path.clone();
            let task = tokio::spawn(async move {
                tokio::fs::copy(&from_path, &to_path)
                    .await
                    .map_err(|e| format!("复制文件失败: {}", e))?;
                Ok::<(), String>(())
            });
            tasks.push(task);
        } else if from_path.is_dir() {
            // 使用 Box::pin 处理递归
            let future = Box::pin(copy_dir_recursive(&from_path, &to_path));
            future.await?;
        }
    }

    // 等待所有文件复制任务完成
    for task in tasks {
        task.await.map_err(|e| format!("任务执行失败: {}", e))??;
    }

    Ok(())
}

#[tauri::command]
pub async fn read_file(path: String) -> Result<String, String> {
    fs::read_to_string(&path).map_err(|e| format!("读取文件失败: {}", e))
}

#[tauri::command]
pub async fn 创建目录(path: String) -> Result<(), String> {
    // 检查目录是否存在
    if Path::new(&path).exists() {
        // 如果存在,清空目录内容
        fs::remove_dir_all(&path).map_err(|e| format!("清空目录失败: {}", e))?;
    }

    // 创建目录
    fs::create_dir_all(&path).map_err(|e| format!("创建目录失败: {}", e))?;

    Ok(())
}

#[tauri::command]
pub async fn 打开目录或文件(
    app_handle: tauri::AppHandle,
    path: String,
) -> Result<(), String> {
    let path_buf = Path::new(&path);
    if !path_buf.exists() {
        return Err(format!("路径 {} 不存在", path));
    }

    app_handle
        .opener()
        .open_path(&path, None::<&str>)
        .map_err(|e| format!("打开路径失败: {}", e))?;

    println!("已打开路径: {}", path);
    Ok(())
}
#[tauri::command]
pub async fn open_uri(app_handle: tauri::AppHandle, uri: String) -> Result<(), String> {
    app_handle
        .opener()
        .open_url(&uri, None::<&str>)
        .map_err(|e| format!("打开网址失败: {}", e))?;

    println!("已打开网页: {}", uri);
    Ok(())
}

#[tauri::command]
pub async fn 清理失效路径(app_handle: tauri::AppHandle) -> Result<Value, String> {
    let start_time = std::time::Instant::now();
    println!("开始清理失效路径...");

    let cache_dir = 获取缓存目录(app_handle.clone()).await?;
    let json_path = Path::new(&cache_dir).join("source_record.json");

    // 读取 JSON 文件
    let json_content =
        fs::read_to_string(&json_path).map_err(|e| format!("读取source_record.json失败: {}", e))?;

    let mut json: Value =
        serde_json::from_str(&json_content).map_err(|e| format!("解析JSON失败: {}", e))?;

    // 获取并处理 items 数组
    let items = json["items"]
        .as_array_mut()
        .ok_or_else(|| "items字段不是数组".to_string())?;

    // 收集所有有效的基础文件名（不含扩展名）
    let mut valid_base_names = std::collections::HashSet::new();

    // 使用tokio并行处理路径检查
    let mut handles: Vec<
        tokio::task::JoinHandle<
            Result<(String, u64, bool, Option<String>, Option<String>), String>,
        >,
    > = Vec::new();
    let cache_dir_clone = cache_dir.clone();

    // 为每个路径创建一个异步任务
    for item in items.iter() {
        let path = item
            .get("path")
            .and_then(|v| v.as_str())
            .ok_or_else(|| "路径不是字符串".to_string())?
            .to_string();
        let timestamp = item
            .get("timestamp")
            .and_then(|v| v.as_u64())
            .ok_or_else(|| "时间戳不是数字".to_string())?;

        // 获取缓存目录名（如果存在）
        let cache_dir_name = item
            .get("cache_dir_name")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string());

        let cache_dir = cache_dir_clone.clone();
        // 移除未使用的变量 app_handle_clone

        let handle = tokio::spawn(async move {
            let path_exists = Path::new(&path).exists();
            if path_exists {
                // 如果原始路径存在，保留记录
                Ok((path, timestamp, true, None, None))
            } else {
                println!("发现失效路径: {}", path);

                // 获取路径的最后一部分作为文件夹名
                if let Some(folder_name) = Path::new(&path).file_name() {
                    // 使用与缓存文件函数相同的方式生成唯一的缓存目录名
                    let dir_name = folder_name.to_str().unwrap_or("");

                    // 优先使用记录中的缓存目录名
                    let unique_cache_dir_name = if let Some(name) = &cache_dir_name {
                        if !name.is_empty() {
                            name.clone()
                        } else {
                            // 使用与缓存文件函数相同的方式生成唯一的缓存目录名
                            use crate::cache_files::generate_unique_cache_dir_name;
                            generate_unique_cache_dir_name(&path, dir_name)
                        }
                    } else {
                        // 使用与缓存文件函数相同的方式生成唯一的缓存目录名
                        use crate::cache_files::generate_unique_cache_dir_name;
                        generate_unique_cache_dir_name(&path, dir_name)
                    };

                    let folder_to_remove = Path::new(&cache_dir).join(&unique_cache_dir_name);
                    let mut _deleted_folder = None;

                    // 如果缓存目录中存在对应文件夹，则删除
                    if folder_to_remove.exists() {
                        println!("删除失效缓存目录: {}", folder_to_remove.display());
                        if let Err(e) = tokio::fs::remove_dir_all(&folder_to_remove).await {
                            let err_msg = format!("删除目录失败: {}", e);
                            println!("{}", err_msg);
                            return Ok((
                                path,
                                timestamp,
                                false,
                                Some(err_msg),
                                Some(unique_cache_dir_name),
                            ));
                        }
                        _deleted_folder = Some(folder_to_remove.to_string_lossy().to_string());
                    } else {
                        println!("缓存目录不存在: {}", folder_to_remove.display());
                    }

                    // 删除关联的.mtz文件
                    let mtz_path =
                        Path::new(&cache_dir).join(format!("{}.mtz", unique_cache_dir_name));
                    if mtz_path.exists() {
                        println!("删除.mtz文件: {}", mtz_path.display());
                        if let Err(e) = tokio::fs::remove_file(&mtz_path).await {
                            println!("删除.mtz文件失败: {}", e);
                        }
                    }

                    // 删除关联的_record.bin文件
                    let record_path =
                        Path::new(&cache_dir).join(format!("{}_record.bin", unique_cache_dir_name));
                    if record_path.exists() {
                        println!("删除_record.bin文件: {}", record_path.display());
                        if let Err(e) = tokio::fs::remove_file(&record_path).await {
                            println!("删除_record.bin文件失败: {}", e);
                        }
                    } else {
                        println!("_record.bin文件不存在: {}", record_path.display());
                    }

                    Ok((path, timestamp, false, None, Some(unique_cache_dir_name)))
                } else {
                    Ok((path, timestamp, false, None, None))
                }
            }
        });
        handles.push(handle);
    }

    // 等待所有任务完成并收集结果
    let mut valid_items = Vec::new();
    for handle in handles {
        match handle.await {
            Ok(result) => match result {
                Ok((path, timestamp, is_valid, error, cache_dir_name)) => {
                    if let Some(err) = error {
                        println!("处理路径 {} 时发生错误: {}", path, err);
                    }
                    if is_valid {
                        // 构建包含缓存目录名的记录
                        let mut item = json!({
                            "path": path,
                            "timestamp": timestamp
                        });

                        // 如果有缓存目录名，添加到记录中
                        if let Some(name) = cache_dir_name {
                            item["cache_dir_name"] = json!(name);
                        }

                        valid_items.push(item);

                        // 获取路径的最后一部分作为文件夹名
                        if let Some(folder_name) = Path::new(&path).file_name() {
                            valid_base_names.insert(folder_name.to_string_lossy().to_string());
                        }
                    }
                }
                Err(e) => println!("处理路径时发生错误: {}", e),
            },
            Err(e) => println!("任务执行失败: {}", e),
        }
    }

    println!("有效路径数量: {}", valid_items.len());

    // 不再使用基础文件名来清理文件，因为我们现在使用唯一的缓存目录名
    // 所有无效的记录已经在上面的处理中被删除了

    let elapsed = start_time.elapsed();
    println!("清理失效路径完成，耗时: {:?}", elapsed);

    // 更新 JSON 中的 items
    json["items"] = json!(valid_items);

    // 写回文件
    tokio::fs::write(
        &json_path,
        serde_json::to_string_pretty(&json).map_err(|e| format!("JSON序列化失败: {}", e))?,
    )
    .await
    .map_err(|e| format!("写入文件失败: {}", e))?;

    Ok(json)
}

#[tauri::command]
pub async fn 检查9patch文件(input_path: &str) -> Result<bool, String> {
    // 遍历目录查找.9.png文件并检查nptc块
    let mut needs_compilation = false;

    for entry in WalkDir::new(input_path)
        .into_iter()
        .filter_map(|e| e.ok())
        .filter(|e| {
            e.path()
                .to_str()
                .map(|s| s.ends_with(".9.png"))
                .unwrap_or(false)
        })
    {
        // 读取文件内容
        match std::fs::read(entry.path()) {
            Ok(buffer) => {
                // 如果找到一个没有nptc块的.9.png文件,就需要编译
                if !has_nptc_chunk(&buffer) {
                    needs_compilation = true;
                    break;
                }
            }
            Err(e) => {
                println!("读取文件失败 {}: {}", entry.path().display(), e);
                continue;
            }
        }
    }

    Ok(needs_compilation)
}

// 检查文件是否包含nptc块
fn has_nptc_chunk(buffer: &[u8]) -> bool {
    if buffer.len() < 4 {
        return false;
    }
    buffer.windows(4).any(|window| window == b"npTc")
}

#[tauri::command]
pub fn 获取用户名称() -> Result<String, String> {
    // 尝试获取当前用户名
    match whoami::username() {
        username => Ok(username),
    }
}

#[tauri::command]
pub fn 超大文件检测(source_path: String) -> Result<String, String> {
    use rayon::prelude::*;
    use std::fs::File;
    use std::io::Write;
    use std::path::Path;
    use std::sync::{Arc, Mutex};
    use walkdir::WalkDir;

    // 创建一个线程安全的 Vec 来存储超大文件的路径
    let large_files = Arc::new(Mutex::new(Vec::new()));

    // 收集所有文件路径
    let entries: Vec<_> = WalkDir::new(&source_path)
        .into_iter()
        .filter_map(|e| e.ok())
        .filter(|e| e.file_type().is_file())
        .collect();

    // 并行处理所有文件
    entries.par_iter().for_each(|entry| {
        if let Ok(metadata) = entry.metadata() {
            let file_size = metadata.len();
            if file_size > 1024 * 1024 {
                // 如果文件大于1M，将其路径添加到结果中
                let mut large_files = large_files.lock().unwrap();
                large_files.push(format!(
                    "文件: {} - 大小: {:.2}MB",
                    entry.path().display(),
                    file_size as f64 / (1024.0 * 1024.0)
                ));
            }
        }
    });

    // 获取结果
    let large_files = large_files.lock().unwrap();

    if large_files.is_empty() {
        return Ok("".to_string());
    }

    // 构建输出文件路径
    let output_path = Path::new(&source_path).join("超大文件列表.txt");

    // 写入结果到文件
    let mut file = File::create(&output_path).map_err(|e| format!("创建结果文件失败: {}", e))?;

    writeln!(
        file,
        "声明：使用小编辑器打包主题，该文件不会被打包进mtz中。\n\n发现以下超过1M的文件，请注意小尺寸文件超大可能导致app崩溃:"
    )
    .unwrap();
    for file_info in large_files.iter() {
        writeln!(file, "{}", file_info).unwrap();
    }

    Ok(output_path.to_string_lossy().into_owned())
}

#[tauri::command]
pub fn open_devtools(window: tauri::Window) {
    if let Some(webview) = window.get_webview_window("main") {
        let _ = webview.open_devtools();
    }
}

#[tauri::command]
pub async fn 打包文件(
    app_handle: tauri::AppHandle,
    paths: Vec<String>,
) -> Result<String, String> {
    use rayon::prelude::*;
    use std::fs::File;
    use std::io::{BufReader, BufWriter};
    use std::path::PathBuf;
    use walkdir::WalkDir;
    use zip::write::FileOptions;
    use zip::ZipWriter;

    // 使用线程安全的集合存储文件和目录
    let all_files = std::sync::Mutex::new(Vec::new());
    let all_dirs = std::sync::Mutex::new(Vec::new());

    // 获取第一个路径作为基准路径
    let base_dir = PathBuf::from(paths.first().ok_or("路径列表为空")?)
        .parent()
        .unwrap()
        .to_path_buf();

    // 并行处理所有输入路径
    paths.par_iter().for_each(|path| {
        let path = PathBuf::from(path);
        if !path.exists() {
            return;
        }

        let mut files = all_files.lock().unwrap();
        let mut dirs = all_dirs.lock().unwrap();

        if path.is_file() {
            files.push((
                path.clone(),
                path.strip_prefix(&base_dir).unwrap().to_path_buf(),
            ));
        } else if path.is_dir() {
            // 记录目录本身
            if let Ok(rel_path) = path.strip_prefix(&base_dir) {
                dirs.push(rel_path.to_path_buf());
            }

            for entry in WalkDir::new(&path).into_iter().filter_map(|e| e.ok()) {
                let full_path = entry.path().to_path_buf();
                if let Ok(relative_path) = full_path.strip_prefix(&base_dir) {
                    if entry.path().is_file() {
                        files.push((full_path.clone(), relative_path.to_path_buf()));
                    } else if entry.path().is_dir() {
                        dirs.push(relative_path.to_path_buf());
                    }
                }
            }
        }
    });

    // 获取收集到的所有文件和目录
    let files = all_files.lock().unwrap().clone();
    let dirs = all_dirs.lock().unwrap().clone();

    // 如果没有找到任何文件和目录
    if files.is_empty() && dirs.is_empty() {
        return Err("未找到任何有效文件或目录".to_string());
    }

    // 获取缓存目录作为输出目录
    let cache_dir = 获取缓存目录(app_handle).await?;
    let output_path = PathBuf::from(cache_dir).join("alsdjlkasjlpacked.zip");

    // 创建zip文件
    let file = File::create(&output_path).map_err(|e| format!("创建压缩文件失败: {}", e))?;
    let mut zip = ZipWriter::new(BufWriter::new(file));

    // 设置压缩选项 - 不压缩
    let options = FileOptions::default().compression_method(zip::CompressionMethod::Stored);

    // 先创建所有目录
    for dir_path in dirs {
        let dir_path = dir_path.to_string_lossy().to_string();
        if !dir_path.is_empty() {
            let dir_path = if dir_path.ends_with('/') {
                dir_path
            } else {
                format!("{}/", dir_path)
            };
            zip.add_directory(dir_path, options)
                .map_err(|e| format!("添加目录失败: {}", e))?;
        }
    }

    // 写入所有文件
    for (full_path, relative_path) in files {
        // 开始写入文件
        zip.start_file(relative_path.to_string_lossy().to_string(), options)
            .map_err(|e| format!("写入文件失败: {}", e))?;

        // 使用缓冲读取加快IO
        let file = File::open(&full_path).map_err(|e| format!("打开文件失败: {}", e))?;
        let mut reader = BufReader::with_capacity(1024 * 1024, file); // 1MB buffer
        std::io::copy(&mut reader, &mut zip).map_err(|e| format!("复制文件内容失败: {}", e))?;
    }

    // 完成打包
    zip.finish().map_err(|e| format!("完成压缩失败: {}", e))?;

    Ok(output_path.to_string_lossy().to_string())
}

// 获取配置文件路径
#[tauri::command]
pub fn 获取配置文件路径(
    app_handle: tauri::AppHandle,
    file_name: String,
) -> Result<String, String> {
    use std::time::Instant;
    let start_time = Instant::now();

    // 首先尝试使用 current_dir 获取配置文件（适用于开发环境）
    let current_dir = std::env::current_dir().unwrap_or_default();

    let dev_config_path = current_dir.join("config").join(&file_name);

    // 如果开发环境的路径存在，直接返回
    if dev_config_path.exists() {
        return match dev_config_path.to_str() {
            Some(path_str) => Ok(path_str.to_string()),
            None => Err("路径包含无效的Unicode字符".to_string()),
        };
    }

    // 尝试获取打包后的资源目录路径
    let resource_dir = app_handle.path().resource_dir().unwrap_or_default();

    let resource_config_path = resource_dir.join("config").join(&file_name);

    // 用于读取的资源目录配置文件路径
    if resource_config_path.exists() {
        // 检查文件是否可写
        match fs::metadata(&resource_config_path) {
            Ok(_) => {
                // 尝试创建临时文件测试是否可写
                let temp_path = resource_config_path.with_file_name("temp_write_test");
                let write_result = fs::write(&temp_path, b"test");

                // 如果成功写入，则删除临时文件并返回资源路径
                if write_result.is_ok() {
                    match fs::remove_file(&temp_path) {
                        Ok(_) => (),
                        Err(_e) => (),
                    };
                    return match resource_config_path.to_str() {
                        Some(path_str) => {
                            let result = path_str.to_string();
                            Ok(result)
                        }
                        None => Err("路径包含无效的Unicode字符".to_string()),
                    };
                }
                // 否则，继续尝试用户目录
            }
            Err(_e) => {
                // 元数据获取失败，继续尝试用户目录
            }
        }
    }

    // 尝试使用AppData目录作为可写入的配置文件位置
    let app_data_dir = app_handle.path().app_data_dir().unwrap_or_default();

    let user_config_path = app_data_dir.join("config").join(&file_name);

    // 如果用户配置目录不存在，尝试从资源目录复制
    if !user_config_path.exists() {
        if let Some(parent) = user_config_path.parent() {
            // 确保配置目录存在
            if !parent.exists() {
                if let Err(e) = fs::create_dir_all(parent) {
                    log::error!("无法创建用户配置目录: {}", e);
                } else {
                    log::debug!("成功创建用户配置目录: {}", parent.display());
                }
            }
        }

        // 如果资源目录中有该文件，复制到用户目录
        if resource_config_path.exists() {
            log::info!(
                "将从资源目录复制配置文件到用户目录: {} -> {}",
                resource_config_path.display(),
                user_config_path.display()
            );
            if let Err(e) = fs::copy(&resource_config_path, &user_config_path) {
                log::error!("复制配置文件到用户目录失败: {}", e);
                // 如果复制失败，但资源目录文件存在，返回资源目录路径(只读)
                return match resource_config_path.to_str() {
                    Some(path_str) => {
                        let result = path_str.to_string();
                        log::info!(
                            "复制失败，返回只读的资源目录配置路径，耗时: {:?}",
                            start_time.elapsed()
                        );
                        Ok(result)
                    }
                    None => {
                        log::error!("资源目录路径包含无效的Unicode字符");
                        Err("路径包含无效的Unicode字符".to_string())
                    }
                };
            } else {
                log::info!(
                    "成功将配置文件复制到用户目录: {}",
                    user_config_path.display()
                );
            }
        } else {
            // 如果资源目录也没有，尝试创建一个空的配置文件
            log::info!("资源目录中也不存在配置文件，将创建默认配置文件");
            if let Some(parent) = user_config_path.parent() {
                if !parent.exists() {
                    if let Err(e) = fs::create_dir_all(parent) {
                        log::error!("无法创建用户配置目录: {}", e);
                    } else {
                        log::debug!("成功创建用户配置目录: {}", parent.display());
                    }
                }
            }

            // 创建一个默认的空配置文件
            if let Err(e) = fs::write(&user_config_path, "{}") {
                log::error!("创建默认配置文件失败: {}", e);
            } else {
                log::info!("成功创建默认配置文件: {}", user_config_path.display());
            }
        }
    }

    // 返回用户目录中的配置文件路径
    if user_config_path.exists() {
        log::info!("使用用户目录配置文件: {}", user_config_path.display());
        match user_config_path.to_str() {
            Some(path_str) => {
                let result = path_str.to_string();
                log::info!("成功返回用户目录配置路径，耗时: {:?}", start_time.elapsed());
                Ok(result)
            }
            None => {
                log::error!("用户目录路径包含无效的Unicode字符");
                Err("路径包含无效的Unicode字符".to_string())
            }
        }
    } else {
        // 如果都找不到，返回错误
        let error_msg = format!(
            "配置文件不存在。\n开发路径: {}\n资源路径: {}\n用户路径: {}",
            dev_config_path.display(),
            resource_config_path.display(),
            user_config_path.display()
        );
        log::error!("无法找到任何可用的配置文件: {}", error_msg);
        log::info!("获取配置文件路径失败，耗时: {:?}", start_time.elapsed());
        Err(error_msg)
    }
}

// 获取配置目录路径
#[tauri::command]
pub fn 获取配置目录(app_handle: tauri::AppHandle) -> Result<String, String> {
    // 首先尝试使用 current_dir 获取配置目录（适用于开发环境）
    let current_dir = std::env::current_dir().unwrap_or_default();
    let dev_config_dir = current_dir.join("config");

    // 如果开发环境的配置目录存在，直接返回
    if dev_config_dir.exists() && dev_config_dir.is_dir() {
        return match dev_config_dir.to_str() {
            Some(path_str) => Ok(path_str.to_string()),
            None => Err("路径包含无效的Unicode字符".to_string()),
        };
    }

    // 尝试获取打包后的资源目录路径
    let resource_dir = app_handle.path().resource_dir().unwrap_or_default();
    let resource_config_dir = resource_dir.join("config");

    // 检查资源目录中的配置目录
    if resource_config_dir.exists() && resource_config_dir.is_dir() {
        return match resource_config_dir.to_str() {
            Some(path_str) => Ok(path_str.to_string()),
            None => Err("路径包含无效的Unicode字符".to_string()),
        };
    }

    // 尝试使用AppData目录作为配置目录位置
    let app_data_dir = app_handle.path().app_data_dir().unwrap_or_default();
    let user_config_dir = app_data_dir.join("config");

    // 如果用户配置目录不存在，尝试创建
    if !user_config_dir.exists() {
        if let Err(e) = fs::create_dir_all(&user_config_dir) {
            log::error!("无法创建用户配置目录: {}", e);
            return Err(format!("无法创建配置目录: {}", e));
        }
        log::info!("成功创建用户配置目录: {}", user_config_dir.display());
    }

    // 返回用户配置目录路径
    match user_config_dir.to_str() {
        Some(path_str) => {
            log::info!("使用用户配置目录: {}", user_config_dir.display());
            Ok(path_str.to_string())
        }
        None => {
            log::error!("用户配置目录路径包含无效的Unicode字符");
            Err("路径包含无效的Unicode字符".to_string())
        }
    }
}

#[tauri::command]
pub async fn find_tool_path(tool_name: String) -> Result<String, String> {
    match which(&tool_name) {
        Ok(path) => Ok(path.to_string_lossy().into_owned()),
        Err(_) => Err(format!("未找到工具: {}", tool_name)),
    }
}

/// 创建临时目录
///
/// # 参数
/// * `prefix` - 临时目录名称前缀
///
/// # 返回值
/// * `Result<String, String>` - 成功返回临时目录路径，失败返回错误信息
#[tauri::command]
pub async fn 创建临时目录(
    app_handle: tauri::AppHandle,
    prefix: String,
) -> Result<String, String> {
    let app_data_dir = app_handle
        .path()
        .app_local_data_dir()
        .map_err(|e| format!("无法获取应用数据目录: {}", e))?;

    let temp_base_dir = app_data_dir.join("temp");

    // 确保temp基础目录存在
    fs::create_dir_all(&temp_base_dir).map_err(|e| format!("创建temp基础目录失败: {}", e))?;

    // 使用时间戳和随机数生成唯一的临时目录名
    let timestamp = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs();
    let random_id = rand::random::<u32>();
    let temp_dir_name = format!("{}_{}_{}", prefix, timestamp, random_id);

    let temp_dir = temp_base_dir.join(temp_dir_name);

    // 创建临时目录
    fs::create_dir_all(&temp_dir).map_err(|e| format!("创建临时目录失败: {}", e))?;

    Ok(temp_dir.to_string_lossy().into_owned())
}

/// 删除目录及其所有内容
///
/// # 参数
/// * `path` - 要删除的目录路径
///
/// # 返回值
/// * `Result<(), String>` - 成功返回 Ok(())，失败返回错误信息
#[tauri::command]
pub async fn 删除目录文件夹(path: String) -> Result<(), String> {
    let dir_path = Path::new(&path);

    if !dir_path.exists() {
        return Ok(()); // 目录不存在，视为成功
    }

    if !dir_path.is_dir() {
        return Err(format!("路径不是目录: {}", path));
    }

    // 删除目录及其所有内容
    fs::remove_dir_all(&path).map_err(|e| format!("删除目录失败: {}", e))?;

    println!("成功删除目录: {}", path);
    Ok(())
}

/// 复制目录
///
/// # 参数
/// * `from` - 源目录路径
/// * `to` - 目标目录路径
///
/// # 返回值
/// * `Result<(), String>` - 成功返回 Ok(())，失败返回错误信息
#[tauri::command]
pub async fn 复制目录(from: String, to: String) -> Result<(), String> {
    复制文件(from, to).await
}
