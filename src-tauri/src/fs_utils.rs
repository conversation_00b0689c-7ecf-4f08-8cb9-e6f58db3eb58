use futures::future::join_all;
use log::{debug, error};
use std::fs;
use std::path::Path;
use std::sync::Arc;
use tokio::task;

#[tauri::command]
pub async fn 删除目录(dir_path: String) -> Result<String, String> {
    let path = Path::new(&dir_path);

    if !path.exists() {
        return Ok("目录不存在，无需删除".to_string());
    }

    let errors = Arc::new(tokio::sync::Mutex::new(Vec::new()));
    let mut tasks = Vec::new();

    // 读取目录内容
    let entries = match fs::read_dir(path) {
        Ok(entries) => entries,
        Err(e) => {
            error!("无法读取目录: {}", e);
            return Err(format!("无法读取目录: {}", e));
        }
    };

    // 遍历目录项并创建异步任务
    for entry in entries {
        if let Ok(entry) = entry {
            let path = entry.path();
            let errors = Arc::clone(&errors);

            let task = task::spawn(async move {
                if path.is_file() {
                    if let Err(e) = fs::remove_file(&path) {
                        error!("删除文件失败: {:?}, 错误: {}", path, e);
                        let mut errors = errors.lock().await;
                        errors.push(format!("删除文件 {:?} 失败: {}", path, e));
                    } else {
                        debug!("成功删除文件: {:?}", path);
                    }
                } else if path.is_dir() {
                    if let Err(e) = fs::remove_dir_all(&path) {
                        error!("删除目录失败: {:?}, 错误: {}", path, e);
                        let mut errors = errors.lock().await;
                        errors.push(format!("删除目录 {:?} 失败: {}", path, e));
                    } else {
                        debug!("成功删除目录: {:?}", path);
                    }
                }
            });

            tasks.push(task);
        }
    }

    // 等待所有任务完成
    join_all(tasks).await;

    // 检查是否有错误
    let errors = errors.lock().await;
    if errors.is_empty() {
        // 最后删除根目录
        if let Err(e) = fs::remove_dir(path) {
            Err(format!("删除根目录失败: {}", e))
        } else {
            Ok(format!("成功删除目录: {}", path.display()))
        }
    } else {
        Err(errors.join("\n"))
    }
}
