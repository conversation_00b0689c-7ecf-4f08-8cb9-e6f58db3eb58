use anyhow::{Context, Result};
use quick_xml::events::{BytesText, Event};
use quick_xml::{Reader, Writer};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs::{self, File};
use std::io::BufReader;
use std::path::Path;
use tauri::{command, Manager};

#[derive(Debug, Serialize, Deserialize)]
pub struct ColorMapping {
    pub name: String,
    pub color: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ColorsConfig {
    #[serde(flatten)]
    pub packages: HashMap<String, HashMap<String, Vec<String>>>,
}

/// 替换 XML 文件中的颜色值
#[command]
pub async fn replace_xml_colors(
    theme_path: String,
    color_mappings: Vec<ColorMapping>,
    config_data: String,
) -> Result<String, String> {
    tokio::task::spawn_blocking(move || {
        process_xml_colors(&theme_path, &color_mappings, &config_data)
            .map_err(|e| format!("处理XML颜色失败: {}", e))
    })
    .await
    .map_err(|e| format!("任务执行失败: {}", e))?
}

fn process_xml_colors(
    theme_path: &str,
    color_mappings: &[ColorMapping],
    config_data: &str,
) -> Result<String> {
    let theme_path = Path::new(theme_path);

    // 解析配置数据
    let config: ColorsConfig = serde_json::from_str(config_data).context("解析颜色配置失败")?;

    // 创建颜色名称到颜色值的映射
    let color_map: HashMap<String, String> = color_mappings
        .iter()
        .map(|mapping| (mapping.name.clone(), mapping.color.clone()))
        .collect();

    let mut processed_count = 0;
    let mut error_messages = Vec::new();

    // 遍历配置中的每个包
    for (package_name, color_groups) in &config.packages {
        let xml_path = theme_path.join(package_name).join("theme_values.xml");

        if !xml_path.exists() {
            log::warn!("XML文件不存在: {}", xml_path.display());
            continue;
        }

        match process_single_xml(&xml_path, color_groups, &color_map) {
            Ok(modified_count) => {
                if modified_count > 0 {
                    processed_count += modified_count;
                    log::info!("成功处理 {} 中的 {} 个颜色", package_name, modified_count);
                }
            }
            Err(e) => {
                let error_msg = format!("处理 {} 失败: {}", package_name, e);
                log::error!("{}", error_msg);
                error_messages.push(error_msg);
            }
        }
    }

    let result_message = if error_messages.is_empty() {
        format!("成功处理了 {} 个颜色值", processed_count)
    } else {
        format!(
            "处理了 {} 个颜色值，但有 {} 个错误: {}",
            processed_count,
            error_messages.len(),
            error_messages.join("; ")
        )
    };

    Ok(result_message)
}

fn process_single_xml(
    xml_path: &Path,
    color_groups: &HashMap<String, Vec<String>>,
    color_map: &HashMap<String, String>,
) -> Result<usize> {
    let file =
        File::open(xml_path).with_context(|| format!("无法打开XML文件: {}", xml_path.display()))?;

    let mut reader = Reader::from_reader(BufReader::new(file));
    reader.config_mut().trim_text(true);

    let mut writer = Writer::new(Vec::new());
    let mut buf = Vec::new();
    let mut modified_count = 0;
    let mut in_color_tag = false;
    let mut current_color_name = String::new();

    loop {
        match reader.read_event_into(&mut buf) {
            Ok(Event::Start(ref e)) if e.name().as_ref() == b"color" => {
                in_color_tag = true;
                current_color_name.clear();

                // 获取 name 属性
                for attr in e.attributes() {
                    if let Ok(attr) = attr {
                        if attr.key.as_ref() == b"name" {
                            current_color_name = String::from_utf8_lossy(&attr.value).to_string();
                            break;
                        }
                    }
                }

                writer.write_event(Event::Start(e.clone()))?;
            }
            Ok(Event::Text(ref e)) if in_color_tag => {
                let original_color = String::from_utf8_lossy(e);
                let new_color = find_replacement_color(
                    &current_color_name,
                    color_groups,
                    color_map,
                    &original_color,
                );

                if new_color != original_color {
                    modified_count += 1;
                    log::debug!(
                        "替换颜色: {} {} -> {}",
                        current_color_name,
                        original_color,
                        new_color
                    );
                }

                writer.write_event(Event::Text(BytesText::new(&new_color)))?;
            }
            Ok(Event::End(ref e)) if e.name().as_ref() == b"color" => {
                in_color_tag = false;
                current_color_name.clear();
                writer.write_event(Event::End(e.clone()))?;
            }
            Ok(Event::Eof) => break,
            Ok(e) => writer.write_event(e)?,
            Err(e) => {
                log::error!("解析XML错误: {}", e);
                continue;
            }
        }
        buf.clear();
    }

    // 写入修改后的内容
    let output = writer.into_inner();
    fs::write(xml_path, output)
        .with_context(|| format!("无法写入XML文件: {}", xml_path.display()))?;

    Ok(modified_count)
}

fn find_replacement_color(
    color_name: &str,
    color_groups: &HashMap<String, Vec<String>>,
    color_map: &HashMap<String, String>,
    original_color: &str,
) -> String {
    // 查找该颜色名称属于哪个颜色组
    for (group_name, color_names) in color_groups {
        if color_names.contains(&color_name.to_string()) {
            if let Some(new_rgb) = color_map.get(group_name) {
                // 保留原始颜色的 alpha 通道，替换 RGB 部分
                return replace_rgb_keep_alpha(original_color, new_rgb);
            }
        }
    }

    // 如果没有找到匹配的颜色组，返回原始颜色
    original_color.to_string()
}

fn replace_rgb_keep_alpha(original_color: &str, new_rgb: &str) -> String {
    let original = original_color.trim();
    let new_rgb = new_rgb.trim();

    // 确保颜色格式正确
    if !original.starts_with('#') || original.len() != 9 {
        log::warn!("原始颜色格式不正确: {}", original);
        return original.to_string();
    }

    if !new_rgb.starts_with('#') || new_rgb.len() != 7 {
        log::warn!("新RGB颜色格式不正确: {}", new_rgb);
        return original.to_string();
    }

    // 提取 alpha 通道（前两位）和新的 RGB 值（后六位）
    let alpha = &original[1..3];
    let rgb = &new_rgb[1..];

    format!("#{}{}", alpha, rgb)
}

/// 获取主题的 UI 版本
#[command]
pub async fn get_theme_ui_version(theme_path: String) -> Result<u32, String> {
    tokio::task::spawn_blocking(move || {
        extract_theme_ui_version(&theme_path).map_err(|e| format!("获取主题版本失败: {}", e))
    })
    .await
    .map_err(|e| format!("任务执行失败: {}", e))?
}

fn extract_theme_ui_version(theme_path: &str) -> Result<u32> {
    let description_path = Path::new(theme_path).join("description.xml");

    if !description_path.exists() {
        return Err(anyhow::anyhow!("未找到 description.xml 文件"));
    }

    let content = fs::read_to_string(&description_path).context("无法读取 description.xml 文件")?;

    extract_ui_version_from_xml(&content)
        .ok_or_else(|| anyhow::anyhow!("未找到 uiVersion 标签或解析失败"))
}

/// 从 XML 内容中提取 uiVersion 标签值
fn extract_ui_version_from_xml(xml_content: &str) -> Option<u32> {
    let ui_version_start = xml_content.find("<uiVersion>")?;
    let ui_version_end = xml_content.find("</uiVersion>")?;

    if ui_version_start < ui_version_end {
        let start_pos = ui_version_start + 11; // "<uiVersion>".len()
        let version_str = &xml_content[start_pos..ui_version_end];
        version_str.trim().parse::<u32>().ok()
    } else {
        None
    }
}

/// 获取资源文件路径
#[command]
pub async fn get_resource_file_path(
    app_handle: tauri::AppHandle,
    relative_path: String,
) -> Result<String, String> {
    tokio::task::spawn_blocking(move || {
        // 使用 Tauri API 解析打包的资源路径
        match app_handle
            .path()
            .resolve(&relative_path, tauri::path::BaseDirectory::Resource)
        {
            Ok(path) => Ok(path.to_string_lossy().to_string()),
            Err(e) => Err(format!("无法解析资源路径 '{}': {}", relative_path, e)),
        }
    })
    .await
    .map_err(|e| format!("任务执行失败: {}", e))?
}
