// use rayon::prelude::*;
use serde::Serialize;
use std::collections::HashMap;
use std::fs::File;
use std::path::{Path, PathBuf};
use std::sync::{
    atomic::{AtomicUsize, Ordering},
    Arc,
};
use std::time::Instant;
use tauri::Emitter;
use tokio::sync::{Mutex, Semaphore};
use tokio::task;
use walkdir::WalkDir;

use memmap2::Mmap;
use serde_json::json;
use std::collections::BTreeMap;
use tauri_plugin_shellx::process::CommandEvent;
use tauri_plugin_shellx::ShellExt;

lazy_static::lazy_static! {
    // 减少并发数，避免资源争用
    static ref CPU_CORES: usize = std::cmp::max(1, num_cpus::get());
}

#[derive(Serialize, Clone)]
struct ProgressEvent {
    processed: usize,
    total: usize,
    progress: f64,
}

#[derive(Serialize)]
struct ErrorInfo {
    file: String,
    reason: String,
}

#[tauri::command]
pub async fn 预编译(
    app_handle: tauri::AppHandle,
    input_path: String,
) -> Result<HashMap<PathBuf, Vec<u8>>, String> {
    let total_start_time = Instant::now();
    let logs = Arc::new(Mutex::new(Vec::<String>::new()));

    {
        let mut logs_guard = logs.lock().await;
        logs_guard.push("开始处理 .9.png 文件...".to_string());
    }

    // 扫描所有 .9.png 文件
    let files = scan_nine_png_files(&input_path)?;
    let total_files = files.len();
    {
        let mut logs_guard = logs.lock().await;
        logs_guard.push(format!("找到 {} 个 .9.png 文件", total_files));
    }

    // 创建共享数据结构：存储结果的Map和错误信息收集向量
    let nine_patches = Arc::new(Mutex::new(HashMap::new()));
    let error_messages = Arc::new(Mutex::new(Vec::new()));

    // 创建并发控制用的Semaphore
    let semaphore = Arc::new(Semaphore::new(*CPU_CORES));
    // 用于进度计数
    let counter = Arc::new(AtomicUsize::new(0));

    // 并发处理任务
    let mut tasks = Vec::with_capacity(total_files);
    for path in files {
        let app_handle = app_handle.clone();
        let semaphore = semaphore.clone();
        let nine_patches = nine_patches.clone();
        let error_messages = error_messages.clone();
        let counter = counter.clone();
        let logs = logs.clone();

        tasks.push(task::spawn(async move {
            let permit = semaphore.acquire_owned().await;
            if permit.is_err() {
                let error_msg = format!("文件 {:?} 获取Semaphore失败", path);
                let mut errs = error_messages.lock().await;
                let mut logs_guard = logs.lock().await;
                errs.push(error_msg.clone());
                logs_guard.push(error_msg);
                return;
            }

            match process_file(&app_handle, &path, &nine_patches).await {
                Ok(_) => {
                    let mut logs_guard = logs.lock().await;
                    logs_guard.push(format!("文件处理完成: {:?}", path));
                }
                Err(e) => {
                    let error_msg = format!("文件 {:?} 处理出错: {}", path, e);
                    let mut errs = error_messages.lock().await;
                    let mut logs_guard = logs.lock().await;
                    errs.push(error_msg.clone());
                    logs_guard.push(error_msg);
                }
            }

            // 更新进度
            let processed = counter.fetch_add(1, Ordering::SeqCst) + 1;
            let progress = processed as f64 / total_files as f64;
            let progress_event = ProgressEvent {
                processed,
                total: total_files,
                progress,
            };
            if let Err(e) = app_handle.emit("progress", progress_event) {
                let error_msg = format!("发送进度事件失败: {}", e);
                let mut errs = error_messages.lock().await;
                let mut logs_guard = logs.lock().await;
                errs.push(error_msg.clone());
                logs_guard.push(error_msg);
            }
        }));
    }

    // 等待所有任务结束
    futures::future::join_all(tasks).await;

    // 获取最终结果
    let result = Arc::try_unwrap(nine_patches)
        .map_err(|_| "无法释放 nine_patches, 请检查Arc引用计数".to_string())?
        .into_inner();

    // 如果有错误信息，则可以组合成一个字符串返回，也可以在Ok中携带警告
    let errors = Arc::try_unwrap(error_messages)
        .map_err(|_| "无法释放 error_messages, 请检查Arc引用计数".to_string())?
        .into_inner();

    // 获取最终日志并按类型分组
    {
        let logs_guard = logs.lock().await;
        let mut grouped_logs: BTreeMap<&str, Vec<String>> = BTreeMap::new();

        // 对日志进行分类
        for log in logs_guard.iter() {
            if log.starts_with("开始处理文件:") {
                grouped_logs
                    .entry("文件处理")
                    .or_default()
                    .push(log.clone());
            } else if log.starts_with("成功编译并保存文件:") {
                grouped_logs
                    .entry("编译成功")
                    .or_default()
                    .push(log.clone());
            } else if log.contains("错误") || log.contains("失败") {
                grouped_logs
                    .entry("错误信息")
                    .or_default()
                    .push(log.clone());
            } else {
                grouped_logs
                    .entry("其他信息")
                    .or_default()
                    .push(log.clone());
            }
        }

        // 最后统一打印所有日志
        println!("=== 处理任务完成，日志汇总 ===");
        println!("总耗时: {:?}", total_start_time.elapsed());

        for (log_type, logs) in grouped_logs {
            match log_type {
                "文件处理" => {
                    println!("共处理文件数: {}", logs.len());
                }
                "编译成功" => {
                    println!("成功编译文件数: {}", logs.len());
                }
                "错误信息" => {
                    if !logs.is_empty() {
                        println!("遇到的错误 ({} 条):\n{}", logs.len(), logs.join("\n"));
                    }
                }
                "其他信息" => {
                    println!("其他信息:\n{}", logs.join("\n"));
                }
                _ => {}
            }
        }
        println!("=== 日志汇总结束 ===");
    }

    if !errors.is_empty() {
        // 处理每个错误消息，提取简化的文件路径
        let error_objects: Vec<ErrorInfo> = errors
            .iter()
            .map(|error_msg| {
                // 提取文件路径
                let file_path = if let Some(quote_start) = error_msg.find("\"") {
                    if let Some(quote_end) = error_msg[quote_start + 1..].find("\"") {
                        let full_path = &error_msg[quote_start + 1..quote_start + 1 + quote_end];

                        // 获取更通用的相对路径：从cache目录开始，或者只保留最后几级目录
                        if let Some(cache_index) = full_path.find("cache") {
                            // 找到cache后的第一个斜杠
                            if let Some(after_cache) = full_path[cache_index..].find('/') {
                                // 从cache目录后的第一个斜杠开始，保留后面的相对路径
                                full_path[cache_index + after_cache + 1..].to_string()
                            } else {
                                // 如果找不到斜杠，则使用整个文件名
                                if let Some(last_slash) = full_path.rfind('/') {
                                    full_path[last_slash + 1..].to_string()
                                } else {
                                    full_path.to_string()
                                }
                            }
                        } else {
                            // 如果没有cache关键字，则保留最后2-3级目录
                            let parts: Vec<&str> = full_path.split('/').collect();
                            if parts.len() > 2 {
                                parts[parts.len() - 3..].join("/")
                            } else {
                                // 文件名过短则显示完整路径
                                full_path.to_string()
                            }
                        }
                    } else {
                        "未知文件".to_string()
                    }
                } else {
                    "未知文件".to_string()
                }
                .to_string();

                // 创建错误对象
                ErrorInfo {
                    file: file_path,
                    reason: "编译失败".to_string(),
                }
            })
            .collect();

        // 构建JSON响应
        let error_json = json!({
            "timestamp": chrono::Local::now().to_rfc3339(),
            "input_path": input_path,
            "total_errors": error_objects.len(),
            "errors": error_objects
        });

        // 返回JSON字符串
        return Err(serde_json::to_string(&error_json)
            .unwrap_or_else(|_| format!("处理完成，但部分文件出错，无法序列化错误信息")));
    }

    Ok(result)
}

/// 扫描 .9.png 文件
fn scan_nine_png_files(input_path: &str) -> Result<Vec<PathBuf>, String> {
    // 如果路径不存在或者为空，可以在此进行校验
    let files: Vec<_> = WalkDir::new(input_path)
        .into_iter()
        .filter_map(|entry| match entry {
            Ok(e) => {
                if e.file_type().is_file() && e.path().to_string_lossy().contains(".9.png") {
                    Some(e.path().to_path_buf())
                } else {
                    None
                }
            }
            Err(_) => None,
        })
        .collect();

    Ok(files)
}

/// 处理单个文件：执行编译并写入结果Map
async fn process_file(
    app_handle: &tauri::AppHandle,
    path: &Path,
    nine_patches_map: &Arc<Mutex<HashMap<PathBuf, Vec<u8>>>>,
) -> Result<(), String> {
    let file_path = path.to_string_lossy().to_string();

    // 先检查文件是否已经包含nptc块
    let file_content = match tokio::fs::read(path).await {
        Ok(content) => content,
        Err(e) => return Err(format!("读取文件失败: {}, 错误: {}", file_path, e)),
    };

    // 如果文件已经包含nptc块，则不需要编译
    if has_nptc_chunk(&file_content) {
        // 直接使用现有内容
        let mut map_guard = nine_patches_map.lock().await;
        map_guard.insert(path.to_path_buf(), file_content);
        return Ok(());
    }

    // 直接执行编译流程
    执行aapt编译(app_handle, &file_path).await?;
    let compiled_content = mmap_file(path)?;
    let mut map_guard = nine_patches_map.lock().await;
    map_guard.insert(path.to_path_buf(), compiled_content);
    Ok(())
}

// 检查文件是否包含nptc块
fn has_nptc_chunk(buffer: &[u8]) -> bool {
    if buffer.len() < 4 {
        return false;
    }
    buffer.windows(4).any(|window| window == b"npTc")
}

/// 内存映射文件
fn mmap_file(path: &Path) -> Result<Vec<u8>, String> {
    let file =
        File::open(path).map_err(|e| format!("无法打开文件: {}, err: {}", path.display(), e))?;
    let mmap = unsafe { Mmap::map(&file).map_err(|e| format!("内存映射失败: {}", e))? };
    Ok(mmap.to_vec())
}

/// 执行aapt编译
async fn 执行aapt编译(app_handle: &tauri::AppHandle, file_path: &str) -> Result<(), String> {
    // 最大重试次数
    const MAX_RETRIES: usize = 3;
    let mut retry_count = 0;
    let mut last_error = String::new();

    while retry_count < MAX_RETRIES {
        if retry_count > 0 {
            // 如果是重试，则等待一小段时间
            tokio::time::sleep(tokio::time::Duration::from_millis(500 * retry_count as u64)).await;
        }

        let sidecar_command = match app_handle.shell().sidecar("aapt") {
            Ok(cmd) => cmd,
            Err(e) => {
                last_error = format!("获取 aapt 失败: {}", e);
                retry_count += 1;
                continue;
            }
        };

        // 创建临时输出文件路径
        let temp_output_path = format!("{}.temp", file_path);

        // 设置命令参数，使用临时文件作为输出
        let command = sidecar_command.args(&["s", "-i", file_path, "-o", &temp_output_path]);

        // 执行命令并获取输出流
        let (mut rx, _child) = match command.spawn() {
            Ok(result) => result,
            Err(e) => {
                last_error = format!("启动 aapt 失败: {}", e);
                retry_count += 1;
                continue;
            }
        };

        // 处理命令输出
        let mut success = false;

        while let Some(event) = rx.recv().await {
            if let CommandEvent::Terminated(status) = event {
                match status.code {
                    Some(0) => {
                        // 编译成功，将临时文件移动到原始文件
                        match tokio::fs::rename(&temp_output_path, file_path).await {
                            Ok(_) => {
                                success = true;
                                break;
                            }
                            Err(_e) => {
                                // 如果重命名失败，尝试复制文件内容然后删除临时文件
                                match tokio::fs::copy(&temp_output_path, file_path).await {
                                    Ok(_) => {
                                        // 删除临时文件（忽略删除失败的错误）
                                        let _ = tokio::fs::remove_file(&temp_output_path).await;
                                        success = true;
                                        break;
                                    }
                                    Err(e2) => {
                                        last_error = format!(
                                            "复制编译后的文件失败: {}, 原因: {}",
                                            temp_output_path, e2
                                        );
                                        // 删除临时文件
                                        let _ = tokio::fs::remove_file(&temp_output_path).await;
                                    }
                                }
                            }
                        }
                    }
                    _ => {
                        // 删除临时文件（如果存在）
                        let _ = tokio::fs::remove_file(&temp_output_path).await;
                        last_error = format!("文件 {} 编译失败", file_path);
                    }
                }
            }
        }

        // 如果成功，直接返回
        if success {
            return Ok(());
        }

        // 否则尝试重试
        retry_count += 1;
    }

    // 所有重试都失败
    Err(format!(
        "编译失败 (已重试 {} 次): {}",
        MAX_RETRIES, last_error
    ))
}
