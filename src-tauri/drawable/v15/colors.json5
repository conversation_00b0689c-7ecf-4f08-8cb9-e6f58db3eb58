{
    "com.android.contacts": {
        "高亮色蓝": [
            "bh_txt_blue",
            "contacts_accent_color",
            "miuix_color_blue_light_primary_default",
            "miuix_appcompat_dropdown_popup_list_text_color_checked_light",
            "miuix_appcompat_checked_text_light",
            "miuix_preference_card_group_background_color_light",
            "miuix_color_blue_light_level1",
            "miuix_appcompat_search_action_mode_cancel_text_color_normal_light",
            "miuix_appcompat_edit_text_border_color_light",
            "miuix_color_blue_light_primary_hover",
            "miuix_color_blue_light_primary_pressed",
            "miuix_color_blue_dark_secondary_default",
            "miuix_color_blue_light_secondary_hover",
            "miuix_color_blue_light_secondary_pressed",
            "miuix_appcompat_cloud_count_normal_text_color_light",
            "miuix_appcompat_cloud_arrow_color_light",
            "bh_color_main_text_blue",
            "bh_color_charge_more_list_item_title_color",
            "bh_color_no_card_recommend_item_btn_txt",
            "bh_color_main_text_blue_disable",
            "bh_color_bg_waterbox",
            "bh_orange",
            "bh_red_tag",
            "bh_red_5500",
            "high_light_text",
            "miuix_color_green_light_primary_default",
            "t9_guide_text_color",
            "t9_list_item_text_highlight",
            "action_button_positive_color",
            "bh_orange_start",
            "bh_tag_blue",
            "editor_scan_businesscard_btn_txt_color",
            "miuix_appcompat_link_text_light",
            "miuix_color_blue_light_primary_disable",
            "miuix_color_blue_light_secondary_disable",
            "miuix_appcompat_spinner_item_checked_bg_color_light",
            "bh_color_button_blue_tint",
            "bh_color_bg_block_orange_light",
            "bh_color_charge_list_item_bg",
            "bh_black_50_daynight_dialog_bg_fixed",
            "miuix_appcompat_dropdown_popup_list_item_bg_checked_light",
            "dropdown_popup_list_item_bg_checked_v12",
            "miuix_appcompat_list_item_bg_color_checked_light",
            "miuix_appcompat_cloud_state_message_bg_color_normal_light",
            "contact_list_item_bg_p",
            "contact_list_item_bg_s",
            "editor_scan_bussiness_card_bg_n",
            "bh_color_main_pkg_goto",
            "miuix_color_blue_light_secondary_default"
        ],
    },
    "com.android.mms": {
        "高亮色蓝": [
            "miuix_appcompat_sliding_button_bar_on_light",
            "miuix_appcompat_list_text_color_checked_light",
            "miuix_color_blue_light_primary_default",
            "miuix_appcompat_search_action_mode_cancel_text_color_normal_light",
            "miuix_color_blue_light_level1",
            "miuix_appcompat_checked_text_light",
            "miuix_appcompat_dropdown_popup_list_text_color_checked_light",
            "miuix_appcompat_dialog_default_checkbox_btn_on_background_color_light",
            "miuix_appcompat_edit_text_border_color_light",
            "miuix_appcompat_dialog_default_button_bg_primary_color_normal_light",
            "miuix_color_blue_light_primary_hover",
            "miuix_color_blue_light_primary_pressed",
            "miuix_color_blue_dark_secondary_default",
            "miuix_color_blue_light_secondary_hover",
            "miuix_color_blue_light_secondary_pressed",
            "screen_view_seek_point_highlight",
            "text_color_attachment_edit_phrase",
            "miuix_appcompat_filter_sort_tab_view2_bg_color_selected_light",
            "ic_insert_contact_color",
            "sim_indicator_color_selected",
            "text_color_mi_recipient",
            "miuix_appcompat_cloud_count_normal_text_color_light",
            "miuix_appcompat_cloud_sync_color_light",
            "miuix_appcompat_cloud_arrow_color_light",
            "aim_show_card",
            "security_alert_color",
            "mms_block_alert_color",
            "recommend_msg_color",
            "miuix_color_blue_light_primary_disable",
            "miuix_color_blue_light_secondary_disable",
            "miuix_appcompat_list_item_bg_color_checked_light",
            "miuix_appcompat_cloud_state_message_bg_color_normal_light",
            "miuix_color_blue_light_secondary_default",
            "miuix_preference_card_group_background_color_light",
            "miuix_appcompat_spinner_item_checked_bg_color_light",
            "miuix_appcompat_dropdown_popup_list_item_bg_checked_light"
        ],
        "高亮色黄": [
            "miuix_color_yellow_light_primary_default"
        ],
    },
    "com.android.settings": {
        "高亮色蓝": [
            "usb_dialog_selected_bg_color",
            "miuix_appcompat_edit_text_border_color_light",
            "miuix_appcompat_dialog_default_button_bg_primary_color_normal_light",
            "miuix_appcompat_dropdown_popup_list_text_color_checked_light",
            "miuix_appcompat_dialog_default_checkbox_btn_on_background_color_light",
            "miuix_color_blue_light_level1",
            "miuix_appcompat_search_action_mode_cancel_text_color_normal_light",
            "miuix_appcompat_checked_text_light",
            "miuix_appcompat_list_text_color_checked_light",
            "miuix_appcompat_dialog_default_singleChoice_checked_text_light",
            "miuix_preference_secondary_text_color_checked_light",
            "miuix_appcompat_default_number_picker_highlight_color",
            "miuix_appcompat_progress_primary_color_light",
            "miuix_color_blue_light_primary_default",
            "miuix_color_blue_light_primary_hover",
            "miuix_color_blue_light_primary_pressed",
            "miuix_color_blue_light_secondary_hover",
            "miuix_color_blue_light_secondary_pressed",
            "bt_refresh_color",
            "nearby_wifi_bt_refresh_color",
            "nearby_wifi_refresh_color",
            "bluetooth_disconnect_text_color",
            "choose_unlock_item_bg_selected",
            "choose_unlock_item_bg_selected_pressed",
            "wifi_add_network_title_color",
            "card_view_checked_color",
            "bubble_right_bg_color",
            "font_settings_view_big_color",
            "font_size_seekbar_big_pointer_blue",
            "miuix_appcompat_filter_sort_tab_view_bg_color_light",
            "recommend_item_link",
            "recommend_item_link_new",
            "recommend_item_link_pressed",
            "font_size_view_big_color",
            "new_version_text_color",
            "input_method_function_icon_color_selected",
            "wifi_detail_modify_color",
            "headset_find_device_blue",
            "miuix_appcompat_sliding_button_bar_on_light",
            "dark_color_mode_outer",
            "light_color_mode",
            "search_result_highlight",
            "my_card_bg",
            "notch_style_text_selected",
            "miuix_color_blue_light_primary_disable",
            "miuix_appcompat_progress_disable_color_light",
            "miuix_preference_card_group_background_color_light",
            "miuix_appcompat_spinner_item_checked_bg_color_light",
            "miuix_color_blue_light_secondary_default",
            "miuix_color_blue_light_secondary_disable",
            "restricted_lock_item_select_bg",
            "miuix_appcompat_dialog_default_list_item_bg_dialog_color_checked_light",
            "miuix_preference_checkable_item_two_state_color_pressed_light",
        ],
    },
    "com.android.systemui": {
        "高亮色蓝": [
            "notification_progress_horizontal_progress_color",
            "notification_modal_ok_btn_blend_color",
            "miuix_color_blue_light_level1",
            "control_center_detail_item_selectable_background_color_s",
        ],
    },
    "com.miui.home": {
        "高亮色蓝": [
            "miuix_color_blue_light_primary_default",
            "miuix_color_blue_light_level1"
        ],
    },
    "miui.systemui.plugin": {
        "高亮色蓝": [
            "qs_card_enabled_color",
            "qs_card_cellular_color",
            "qs_enabled_color",
            "qs_warning_color",
            "selector_qs_control_miplay_device_item_bg_radius",
            "qs_detail_enabled_color",
            "miui_volume_color_btn_seleted"
        ]
    },
}