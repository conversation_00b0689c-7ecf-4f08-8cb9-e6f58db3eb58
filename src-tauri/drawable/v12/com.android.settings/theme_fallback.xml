<?xml version='1.0' encoding='utf-8' standalone='yes' ?>
<MIUI_Theme_Values>
    <!-- 【MIUI12】版本icon名 -->
    <drawable name="miui_version_logo_dev.png">miui_version_logo.png</drawable>
    <!-- 设置大背景 -->
    <drawable name="settings_window_bg_light.9.png">window_bg_light.9.png</drawable>
    <drawable name="window_search_mask.9.png">window_bg_light.9.png</drawable>
    <!-- 编辑模式	底栏-更多-展开后的底部背景 -->
    <drawable name="action_bar_split_bg_expanded_light.9.png">action_bar_split_bg_light.9.png</drawable>
    <!-- MIUIX -->
    <!-- 设置大背景 -->
    <drawable name="miuix_appcompat_settings_window_bg_light.9.png" package="miui">window_bg_light.9.png</drawable>
    <drawable name="miuix_appcompat_window_search_mask.9.png" package="miui">window_search_mask.9.png</drawable>
    <!-- 搜索结果顶栏背景 -->
    <drawable name="miuix_appcompat_search_mode_input_bg_light.9.png" package="miui">search_mode_input_bg_light.9.png</drawable>
    <!-- 搜索条背景 （可透明） -->
    <drawable name="miuix_appcompat_search_mode_bg_light.9.png" package="miui">search_mode_bg_light.9.png</drawable>
    <!-- 搜索条 -->
    <drawable name="miuix_appcompat_search_mode_edit_text_bg_light.9.png" package="miui">search_mode_edit_text_bg_light.9.png</drawable>
    <!-- 返回按钮 不生效-->
    <drawable name="miuix_appcompat_action_bar_back_light.png" package="miui">action_bar_back_light.png</drawable>
    <!-- 底部弹窗背景 -->
    <drawable name="miuix_appcompat_dialog_bg_light.9.png" package="miui">dialog_bg_light.9.png</drawable>
    <drawable name="miuix_appcompat_list_menu_bg_light.9.png" package="miui">list_menu_bg_light.9.png</drawable>
    <!-- 蓝牙-保持连接按钮 -->
    <drawable name="btn_bg_normal_selector.9.png">miuix_appcompat_dialog_default_btn_bg_light.9.png</drawable>
    <drawable name="btn_bg_suggest_selector.9.png">miuix_appcompat_dialog_default_btn_bg_primary_light.9.png</drawable>
    <!-- 列表右箭头 不生效-->
    <!-- <drawable name="miuix_appcompat_arrow_right.png" package="miui">arrow_right_normal.png</drawable> -->
    <!-- 密码可见，不可见 不生效 -->
    <!-- <drawable name="miuix_appcompat_ic_visible_light.png" package="miui">ic_visible_light.png</drawable> -->
    <!-- <drawable name="miuix_appcompat_ic_invisible_light.png" package="miui">ic_invisible_light.png</drawable> -->
    <!-- 近手弹窗背景 -->
    <drawable name="miuix_appcompat_immersion_window_bg_light.9.png" package="miui">immersion_window_bg_light.9.png</drawable>
    <!-- 列表选中icon -->
    <!-- <drawable name="miuix_appcompat_btn_radio_arrow_on.png" package="miui">btn_radio_on_light.png</drawable> -->
    <!-- 二级页面-列表右箭头，带圆圈 -->
    <drawable name="miuix_appcompat_btn_inline_detail_light.9.png">btn_inline_detail_normal_light.png</drawable>
    <!-- 输入框 -->
    <drawable name="miuix_appcompat_edit_text_bg_dialog_light.9.png" package="miui">edit_text_bg_dialog_light.9.png</drawable>
    <drawable name="miuix_appcompat_edit_text_bg_light.9.png" package="miui">edit_text_bg_light.9.png</drawable>
    <!-- 文本选中手柄左中右 -->
    <drawable name="miuix_appcompat_text_cursor_light.9.png" package="miui">text_cursor.9.png</drawable>
    <drawable name="miuix_appcompat_text_select_handle_left_light.9.png" package="miui">text_select_handle_left.9.png</drawable>
    <drawable name="miuix_appcompat_text_select_handle_middle_light.9.png" package="miui">text_select_handle_middle.9.png</drawable>
    <drawable name="miuix_appcompat_text_select_handle_right_light.9.png" package="miui">text_select_handle_right.9.png</drawable>
</MIUI_Theme_Values>
