<?xml version='1.0' encoding='utf-8' standalone='yes' ?>
<MIUI_Theme_Values>
    <!-- 开关窗口背景、开关编辑窗口背景 -->
    <drawable name="panel_round_corner_bg.9.png">qs_detail_bg.9.png</drawable>
    <drawable name="qs_customizer_bg.9.png">qs_detail_bg.9.png</drawable>
    <!-- <drawable name="notification_panel_bg.9.png">qs_detail_bg.9.png</drawable> -->

    <!-- 单条通知左滑点击设置后弹窗的背景 -->
    <!-- <drawable name="notification_guts_bg.9.png">notification_item_bg.9.png</drawable> -->

    <!-- 低电量白色 -->
    <drawable name="battery_meter_progress_low_digit.png">
    battery_meter_progress_low.png
    </drawable>
    <!-- 低电量黑色 -->
    <drawable name="battery_meter_progress_low_digit_dark.png">
    battery_meter_progress_low_dark.png
    </drawable>

    <!-- 充电白色 -->
    <drawable name="battery_meter_progress_charging_digit.png">
    battery_meter_progress_charging.png
    </drawable>
    <drawable name="battery_meter_progress_quick_charging_digit.png">
    battery_meter_progress_charging.png
    </drawable>
    <drawable name="battery_meter_progress_quick_charging.png">
    battery_meter_progress_charging.png
    </drawable>
    <!-- 充电黑色 -->
    <drawable name="battery_meter_progress_charging_digit_dark.png">
    battery_meter_progress_charging_dark.png
    </drawable>
    <drawable name="battery_meter_progress_quick_charging_dark.png">
    battery_meter_progress_charging_dark.png
    </drawable>
    <drawable name="battery_meter_progress_quick_charging_digit_dark.png">
    battery_meter_progress_charging_dark.png
    </drawable>

    <!-- 省电白色 -->
    <drawable name="battery_meter_progress_power_save_digit.png">
    battery_meter_progress_power_save.png
    </drawable>
    <!-- 省电黑色 -->
    <drawable name="battery_meter_progress_power_save_digit_dark.png">
    battery_meter_progress_power_save_dark.png
    </drawable>
</MIUI_Theme_Values>
