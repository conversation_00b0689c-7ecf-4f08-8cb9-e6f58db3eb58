// copyHtmlFiles.js
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 要复制的HTML文件列表和对应的JS入口
const filesToCopy = [
    {
        html: 'edit_config.html',
        jsPrefix: 'edit_config_js'
    },
    {
        html: 'search_color.html',
        jsPrefix: 'search_color_js'
    }
];

// 目标目录
const distDir = path.join(__dirname, 'dist');
const distAssetsDir = path.join(distDir, 'assets');

// 输出更多调试信息
console.log('当前运行目录:', __dirname);
console.log('目标dist目录:', distDir);
console.log('目标assets目录:', distAssetsDir);

// 检查目标目录是否存在
console.log('检查dist目录是否存在:', fs.existsSync(distDir));

// 确保目标目录存在
if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
    console.log('创建了dist目录');
}

if (!fs.existsSync(distAssetsDir)) {
    fs.mkdirSync(distAssetsDir, { recursive: true });
    console.log('创建了assets目录');
}

// 获取打包后的CSS文件名
const findCssFilename = () => {
    try {
        const assetsDir = path.join(distDir, 'assets');
        if (!fs.existsSync(assetsDir)) {
            console.error('assets目录不存在:', assetsDir);
            return null;
        }

        const files = fs.readdirSync(assetsDir);
        console.log('assets目录中的文件:', files);

        // 查找CSS文件，优先查找index开头的，如果没有就找任何CSS文件
        let cssFile = files.find(file => file.startsWith('index-') && file.endsWith('.css'));
        if (!cssFile) {
            cssFile = files.find(file => file.endsWith('.css'));
        }

        return cssFile ? `assets/${cssFile}` : null;
    } catch (error) {
        console.error('查找CSS文件失败:', error);
        return null;
    }
};

// 查找与特定模块相关的JS文件
const findJsFilename = (jsPrefix) => {
    try {
        const assetsDir = path.join(distDir, 'assets');
        if (!fs.existsSync(assetsDir)) {
            console.error('assets目录不存在:', assetsDir);
            return null;
        }

        const files = fs.readdirSync(assetsDir);

        // 查找匹配前缀的JS文件
        let jsFile = files.find(file => file.startsWith(`${jsPrefix}-`) && file.endsWith('.js'));

        // 如果找不到特定模块的JS，则回退到通用的index JS
        if (!jsFile) {
            jsFile = files.find(file => file.startsWith('index-') && file.endsWith('.js'));
        }

        // 如果仍找不到，则使用任何JS文件
        if (!jsFile) {
            jsFile = files.find(file => file.endsWith('.js'));
        }

        return jsFile ? `assets/${jsFile}` : null;
    } catch (error) {
        console.error(`查找JS文件(${jsPrefix})失败:`, error);
        return null;
    }
};

// 复制所有静态资源文件
const copyAllAssets = () => {
    try {
        const srcAssetsDir = path.join(__dirname, 'src', 'assets');
        if (!fs.existsSync(srcAssetsDir)) {
            console.error('源资源目录不存在:', srcAssetsDir);
            return;
        }

        console.log('源资源目录:', srcAssetsDir);

        // 确保目标目录存在
        if (!fs.existsSync(distAssetsDir)) {
            fs.mkdirSync(distAssetsDir, { recursive: true });
            console.log('创建了资源目标目录:', distAssetsDir);
        }

        // 读取源资源目录中的所有文件
        const files = fs.readdirSync(srcAssetsDir);
        console.log(`源资源目录中有 ${files.length} 个文件`);

        // 复制所有资源文件
        let copiedCount = 0;
        let errorCount = 0;
        let skippedCount = 0;

        files.forEach(file => {
            const sourcePath = path.join(srcAssetsDir, file);
            const targetPath = path.join(distAssetsDir, file);

            // 只处理文件，不处理目录
            if (fs.statSync(sourcePath).isFile()) {
                try {
                    // 检查目标文件是否存在
                    const targetExists = fs.existsSync(targetPath);

                    // 如果目标文件已存在，检查文件大小是否相同
                    if (targetExists) {
                        const sourceStats = fs.statSync(sourcePath);
                        const targetStats = fs.statSync(targetPath);

                        // 如果文件大小相同，跳过复制
                        if (sourceStats.size === targetStats.size) {
                            skippedCount++;
                            return;
                        }
                    }

                    // 执行复制
                    fs.copyFileSync(sourcePath, targetPath);
                    copiedCount++;

                    // 每10个文件输出一次日志，避免日志过多
                    if (copiedCount % 10 === 0) {
                        console.log(`已复制 ${copiedCount} 个资源文件到 dist/assets 目录`);
                    }
                } catch (err) {
                    errorCount++;
                    console.error(`复制文件 ${file} 失败:`, err);
                }
            }
        });

        console.log(`总共复制了 ${copiedCount} 个资源文件，跳过 ${skippedCount} 个文件，失败 ${errorCount} 个文件`);

        // 复制一些必需的特定SVG文件（确保关键SVG文件存在）
        const criticalSvgFiles = [
            'nofocus.svg',
            'edit0.svg',
            'edit1.svg',
            'edit2.svg',
            'edit3.svg',
            'edit4.svg',
            'edit_config.svg',
            'search_color.svg',
            // 添加消息提示所需的SVG图标
            'success.svg',
            'error.svg',
            'warning.svg',
            'loading.svg'
        ];

        console.log('确保关键SVG文件存在...');

        let criticalErrorCount = 0;
        criticalSvgFiles.forEach(file => {
            const sourcePath = path.join(srcAssetsDir, file);
            const targetPath = path.join(distAssetsDir, file);

            if (fs.existsSync(sourcePath)) {
                try {
                    // 强制复制关键文件，覆盖目标
                    fs.copyFileSync(sourcePath, targetPath);
                    console.log(`已确保关键资源文件存在: ${file}`);
                } catch (err) {
                    criticalErrorCount++;
                    console.error(`复制关键文件 ${file} 失败:`, err);
                }
            } else {
                criticalErrorCount++;
                console.error(`关键资源文件不存在: ${sourcePath}`);
            }
        });

        if (criticalErrorCount > 0) {
            console.error(`警告: ${criticalErrorCount} 个关键SVG文件复制失败！`);
        } else {
            console.log('所有关键SVG文件复制成功!');
        }
    } catch (error) {
        console.error('复制资源文件失败:', error);
    }
};

const cssFilename = findCssFilename();
console.log('找到CSS文件:', cssFilename);

// 复制所有资源文件
copyAllAssets();

// 替换HTML内容中的所有资源路径
const replaceAllAssetPaths = (content) => {
    // 替换img标签的src属性
    content = content.replace(/src="\/src\/assets\//g, 'src="./assets/');
    content = content.replace(/src="src\/assets\//g, 'src="./assets/');

    // 确保SVG图片直接路径也被替换
    content = content.replace(/src=['"]([^'"]*\.svg)['"]/g, (match, p1) => {
        if (p1.includes('./assets/') || p1.includes('/assets/')) {
            return match; // 已经是正确路径
        }
        const filename = p1.split('/').pop(); // 获取文件名
        return `src="./assets/${filename}"`;
    });

    // 替换CSS中的url引用
    content = content.replace(/url\(["']?\/src\/assets\//g, 'url("./assets/');
    content = content.replace(/url\(["']?src\/assets\//g, 'url("./assets/');

    // 替换其他可能的属性中的路径（如background）
    content = content.replace(/background(-image)?:[\s]*url\(["']?\/src\/assets\//g, 'background$1: url("./assets/');
    content = content.replace(/background(-image)?:[\s]*url\(["']?src\/assets\//g, 'background$1: url("./assets/');

    // 处理inline SVG的href属性
    content = content.replace(/href="\/src\/assets\//g, 'href="./assets/');
    content = content.replace(/href="src\/assets\//g, 'href="./assets/');

    // 处理可能的相对路径引用
    content = content.replace(/href=['"]\.\.\/assets\/([^'"]+)['"]/g, 'href="./assets/$1"');

    // 替换CSS stylesheet引用
    content = content.replace(/<link [^>]*rel="stylesheet"[^>]*href="\.?\/src\/styles\/main\.css"[^>]*>/g,
        cssFilename ? `<link rel="stylesheet" crossorigin href="./${cssFilename}">` : '');
    content = content.replace(/<link [^>]*rel="stylesheet"[^>]*href="\.?\/src\/styles\.css"[^>]*>/g,
        cssFilename ? `<link rel="stylesheet" crossorigin href="./${cssFilename}">` : '');

    return content;
};

// 复制并处理文件
filesToCopy.forEach(fileConfig => {
    const { html: file, jsPrefix } = fileConfig;
    const sourcePath = path.join(__dirname, file);
    const targetPath = path.join(distDir, file);

    // 查找对应的JS文件
    const jsFilename = findJsFilename(jsPrefix);

    try {
        // 读取原始文件内容
        if (!fs.existsSync(sourcePath)) {
            console.error(`源文件不存在: ${sourcePath}`);
            return;
        }

        let content = fs.readFileSync(sourcePath, 'utf8');
        console.log(`读取文件 ${file} 成功, 大小: ${content.length} 字节`);

        // 替换图片和资源路径
        content = replaceAllAssetPaths(content);

        // 替换JS引入
        // 找到并移除所有script标签
        content = content.replace(/<script\s+type="module"\s+src="[^"]*"><\/script>/g, '');

        // 在</body>之前添加新的script标签
        if (jsFilename) {
            content = content.replace('</body>', `<script type="module" src="./${jsFilename}"></script>\n</body>`);
            console.log(`为 ${file} 添加JS引用: ${jsFilename}`);
        }

        // 写入目标文件
        fs.writeFileSync(targetPath, content);
        console.log(`写入文件 ${file} 成功, 大小: ${content.length} 字节`);
    } catch (error) {
        console.error(`处理文件 ${file} 失败:`, error);
    }
});

console.log('HTML文件复制和处理完成!');