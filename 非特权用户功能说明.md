# MAML 脚本非特权用户功能说明

本文档整理了MAML脚本中非特权用户可使用的功能，以及如何在XML中使用这些功能。

## 目录

- [删除空属性](#删除空属性)
- [删除未使用变量](#删除未使用变量)
- [从Manifest生成变量](#从manifest生成变量)
- [从配置生成变量](#从配置生成变量)
- [命令移动到动作](#命令移动到动作)
- [缩放组](#缩放组)
- [合并外部命令](#合并外部命令)
- [展开表达式](#展开表达式)
- [调试功能](#调试功能)
- [变量命令处理](#变量命令处理)
- [SVG转TTF](#svg转ttf)
- [内容提供者绑定器处理](#内容提供者绑定器处理)

## 删除空属性

### 功能描述

自动删除XML文档中所有空字符串属性（即值为`""`的属性）。

### 实现方式

系统会遍历所有XML元素，移除值为空字符串的属性，使文档更加简洁。

### 使用示例

**修改前：**
```xml
<Rectangle w="100" h="100" color="" strokeColor="" />
```

**修改后：**
```xml
<Rectangle w="100" h="100" />
```

## 删除未使用变量

### 功能描述

删除XML文档中未被引用的变量定义。

### 支持的变量标签

- `<Var>`
- `<VariableCommand>`
- `<Permanence>`
- `<PermanenceCommand>`
- `<Variable>`

### 变量引用识别

系统能够识别以下形式的变量引用：
- 基本变量引用：`#变量名`, `@变量名`, `$变量名`
- 数组引用：`#变量名[索引]`
- 函数调用中的变量：`max(#变量1, #变量2)`
- 字符串拼接中的变量：`'文本'+#变量名` 

### 使用示例

**修改前：**
```xml
<Var name="usedVar" type="number" expression="1" />
<Var name="unusedVar" type="number" expression="2" />
<Text textExp="#usedVar" />
```

**修改后：**
```xml
<Var name="usedVar" type="number" expression="1" />
<Text textExp="#usedVar" />
```

## 从Manifest生成变量

### 功能描述

从`<VariableCommand>`标签自动生成`<Var>`标签，并插入到文档根元素的第一行。

### 使用方法

在XML中使用`<VariableCommand>`标签定义变量，系统将自动生成对应的`<Var>`标签。

### 支持特性

- 支持生成数组变量
- 支持指定变量类型
- 支持循环中的变量生成

### 使用示例

**修改前：**
```xml
<LoopCommand count="10" indexName="i">
  <VariableCommand name="items" expression="#i" index="#i" type="string[]" />
</LoopCommand>
```

**修改后：** 
（自动在文档顶部生成）
```xml
<Var name="items" size="10" type="string[]" const="true" />
<!-- 原有内容保持不变 -->
```

## 从配置生成变量

### 功能描述

从`var_config.xml`文件生成组件变量配置，添加`<Var>`标签到主文档。

### 使用方法

创建`var_config.xml`文件，定义需要的变量，系统将自动读取并生成对应的`<Var>`标签。

### 使用示例

**var_config.xml:**
```xml
<Config>
  <Variables>
    <Variable name="textColor" value="#FF0000" type="color" />
    <Variable name="fontSize" value="24" type="number" />
  </Variables>
</Config>
```

**生成结果:**
```xml
<Var name="textColor" expression="#FF0000" type="color" const="true" />
<Var name="fontSize" expression="24" type="number" const="true" />
```

## 命令移动到动作

### 功能描述

将带有`hook`属性的标签移动到`<ExternalCommands>`标签中对应的`<Trigger>`节点下。

### 使用方法

为元素添加`hook`属性，指定触发动作，系统将自动将其移动到对应的`<Trigger>`节点中。

### 使用示例

**修改前：**
```xml
<Button text="确定" hook="button_click" />
```

**修改后：**
```xml
<ExternalCommands>
  <Trigger action="button_click">
    <Button text="确定" />
  </Trigger>
</ExternalCommands>
```

## 缩放组

### 功能描述

将`<ScaleGroup>`标签转换为标准的`<Group>`标签，根据中心点配置自动设置对齐和缩放属性。

### 使用方法

使用`<ScaleGroup>`标签，通过`centerPoint`属性指定中心点位置（1-9），系统将自动转换为功能等效的`<Group>`标签。

### 中心点配置

- 1: 左上
- 2: 上中
- 3: 右上
- 4: 左中
- 5: 中间
- 6: 右中
- 7: 左下
- 8: 下中
- 9: 右下

### 使用示例

**修改前：**
```xml
<ScaleGroup w="200" h="100" centerPoint="5" type="min">
  <Text text="示例文本" />
</ScaleGroup>
```

**修改后：**
```xml
<Group w="200" h="100" scale="min(#view_width/200,#view_height/100)" align="center" alignV="center" x="#view_width/2" y="#view_height/2" pivotX="100" pivotY="50">
  <Text text="示例文本" />
</Group>
```

## 合并外部命令

### 功能描述

将多个`<ExternalCommands>`标签合并为一个`<ExternalCommands>`标签。

### 使用方式

当文档中存在多个`<ExternalCommands>`标签时，系统会自动将其合并为一个标签，保留所有子节点。

### 使用示例

**修改前：**
```xml
<ExternalCommands>
  <Trigger action="init">
    <Command1 />
  </Trigger>
</ExternalCommands>
<ExternalCommands>
  <Trigger action="click">
    <Command2 />
  </Trigger>
</ExternalCommands>
```

**修改后：**
```xml
<ExternalCommands>
  <Trigger action="init">
    <Command1 />
  </Trigger>
  <Trigger action="click">
    <Command2 />
  </Trigger>
</ExternalCommands>
```

## 展开表达式

### 功能描述

处理表达式属性，将特定格式的表达式展开为完整形式。

### 支持的属性

- `expression`
- `ifCondition`
- `condition`
- `values`
- `textExp`

### 表达式展开规则

将`#param[1,+,5]`形式的表达式展开为`#param[1] + #param[2] + #param[3] + #param[4] + #param[5]`。

### 使用示例

**修改前：**
```xml
<Var name="total" expression="#values[1,+,5]" />
```

**修改后：**
```xml
<Var name="total" expression="#values[1] + #values[2] + #values[3] + #values[4] + #values[5]" />
```

## 调试功能

### 功能描述

处理调试相关的标签和属性，为开发阶段提供便利。

### 功能特性

1. 处理`<Debug>`标签，将其转换为显示调试信息的`<Group>`
2. 处理带有`.log`后缀的属性，生成调试表达式

### 使用示例

**Debug标签：**
```xml
<Debug x="50" y="50" textExp="#value1,#value2" />
```

**修改后：**
```xml
<Group x="50" y="50">
  <Text y="0" w="#view_width-60" marqueeSpeed="100" textExp="'#value1  ='+#value1" />
  <Text y="45" w="#view_width-60" marqueeSpeed="100" textExp="'#value2  ='+#value2" />
</Group>
```

**Log属性：**
```xml
<Button text="点击" value.log="true" />
```

系统会自动创建Debug标签显示value的值。

## 变量命令处理

### 功能描述

转换变量相关的命令，处理数组变量、索引等。

### 使用示例

```xml
<VariableCommand name="array" type="string[]" size="5" />
```

将会为`array`生成一个大小为5的字符串数组。

## SVG转TTF

### 功能描述

将SVG图标转换为TTF字体文件，便于在MAML中使用自定义字体图标。

### 使用方法

提供SVG文件，系统将自动转换为TTF格式的字体文件。

## 内容提供者绑定器处理

### 功能描述

包含两个功能：
1. 确保`<ContentProviderBinder>`标签的`columns`属性包含所有子`<Variable>`标签的`column`属性值
2. 自动添加初始化和刷新的`<BinderCommand>`

### 使用示例

**修改前：**
```xml
<ContentProviderBinder name="binder1">
  <Variable column="name" />
  <Variable column="age" />
</ContentProviderBinder>
```

**修改后：**
```xml
<ContentProviderBinder name="binder1" columns="name,age">
  <Variable column="name" />
  <Variable column="age" />
</ContentProviderBinder>
<ExternalCommands>
  <Trigger action="init,resume">
    <BinderCommand name="binder1" command="refresh" />
  </Trigger>
</ExternalCommands>
``` 