<!doctype html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8" />
    <link rel="stylesheet" href="/src/styles/main.css" />
    <link rel="stylesheet" href="/src/styles/history.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Mini Editor Pro</title>
    <!-- 标题栏 -->
    <script type="module" src="/src/script/title_bar.ts"></script>
    <!-- 常用连接 -->
    <script type="module" src="src/script/help.ts"></script>
    <!-- 用户名 -->
    <script type="module" src="src/script/user_name.ts"></script>
    <!-- 当前平台 -->
    <script type="module" src="src/script/current_platform.ts"></script>
    <!-- 初始化窗口 -->
    <script type="module" src="src/script/init_window.ts"></script>
    <!-- 拖入文件 -->
    <script type="module" src="/src/script/drag_file_handler.ts"></script>
    <!-- 环境变量 -->
    <script type="module" src="src/script/fix_env_vars.ts"></script>
    <!-- 置顶窗口 -->
    <script type="module" src="/src/script/top_window.ts" defer></script>
    <!-- 日志 -->
    <script type="module" src="src/script/rust_log.ts" defer></script>
    <!-- 提示消息 -->
    <script type="module" src="/src/script/prompt_message.ts" defer></script>
    <!-- 历史记录 -->
    <script type="module" src="/src/script/history.ts" defer></script>
    <!-- 打包主题 -->
    <script type="module" src="/src/script/pack_theme.ts" defer></script>
    <!-- 应用主题 -->
    <script type="module" src="/src/script/apply/index.ts" defer></script>
    <!-- 清理缓存 -->
    <script type="module" src="/src/script/clear_cache.ts" defer></script>
    <!-- 截屏 -->
    <script type="module" src="/src/script/screenshot.ts" defer></script>
    <!-- 获取包名 -->
    <script type="module" src="/src/script/get_package_class.ts" defer></script>
    <!-- 重启ADB -->
    <script type="module" src="/src/script/restart_adb.ts" defer></script>
    <!-- 无线调试 -->
    <script type="module" src="/src/script/wireless_adb.ts" defer></script>
    <!-- 提取APK -->
    <script type="module" src="/src/script/extract_apk.ts" defer></script>
    <!-- 安装APK -->
    <script type="module" src="/src/script/install_apk.ts" defer></script>
    <!-- 提取启动项 -->
    <script type="module" src="/src/script/extract_launcher.ts" defer></script>
    <!-- 重置样式 -->
    <script type="module" src="/src/script/reset_style.ts" defer></script>
    <!-- 拖入文件入口 -->
    <script type="module" src="/src/script/drag_file_entry.ts" defer></script>
    <!-- 打包大图标 -->
    <script type="module" src="/src/script/pack_large_icons.ts" defer></script>
    <!-- 圆形路径文本 -->
    <script type="module" src="src/script/maml_scrcpt/cir_path_text.ts" defer></script>
    <!-- 命令移动动作 -->
    <script type="module" src="src/script/maml_scrcpt/command_move_action.ts" defer></script>
    <!-- 默认列 -->
    <script type="module" src="src/script/maml_scrcpt/cpb_default_columns.ts" defer></script>
    <!-- 绑定命令 -->
    <script type="module" src="src/script/maml_scrcpt/cpb_binder_command.ts" defer></script>
    <!-- 格式化XML -->
    <script type="module" src="src/script/maml_scrcpt/format_xml.ts" defer></script>
    <!-- 渐变样式 -->
    <script type="module" src="src/script/maml_scrcpt/gradient_style.ts" defer></script>
    <!-- 主脚本 -->
    <script type="module" src="src/script/maml_scrcpt/maml_scrcpt_main.ts" defer></script>
    <!-- 生成变量 -->
    <script type="module" src="src/script/maml_scrcpt/manifest_generate_var.ts" defer></script>
    <!-- 展开表达式 -->
    <script type="module" src="src/script/maml_scrcpt/expand_expression.ts" defer></script>
    <!-- 更新动画 -->
    <script type="module" src="src/script/maml_scrcpt/update_animation.ts" defer></script>
    <!-- 解锁配置生成变量 -->
    <script type="module" src="src/script/maml_scrcpt/unlock_config_generate_var.ts" defer></script>
    <!-- 变量命令 -->
    <script type="module" src="src/script/maml_scrcpt/var_commands.ts" defer></script>
    <!-- 变量类型 -->
    <script type="module" src="src/script/maml_scrcpt/var_types.ts" defer></script>
    <!-- 小部件配置生成变量 -->
    <script type="module" src="src/script/maml_scrcpt/widget_config_generate_var.ts" defer></script>
    <!-- 调试 -->
    <script type="module" src="src/script/maml_scrcpt/debug.ts" defer></script>
    <!-- 删除空属性 -->
    <script type="module" src="src/script/maml_scrcpt/delete_empty_properties.ts" defer></script>
    <!-- 平均值 -->
    <script type="module" src="src/script/maml_scrcpt/avg.ts" defer></script>
    <!-- SVG转TTF -->
    <script type="module" src="src/script/maml_scrcpt/svg_to_ttf.ts" defer></script>
    <!-- 缩放组 -->
    <script type="module" src="src/script/maml_scrcpt/scale_group.ts" defer></script>
    <!-- 合并外部命令 -->
    <script type="module" src="src/script/maml_scrcpt/merge_external_commands.ts" defer></script>
    <!-- 合并变量绑定 -->
    <script type="module" src="src/script/maml_scrcpt/merge_variable_binders.ts" defer></script>
    <!-- 线性进度条 -->
    <script type="module" src="src/script/maml_scrcpt/linear_progress_bar.ts" defer></script>
    <!-- 弧形进度条 -->
    <script type="module" src="src/script/maml_scrcpt/arc_progress_bar.ts" defer></script>
    <!-- 删除未使用变量 -->
    <script type="module" src="src/script/maml_scrcpt/delete_unused_variables.ts" defer></script>
    <!-- 投屏 -->
    <script type="module" src="src/script/screen_cast.ts" defer></script>
    <!-- 超大文件检测 -->
    <script type="module" src="src/script/extra_large_file_detection.ts" defer></script>
    <!-- 替换颜色 -->
    <script type="module" src="src/script/replace_color.ts" defer></script>
    <!-- 字体生成图片 -->
    <script type="module" src="src/script/font_to_image.ts" defer></script>
    <!-- 图片切图 -->
    <script type="module" src="src/script/image_crop.ts" defer></script>
    <!-- 更新XML -->
    <script type="module" src="src/script/update_xml.ts" defer></script>
    <!-- 控制台 -->
    <script type="module" src="src/script/console.ts" defer></script>
    <!-- 推送文件 -->
    <script type="module" src="src/script/push_file.ts" defer></script>
    <!-- 文件操作 -->
    <script type="module" src="src/script/file_operations.ts" defer></script>
    <!-- UI动画 -->
    <script type="module" src="src/script/ui_animation.ts" defer></script>
    <!-- aapt命令 -->
    <script type="module" src="src/script/aapt.ts" defer></script>
    <!-- 终端命令 -->
    <script type="module" src="src/script/terminal_commands.ts" defer></script>
    <!-- 重启到刷机模式 -->
    <script type="module" src="src/script/reboot.ts" defer></script>
    <!-- 获取根目录 -->
    <script type="module" src="src/script/get_root_dir.ts" defer></script>
    <!-- 生成随机主题 -->
    <script type="module" src="src/script/random_color_theme/random_color_theme.ts" defer></script>
    <!-- 颜色搜索窗口 -->
    <script type="module" src="src/script/random_color_theme/search_color_window.ts" defer></script>
    <!-- 编辑配置 -->
    <script type="module" src="src/script/edit_config/edit_config_window.ts" defer></script>
    <!-- 特殊覆盖按钮 -->
    <script type="module" src="src/script/special_overlay_button.ts" defer></script>
    <!-- 安卓开发者选项 -->
    <!-- <script type="module" src="src/script/developer_options.ts" defer></script> -->
    <!-- 终端安装器 -->
    <script type="module" src="src/script/terminal_installer.ts" defer></script>
</head>

<body>
    <!-- 标题栏 -->
    <div data-tauri-drag-region id="title-bar" class="custom-title-bar" ondblclick="event.preventDefault()">
        <div id="title-bar-controls" class="title-bar-controls">
            <button id="close-btn">
                <img class="title_icon" id="close-icon" src="/src/assets/nofocus.svg" alt="close">
            </button>
            <button id="minimize-btn">
                <img class="title_icon" id="minimize-icon" src="/src/assets/nofocus.svg" alt="minimize">
            </button>
            <button id="maximize-btn">
                <img class="title_icon" id="maximize-icon" src="/src/assets/nofocus.svg" alt="maximize">
            </button>
        </div>
    </div>

    <main class="container">

        <div class="row">
            <!-- 拖入主题文件 -->
            <div id="mtz-name" class="mtz-name">
                <div id="mtz-name-text" class="mtz-name-text" onclick="showHistory()">
                    拖入主题文件
                </div>
                <div class="mtz-name-icon" onclick="打开目录或文件(window.dragFiles)">
                    <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M3.28249 3.28249C5.06408 1.50089 7.48044 0.5 10 0.5C12.5196 0.5 14.9359 1.50089 16.7175 3.28249C18.4991 5.06408 19.5 7.48044 19.5 10C19.5 12.5196 18.4991 14.9359 16.7175 16.7175C14.9359 18.4991 12.5196 19.5 10 19.5C7.48044 19.5 5.06408 18.4991 3.28249 16.7175C1.50089 14.9359 0.5 12.5196 0.5 10C0.5 7.48044 1.50089 5.06408 3.28249 3.28249ZM10 3.5C8.27609 3.5 6.62279 4.18482 5.40381 5.40381C4.18482 6.62279 3.5 8.27609 3.5 10C3.5 11.7239 4.18482 13.3772 5.40381 14.5962C6.62279 15.8152 8.27609 16.5 10 16.5C11.7239 16.5 13.3772 15.8152 14.5962 14.5962C15.8152 13.3772 16.5 11.7239 16.5 10C16.5 8.27609 15.8152 6.62279 14.5962 5.40381C13.3772 4.18482 11.7239 3.5 10 3.5Z" />
                    </svg>

                </div>
            </div>

            <!-- 导出 MTZ -->
            <button class="button_max" onclick="打包主题(window.cacheDir)">
                导出 MTZ
            </button>

        </div>

        <!-- 工具栏 -->
        <div class="tools-button-container">

            <button class="icon_button" alt="screenshot" onclick="截屏()">
                <div class="bg"></div>
                <p class="tooltip">截屏到电脑</p>
                <img class="svgIcon" src="/src/assets/screenshot.svg">
            </button>

            <button class="icon_button" alt="clear-cache" onclick="清理缓存()">
                <div class="bg"></div>
                <p class="tooltip">清理主题缓存</p>
                <img class="svgIcon" src="/src/assets/clear-cache.svg">
            </button>
            <button class="icon_button" alt="screen-cast" onclick="投屏()">
                <div class="bg"></div>
                <p class="tooltip">投屏到电脑</p>
                <img class="svgIcon" src="/src/assets/screen-cast.svg">
            </button>
            <button class="icon_button" alt="package-name" onclick="获取包名()">
                <div class="bg"></div>
                <p class="tooltip">复制手机当前界面包名</p>
                <img class="svgIcon" src="/src/assets/package-name.svg">
            </button>
            <button class="icon_button" alt="class-package" onclick="获取包名类名()">
                <div class="bg"></div>
                <p class="tooltip">复制手机当前界面包名类名</p>
                <img class="svgIcon" src="/src/assets/class-package.svg">
            </button>
            <button class="icon_button" alt="maml-icon" onclick="获取icon()">
                <div class="bg"></div>
                <p class="tooltip">复制手机当前界面图标为MAML</p>
                <img class="svgIcon" src="/src/assets/maml-icon.svg">
            </button>
            <button class="icon_button" alt="wireless-debug" onclick="无线adb()">
                <div class="bg"></div>
                <p class="tooltip">无线调试</p>
                <img class="svgIcon" src="/src/assets/wireless-debug.svg">
            </button>
            <button class="icon_button" alt="restart-adb" onclick="重启adb()">
                <div class="bg"></div>
                <p class="tooltip">重启ADB</p>
                <img class="svgIcon" src="/src/assets/restart-adb.svg">
            </button>
            <button class="icon_button" alt="extract-apk" onclick="提取apk()">
                <div class="bg"></div>
                <p class="tooltip">提取当前界面APK</p>
                <img class="svgIcon" src="/src/assets/extract-apk.svg">
            </button>
            <button class="icon_button" alt="decompile-apk" onclick="提取apk(true)">
                <div class="bg"></div>
                <p class="tooltip">反编译当前界面APK</p>
                <img class="svgIcon" src="/src/assets/decompile-apk.svg">
            </button>
            <button class="icon_button" alt="extract-launcher" onclick="提取启动项()">
                <div class="bg"></div>
                <p class="tooltip">尝试提取当前界面所有启动项</p>
                <img class="svgIcon" src="/src/assets/extract-launcher.svg">
            </button>
            <!-- <button class="icon_button" alt="random-color" onclick="随机颜色主题()">
                <div class="bg"></div>
                <p class="tooltip">为各模块生成随机颜色主题</p>
                <img class="svgIcon" src="/src/assets/random-color.svg">
            </button> -->
            <button class="icon_button" id="img-size" alt="img-size" onclick="超大文件检测(window.dragFiles)">
                <div class="bg"></div>
                <p class="tooltip">主题包中超过1M文件检测</p>
                <img class="svgIcon" src="/src/assets/img-size.svg">
            </button>
            <!-- <button class="icon_button" id="FASTBOOT" alt="FASTBOOT" onclick="reboot()">
                <div class="bg"></div>
                <p class="tooltip">FASTBOOT刷机模式</p>
                <img class="svgIcon" src="/src/assets/fastboot.svg">
            </button> -->
            <!-- <button class="icon_button" id="edl" alt="edl" onclick="reboot(true)">
                <div class="bg"></div>
                <p class="tooltip">EDL刷机模式</p>
                <img class="svgIcon" src="/src/assets/edl.svg">
            </button> -->
            <!-- <button class="icon_button" id="random-color-" alt="random-color" onclick="random_color_theme()">
                <div class="bg"></div>
                <p class="tooltip">生成随机颜色主题</p>
                <img class="svgIcon" src="/src/assets/random_color_theme.svg">
            </button> -->
            <button class="icon_button" id="search-color" alt="search-color">
                <div class="bg"></div>
                <p class="tooltip">截图并搜索随机颜色对应资源</p>
                <img class="svgIcon" src="/src/assets/random_color_theme.svg">
            </button>
            <button class="icon_button" id="search-color" alt="search-color" onclick="replace_color(window.dragFiles)">
                <div class="bg"></div>
                <p class="tooltip">替换颜色</p>
                <img class="svgIcon" src="/src/assets/replace_color.svg">
            </button>
            <button class="icon_button" id="edit_config" alt="edit_config">
                <div class="bg"></div>
                <p class="tooltip">打开配置目录</p>
                <img class="svgIcon" src="/src/assets/edit_config.svg">
            </button>
            <!-- <button class="icon_button" id="developer-options" alt="developer-options">
                <div class="bg"></div>
                <p class="tooltip">跳转到开发者选项设置</p>
                <img class="svgIcon" src="/src/assets/developer_options.svg">
            </button> -->
            <button class="icon_button" id="pin-window" alt="pin-window" onclick="置顶()">
                <div class="bg"></div>
                <p class="tooltip">置顶窗口</p>
                <img class="svgIcon" src="/src/assets/pin-window.svg">
            </button>
        </div>

        <!-- 应用主题 -->
        <div style="margin-top: -25px;">
            <div id="apply-theme" class="apply-theme-button-container"></div>
        </div>

        <!-- 应用小部件 -->
        <div style="margin-top: -5px;">
            <div id="apply-widget" class="apply-widget-button-container"></div>
        </div>

        <!-- 历史记录 -->
        <div id="history-container" class="history-container">
        </div>
    </main>

    <!-- 连接设备 -->
    <div id="device-container" class="device-container">
        <div id="device-horizontal-strip" class="device-horizontal-strip"></div>
        <span>在以下设备中执行</span>
        <div class="device-close-button-container">
            <span class="device-close-container">关闭</span>
        </div>
        <div class="device-update-button-container">
            <span class="device-update-container">更新</span>
        </div>
        <div id="device-list"></div>
    </div>

    <!--打包大图标 -->
    <div id="bigIconPackageDialog" class="big-icon-dialog">
        <div class="big-icon-dialog-content">
            <div class="form-group">
                <input type="text" id="designer" placeholder="设计师名称:">
            </div>

            <div class="form-group">
                <input type="text" id="title" placeholder="套装名称:">
            </div>

            <div class="form-group">
                <textarea id="description" placeholder="套装介绍:"></textarea>
            </div>

            <div class="form-group">
                <input id="uiVersion" placeholder="UI版本:默认14">
            </div>

            <div class="checkmarkcontainer form-group">
                使用系统切圆角
                <input class="checkmark" id="roundedCorner" type="checkbox">
            </div>

            <div class="checkmarkcontainer form-group">
                官方图标：三方勿选
                <input class="checkmark" id="officialIcons" type="checkbox">
            </div>

            <div class="dialog-buttons">
                <button class="big_icon_button" id="cancelBtn">取消</button>
                <button class="big_icon_button" id="confirmBtn">确认</button>
            </div>
        </div>
    </div>

    <!-- 大图标和文件推送入口 -->
    <div id="bubble-container" class="bubble-container">
        <div id="bubble1" class="bubble">
            拖入此处支持以下操作
            <br>
            打包/解包mtz 支持批量
            <br>
            编译/反编译点9图 支持批量
            <br>
            安装apk 支持批量
            <br>
            字体生成图片
            <br>
            文本中unicode转中文
            <!-- <br>
            1080p png图片生成背景图 -->
        </div>

        <div class="line"></div>

        <div id="bubble2" class="bubble">
            拖入此处 推送文件到手机
        </div>

        <div class="line"></div>

        <div id="bubble3" class="bubble">
            拖入此处 打包大图标
        </div>
    </div>

    <!-- 帮助界面 教程链接 -->
    <div id="help-container" class="help-container">
        <div class="help-content">
            <a class="help-link"
                href="https://zhuti.designer.xiaomi.com/docs/themePage/guide/#_1-%E6%96%B0%E5%BB%BA%E4%B8%BB%E9%A2%98">主题教程</a>
            <a class="help-link" href="https://zhuti.designer.xiaomi.com/docs/grammar/#aod-%E6%81%AF%E5%B1%8F">息屏教程</a>
            <a class="help-link" href="https://zmsto9sfpq.feishu.cn/docx/doxcndMGffgwu1b5tQyDP5V77sf">小部件教程</a>
            <a class="help-link" href="https://egrqfhyh64.feishu.cn/docx/doxcnGtHAbjk2LuXdJ6ly6VqQhd">大图标教程</a>
            <a class="help-link" href="https://egrqfhyh64.feishu.cn/docx/EiNtdRwD1oTdEmxRIb8cqaqYnge">平板主题教程</a>
            <a class="help-link" href="https://zhuti.designer.xiaomi.com/">设计师后台</a>
            <a class="help-link" href="https://zhuti.designer.xiaomi.com/docs/grammar/">MAML 教程</a>
            <a class="help-link" href="https://si7pseb30t.feishu.cn/docx/QREXdMXFwoKyuvxrtVjcyQ0BnYe">主题制作常见问题</a>
            <a class="help-link"
                href="https://zmsto9sfpq.feishu.cn/docx/doxcndMGffgwu1b5tQyDP5V77sf#doxcneCuEG2Usq4aUKa7PxePWyc">小部件制作常见问题</a>
            <a class="help-link"
                href="https://egrqfhyh64.feishu.cn/docx/GhRRdEh1lo7k7ExGw1jcLXPenlh?from=from_copylink">小编辑器使用教程</a>
        </div>


    </div>

    <!--字体生成图片 -->
    <div id="font-to-img" class="font-to-img">
        <div class="font-content">
            <div class="font-content-item">
                abcdefghijklmnopqrstuvwxyz
            </div>
            <div class="font-content-item">
                ABCDEFGHIJKLMNOPQRSTUVWXYZ
            </div>
            <div class="font-content-item">
                周 星期 年 月 日 一 二 三 四 五 六
            </div>
            <div class="font-content-item">
                0 1 2 3 4 5 6 7 8 9 : % °
            </div>
        </div>

        <div class="font-to-img-color">
            <input class="font-input" type="string" placeholder="字体颜色 #RGBA or #RGB">
        </div>
        <div class="font-to-img-color">
            <input class="font-input" type="string" placeholder="中文大小">
            <input class="font-input" type="string" placeholder="英文大小">
        </div>
        <div class="font-to-img-color">
            <input class="font-input" type="string" placeholder="A组 数字大小">
            <input class="font-input" type="string" placeholder="B组 数字大小">
        </div>

        <div class="font-to-img-content">
            <div id="font-to-img-edit" class="img-container">
                <img class="svgIcon" src="/src/assets/edit.svg">
            </div>
            <div id="font-to-img-cancel" class="img-container">
                <img class="svgIcon" src="/src/assets/cancel.svg">
            </div>
            <div id="font-to-img-ok" class="img-container">
                <img class="svgIcon" src="/src/assets/ok.svg">
            </div>
        </div>
    </div>

    <!-- 提示消息 -->
    <div id="message-container" class="message-container">
    </div>

    <div id="console-container" class="console-container">
        debug
    </div>

</body>



</html>