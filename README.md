# Tauri + Vanilla TS

This template should help get you started developing with <PERSON><PERSON> in vanilla HTML, CSS and Typescript.

## Recommended IDE Setup

- [VS Code](https://code.visualstudio.com/) + [<PERSON><PERSON>](https://marketplace.visualstudio.com/items?itemName=tauri-apps.tauri-vscode) + [rust-analyzer](https://marketplace.visualstudio.com/items?itemName=rust-lang.rust-analyzer)


# 调试

```bash
yarn tauri dev
```

# 打包
```bash
yarn tauri build
```
# 带debug打包

```bash
yarn tauri build --debug
```

# 在github上构建发布
```bash
VERSION="v0.2.2" && git tag $VERSION && git push origin $VERSION
```

# 删除本地和远程tag
```bash
git tag | xargs -I {} sh -c 'git tag -d {} && git push --delete origin {}'
git fetch --prune --tags
```

# 查看过期依赖
yarn outdated

# 更新所有依赖到最新版本
yarn upgrade --latest


---
# 首先安装 cargo-edit
cargo install cargo-edit

# 然后在 src-tauri 目录下运行以下命令更新所有依赖
cargo upgrade

# 或者更新指定依赖
cargo upgrade tauri

---

# 更新 Rust 依赖
## 进入 Rust 项目目录
cd src-tauri

## 安装 cargo-outdated
cargo install cargo-outdated

## 查看过期依赖
cargo outdated

## 更新 Cargo.lock 中的所有依赖到最新版本
cargo update

## 或者更新指定依赖
cargo update -p tauri
cargo update -p wry
cargo update -p tao