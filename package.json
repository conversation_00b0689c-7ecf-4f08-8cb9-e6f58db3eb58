{"name": "mini-editor-pro", "private": true, "version": "0.2.14", "type": "module", "scripts": {"dev": "node scripts/copy-assets.js && vite", "build": "node scripts/copy-assets.js && tsc && vite build && node copyHtmlFiles.js", "preview": "vite preview", "tauri": "tauri", "lint": "eslint . --ext .ts,.tsx", "sync-assets": "node scripts/copy-assets.js"}, "dependencies": {"@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-clipboard-manager": "^2.2.2", "@tauri-apps/plugin-dialog": "^2.2.2", "@tauri-apps/plugin-fs": "^2.3.0", "@tauri-apps/plugin-global-shortcut": "^2.2.0", "@tauri-apps/plugin-log": "^2.2.0", "@tauri-apps/plugin-opener": "^2.2.0", "@tauri-apps/plugin-os": "~2", "@tauri-apps/plugin-shell": "^2.3.0", "@tauri-apps/plugin-store": "^2.2.0", "@tauri-apps/plugin-window-state": "^2.2.2", "@types/fs-extra": "^11.0.4", "@types/glob": "^8.1.0", "@types/react": "^19.0.12", "@types/xml2js": "^0.4.14", "font-carrier": "^0.3.1", "fs-extra": "^11.3.0", "glob": "^11.0.1", "json5": "^2.2.3", "opentype.js": "^1.3.4", "react": "^19.0.0", "svg2ttf": "^6.0.3", "svgicons2svgfont": "^15.0.1", "svgpath": "^2.6.0", "svgs2fonts": "^2.0.3-beta01", "tauri-plugin-shellx-api": "^2.0.16", "three": "^0.174.0", "xml-formatter": "^3.6.4", "xml2js": "^0.6.2", "xmldom": "^0.6.0"}, "devDependencies": {"@tauri-apps/cli": "^2.5.0", "@types/node": "^22.13.5", "@types/opentype.js": "^1.3.8", "@types/three": "^0.174.0", "@types/xmldom": "^0.1.34", "@typescript-eslint/eslint-plugin": "^8.25.0", "@typescript-eslint/parser": "^8.25.0", "eslint": "^9.21.0", "typescript": "^5.2.2", "vite": "^6.2.0"}}